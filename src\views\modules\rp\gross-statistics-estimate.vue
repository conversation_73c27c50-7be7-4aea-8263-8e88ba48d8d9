<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="getStatisticsDataList()" label-width="100px">
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <three-no-input ref="threeNoInput"
                              :deliveryNo.sync="dataForm.deliveryNos"
                              :waybillNo.sync="dataForm.waybillNos"
                              :customerOrderNo.sync="dataForm.customerOrderNos"
                              :hideItem="[4,5,6,7,12]" :autosize="{ minRows: 4, maxRows: 4}" :noSize="1000" />
            </el-col>
            <el-col>
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item :label="$t('wsComWaybill.salesmanId')" prop="salesmanId">
                    <el-select v-model="dataForm.salesmanId" filterable clearable>
                      <el-option v-for="item in userList" :key="item.id" :label="item.realName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="14">
                  <el-form-item :label="$t('rpGrossStatistics.customerName')" prop="customerId">
                    <el-select v-model="dataForm.customerId" clearable :placeholder="$t('twoCharToSelectForCustomer')"
                               :loading="loading"   filterable  remote reserve-keyword :remote-method="getCustomerByCodeOrName">
                      <el-option v-for="(item, index) in customerList" :key="index" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item :label="$t('wsComWaybill.warehouseId')" prop="warehouseId">
                    <el-select v-model="dataForm.warehouseId" filterable clearable>
                      <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="14">
                  <el-form-item :label="$t('rpGrossStatistics.optDate')" prop="optDate">
                    <el-date-picker class="w-percent-100"
                                    v-model="optDateArr"
                                    :clearable="false"
                                    type="datetimerange"
                                    :picker-options="pickerOptions"
                                    popper-class="hideDateClean"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    :start-placeholder="$t('datePicker.start')"
                                    :end-placeholder="$t('datePicker.end')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item :label="$t('wsComWaybill.logisticsProductCode')" prop="logisticsProductCode">
                    <el-select filterable v-model="dataForm.logisticsProductCode"
                               :placeholder="$t('baBillsReceivable.logisticsProductCode')" clearable>
                      <el-option v-for="(item, index) in logisticsProductByParamsList" :key="index" :label="item.name"
                                 :value="item.code"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="14">
                  <el-form-item :label="$t('rpGrossStatistics.logisticsChannelCode')" prop="logisticsChannelCode">
                    <el-select filterable v-model="dataForm.logisticsChannelCode"
                               :placeholder="$t('rpGrossStatistics.logisticsChannelCode')" clearable>
                      <el-option v-for="(item, index) in logisticsChannelByParamsList" :key="index" :label="item.name"
                                 :value="item.code"/>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="getStatisticsDataList()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="22">
            <div>
              {{ $t('rpGrossStatistics.currency') }}:&nbsp;<b>{{ sumData.currency }}</b>
            <el-divider direction="vertical"></el-divider>
              {{ $t('rpGrossStatistics.receivableSum') }}:&nbsp;<b>{{ sumData.receivableSumD.toFixed(2) }}</b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('rpGrossStatistics.payableSum') }}:&nbsp;<b>{{ sumData.payableSumD.toFixed(2) }}</b>
            <el-divider direction="vertical"></el-divider>
              {{ $t('rpGrossStatistics.grossprofitSum') }}:&nbsp;<b v-if="sumData.grossprofitSumD>0" style="color:blue">{{ sumData.grossprofitSumD.toFixed(2) }}</b><b v-else style="color:red">{{ sumData.grossprofitSumD.toFixed(2) }}</b>
            </div>
            <!--保留空格符-->
          </el-col>
          <el-col :md="2">
            <el-button :loading="expButtonLoading" size="mini" type="primary" plain v-if="$hasPermission('rp:grossstatistics:export')" @click="exportByMsgHandle()">导出</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" :header-align="item.align" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'mawbMappingListNo'">
                    <div v-for="(item, index) in scope.row.mawbMappingList" :key="index" >
                      <span>{{ item.mawbNo }}</span>
                    </div>
                  </div>
                  <div v-else-if="item.prop === 'mawbMappingListTime'">
                    <div v-for="(item, index) in scope.row.mawbMappingList" :key="index" >
                      <span>{{ item.departureTime }}</span>
                    </div>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName, formatterName, formatterUserName, formatterShowName, numberFormat } from '@/filters/filters'
import api from '@/api'
import baseData from '@/api/baseData'
import threeNoInput from '@/components/three-no-input'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './gross-statistics-add-or-update'
import ViewDetail from './gross-statistics-view-detail'
import comMixins from '@/mixins/comMixins'
const getBeforeDate = (date) => {
  return new Date(getNowDate() - 3600 * 1000 * 24 * date + 1)
}
const getNowDate = () => {
  return new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
}
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '120', prop: 'customerName', label: this.$t('rpGrossStatistics.customerName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'optDate', label: this.$t('rpGrossStatistics.optDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'waybillNo', label: this.$t('rpGrossStatistics.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'deliveryNo', label: this.$t('rpGrossStatistics.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'customerOrderNo', label: this.$t('rpGrossStatistics.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '50', prop: 'packageQty', label: this.$t('rpGrossStatistics.packageQty'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'balanceWeightD', label: this.$t('rpGrossStatistics.balanceWeight'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'volumeD', label: this.$t('rpGrossStatistics.volume'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '180', prop: 'inRemark', label: this.$t('rpGrossStatistics.inRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'receivableSumD', label: this.$t('rpGrossStatistics.receivableSum'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'payableSumD', label: this.$t('rpGrossStatistics.payableSum'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'grossprofitSumD', label: this.$t('rpGrossStatistics.grossprofitSum'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '60', prop: 'currency', label: this.$t('rpGrossStatistics.currency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsProductCode', label: this.$t('rpGrossStatistics.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsChannelCode', label: this.$t('rpGrossStatistics.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'warehouseId', label: this.$t('wsComWaybill.warehouseId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'salesmanId', label: this.$t('wsComWaybill.salesmanId'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/rp/grossstatistics/estimatePage',
        getDataListIsPage: true,
        exportURL: '/rp/grossstatistics/estimateExport'
      },
      dataForm: {
        id: '',
        waybillNos: '',
        deliveryNos: '',
        customerOrderNos: '',
        departureStartTime: null,
        departureEndTime: null,
        mawbNo: '',
        optBeginDate: timestampFormat(getBeforeDate(60)),
        optEndDate: timestampFormat(getNowDate())
      },
      sumData: {
        receivableSumD: '',
        payableSumD: '',
        grossprofitSumD: '',
        currency: ''
      },
      optDateArr: [timestampFormat(getBeforeDate(60)), timestampFormat(getNowDate())],
      settlementObjectTypeList: [],
      customerEnableList: [],
      logisticsProductByParamsList: [],
      logisticsChannelByParamsList: [],
      activeName: 'all',
      tableName: 'rp-grossstatistics-estimate',
      channelTypeList: [],
      customerList: [],
      totalSumList: [],
      warehouseList: [],
      userList: [],
      loading: false,
      departureTimeArr: [],
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (!minDate) {
            this.selectDate = ''
          } else {
            this.selectDate = minDate.getTime()
            if (maxDate) {
              this.selectDate = ''
            }
          }
        },
        disabledDate: (time) => {
          if (this.selectDate !== '') {
            const one = 30 * 24 * 3600 * 1000
            const minTime = this.selectDate - one
            const maxTime = this.selectDate + one
            return time.getTime() < minTime || time.getTime() > maxTime
          }
        }
      }
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
    this.getTotalSumList()
  },
  methods: {
    getStatisticsDataList () {
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        this.page = 1
        this.getDataList()
        this.getTotalSumList()
      }
    },
    // 获取信息
    getTotalSumList () {
      this.dataListLoading = true
      this.$http.get('/rp/grossstatistics/estimateTotalSum',
        {
          params: {
            ...this.dataForm
          }
        })
        .then(({ data: res }) => {
          this.dataListLoading = false
          if (res.code !== 0) {
            this.sumData = { }
            return this.$message.error(res.msg)
          }
          this.sumData = { ...res.data }
        }).catch(() => {
          this.dataListLoading = false
        })
    },
    // 重置表单
    resetForm (searchForm) {
      this.optDateArr = [timestampFormat(getBeforeDate(30)), timestampFormat(getNowDate())]
      this.departureTimeArr = []
      this.$refs.threeNoInput.clearValue()
      this._resetForm(searchForm)
    },
    getBaseData () {
      // 所有物流产品 包含未启用
      baseData(api.logisticsProductByParamsList).then(res => {
        this.logisticsProductByParamsList = res
      })
      baseData(api.logisticsChannelByParamsList).then(res => {
        this.logisticsChannelByParamsList = res
      })
      // 币种
      baseData(api.currencyList).then(res => {
        this.currencyList = res
      })
      // 渠道类型
      baseData(api.channelTypeList).then(res => {
        this.channelTypeList = res
      })
      // 仓库信息
      baseData(api.warehouseInfoList).then(res => {
        this.warehouseList = res
      })
      // 用户信息
      baseData(api.userList).then(res => {
        this.userList = res
      })
    },
    changeObjectId () {
      let objectType = this.dataForm.customerType
      this.dataForm.customerId = ''
      this.settlementObjectIdList = []
      if (objectType !== undefined && objectType !== '' && objectType !== null) {
        if (objectType === 0) {
          this.settlementObjectIdList = this.customerEnableList
        }
      }
    },
    getDict () {
      // 获取相关字典
      let userType = this.$store.state.user.userType
      if (userType === 0) {
        this.getDictTypeList('receivableSettlementObjectType').then(res => {
          this.settlementObjectTypeList = res
        })
      }
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'customerType':
          value = formatterType(scope.row.customerType, this.settlementObjectTypeList)
          break
        case 'logisticsProductCode':
          value = formatterCodeName(scope.row.logisticsProductCode, this.logisticsProductByParamsList)
          break
        case 'logisticsProductType':
          value = formatterName(scope.row.logisticsProductType, this.channelTypeList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeName(scope.row.logisticsChannelCode, this.logisticsChannelByParamsList)
          break
        case 'logisticsChannelType':
          value = formatterName(scope.row.logisticsChannelType, this.channelTypeList)
          break
        case 'receivableCurrency':
          value = formatterCodeName(scope.row.receivableCurrency, this.currencyList)
          break
        case 'payableCurrency':
          value = formatterCodeName(scope.row.payableCurrency, this.currencyList)
          break
        case 'costCurrency':
          value = formatterCodeName(scope.row.costCurrency, this.currencyList)
          break
        case 'receivableSumD':
          value = scope.row.receivableSumD.toFixed(2)
          break
        case 'payableSumD':
          value = scope.row.payableSumD.toFixed(2)
          break
        case 'weightD':
          value = scope.row.weightD.toFixed(3)
          break
        case 'balanceWeightD':
          value = scope.row.balanceWeightD.toFixed(3)
          break
        case 'volumeD':
          value = scope.row.volumeD.toFixed(3)
          break
        case 'warehouseId':
          value = formatterShowName(scope.row.warehouseId, this.warehouseList, 'warehouseName')
          break
        case 'salesmanId':
          value = formatterUserName(scope.row.salesmanId, this.userList)
          break
        case 'costFeeD':
          value = numberFormat(scope.row.costFeeD, 3)
          break
        case 'grossprofitSumD':
          value = scope.row.grossprofitSumD.toFixed(2)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        if (this.$store.state.user.userType === 1 && item.prop === 'logisticsChannelCode') {
          return false
        }
        if (this.$store.state.user.userType === 1 && item.prop === 'logisticsChannelType') {
          return false
        }
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet,
    threeNoInput
  },
  watch: {
    departureTimeArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.departureStartTime = newVal[0]
          this.dataForm.departureEndTime = newVal[1]
          return
        }
        this.dataForm.departureStartTime = this.dataForm.departureEndTime = ''
      },
      deep: true
    },
    optDateArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.optBeginDate = newVal[0]
          this.dataForm.optEndDate = newVal[1]
          return
        }
        this.dataForm.optBeginDate = this.dataForm.optEndDate = ''
      },
      deep: true
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName,
    formatterUserName,
    formatterShowName,
    numberFormat
  }
}
</script>
