<template>
  <el-dialog :visible.sync="visible" :title="$t('fba.inputReference')" :close-on-click-modal="false" width='1100px' :close-on-press-escape="false" :show-close='false' :lock-scroll="true" class="location_model" >
    <div style='border-top: 2px dashed #DCDFE6;margin-top: 10px'>
      <div class="flex_table" ref="tableElm" v-domResize="redraw">
        <el-form :model='dataForm' ref='dataForm'  key='6' :inline-message='true'>
          <el-table :data='dataForm.referenceList' border :max-height="tableHeight"  class='width100 margin_top10' >
            <el-table-column prop='fbaNo' :label="$t('coOrderFbaReference.fbaNo')" min-width='200' header-align='center' align='center'  :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'referenceList.' + scope.$index + '.fbaNo'" :rules='dataRule.fbaNo'>
                  <el-input v-model="scope.row.fbaNo"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='referenceId' :label="$t('coOrderFbaReference.referenceId')" min-width='200' header-align='center' align='center'  :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'referenceList.' + scope.$index + '.referenceId'" :rules='dataRule.referenceId'>
                  <el-input v-model="scope.row.referenceId"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageQty' :label="$t('coOrderFbaReference.packageQty')" min-width='100' header-align='center' align='center' :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'referenceList.' + scope.$index + '.packageQty'" :rules='dataRule.packageQty'>
                  <el-input  v-model="scope.row.packageQty"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')"  min-width='60' header-align='center' align='center' >
              <template slot='header' slot-scope='scope'>
                <span>{{ $t('handle') }}</span>
              </template>
              <template slot-scope='scope'>
                <popconfirm i18nOperateValue="delete" @clickHandle='deleteRow(scope.$index)'>{{ $t('delete') }}</popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <el-button class='el-icon-plus width100 margin_top10' size='mini' type='primary' plain @click='addRow'>{{ $t('add') }}</el-button>
        </el-form>
      </div>
    </div>
    <template slot="footer">
      <div style='justify-content: center;display: flex;'>
        <el-button @click='closeHandle'>{{ $t('close') }}</el-button>
        <el-button type='primary' :loading='submitLoading' @click='handleSubmit()'>{{ $t('save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import areaBox from '@/components/areaBox'
import listPage from '@/mixins/listPage'
import { isOverLength, isPlusInteger2 } from '@/utils/validate'
export default {
  mixins: [listPage],
  data () {
    return {
      visible: false,
      autoAppend: false,
      autoGenerateBox: false,
      submitLoading: false,
      customerOrderNo: null,
      dataForm: {
        orderId: '',
        fbaNo: '',
        referenceId: '',
        packageQty: '',
        referenceList: []
      }
    }
  },
  computed: {
    dataRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的整数'))
        }
        callback()
      }
      const repeatFbaNo = (rule, value, callback) => {
        if (value && this.dataForm.referenceList && this.dataForm.referenceList.length > 0) {
          let fbaNoArr = this.dataForm.referenceList.map(item => {
            if (item.fbaNo === value) {
              return item.fbaNo
            }
          }).filter(item => typeof item !== 'undefined')
          let fbaNoSet = new Set(this.dataForm.referenceList.map(item => {
            if (item.fbaNo === value) {
              return item.fbaNo
            }
          }).filter(item => typeof item !== 'undefined')
          )
          if (fbaNoSet.size !== fbaNoArr.length) {
            return callback(new Error('FBA编号不能重复'))
          }
        }
        callback()
      }
      const repeatReferenceId = (rule, value, callback) => {
        if (value && this.dataForm.referenceList && this.dataForm.referenceList.length > 0) {
          let referenceIdArr = this.dataForm.referenceList.map(item => {
            if (item.referenceId === value) {
              return item.referenceId
            }
          }).filter(item => typeof item !== 'undefined')
          let referenceIdSet = new Set(this.dataForm.referenceList.map(item => {
            if (item.referenceId === value) {
              return item.referenceId
            }
          }).filter(item => typeof item !== 'undefined')
          )
          if (referenceIdSet.size !== referenceIdArr.length) {
            return callback(new Error('Reference ID不能重复'))
          }
        }
        callback()
      }
      return {
        fbaNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' },
          { validator: repeatFbaNo, trigger: 'blur' }
        ],
        referenceId: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' },
          { validator: repeatReferenceId, trigger: 'blur' }
        ],
        packageQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isInteger, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (referenceList) {
      this.visible = true
      this.submitLoading = false
      if (referenceList && referenceList.length > 0) {
        this.dataForm.referenceList = referenceList
      }
      this.$nextTick(() => {
        if (this.dataForm.referenceList.length === 0) {
          let obj = {
            fbaNo: '',
            referenceId: '',
            packageQty: ''
          }
          this.dataForm.referenceList.push(obj)
        }
      })
    },
    addRow () {
      let obj = {
        fbaNo: '',
        referenceId: '',
        packageQty: ''
      }
      this.dataForm.referenceList.push(obj)
    },
    deleteRow (index) {
      this.dataForm.referenceList.splice(index, 1)
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    handleSubmit: debounce(function () {
      this.submitLoading = true
      this.$refs.dataForm.validate((valid) => {
        if (!valid) {
          this.$message({
            message: '请正确填写需要Reference的信息',
            type: 'warning',
            duration: 2000,
            onClose: () => {
              this.submitLoading = false
            }
          })
          return false
        }
        this.$emit('inputReferenceEmit', this.dataForm.referenceList)
        this.closeHandle()
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    closeHandle () {
      this.visible = false
      this.$emit('closeReferenceInfo')
    }
  },
  components: {
    areaBox
  }
}
</script>

<style lang='scss' scoped>
#areabox ::v-deep .el-form-item {
  padding: 0px 15px !important;
}
#boxDataFormId{
  ::v-deep .el-form-item {
    margin-bottom: 0!important;
  }
}
</style>
