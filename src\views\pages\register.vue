<template>
  <div class="modern-register-wrapper">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <div class="register-container">
      <!-- 头部品牌区域 -->
      <div class="brand-header">
        <div class="brand-logo">
          <div class="logo-icon">
            <i class="el-icon-truck"></i>
          </div>
          <h1 class="brand-title">货代好帮手</h1>
          <p class="brand-subtitle">专业的国际物流管理平台</p>
        </div>
      </div>

      <!-- 注册成功页面 -->
      <div v-if="isSaved" class="success-container">
        <div class="success-content">
          <div class="success-icon">
            <i class="el-icon-circle-check"></i>
          </div>
          <h2 class="success-title">注册成功！</h2>
          <div class="success-message">
            <p>恭喜您已<span class="highlight-success">成功注册</span>，我们的客服会尽快联系您并审核。</p>
            <p>审核通过后您将获取<span class="highlight-trial">7天</span>的免费试用时间。</p>
            <p class="email-notice">请留意您的邮箱 <span class="email-address">{{dataForm.email}}</span></p>
            <p>注册结果将会以邮件的形式通知您。</p>
          </div>
          <div class="success-actions">
            <el-button type="primary" size="large" @click="goToLogin">
              <i class="el-icon-right"></i>
              前往登录
            </el-button>
          </div>
        </div>
      </div>

      <!-- 注册表单 -->
      <div v-else class="form-container">
        <div class="form-header">
          <h2 class="form-title">公司注册</h2>
          <p class="form-subtitle">开启您的数字化物流管理之旅</p>
        </div>
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          @keyup.enter.native="dataFormSubmitHandle()"
          class="modern-form"
          label-position="top">

          <!-- 公司名称 -->
          <div class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.name')" prop="name">
              <el-input
                ref="username"
                v-model="dataForm.name"
                placeholder="请输入公司名称"
                size="large"
                class="modern-input">
                <i slot="prefix" class="el-icon-office-building"></i>
              </el-input>
            </el-form-item>
          </div>

          <!-- 手机号码 -->
          <div class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.phone')" prop="phone">
              <el-input
                ref="phone"
                v-model="dataForm.phone"
                :placeholder="$t('saasCompanyRegister.phone')"
                size="large"
                class="modern-input">
                <i slot="prefix" class="el-icon-phone"></i>
              </el-input>
            </el-form-item>
          </div>

          <!-- 验证码 -->
          <div class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.verificationCode')" prop="verificationCode">
              <div class="verification-input-group">
                <el-input
                  ref="verificationCode"
                  v-model="dataForm.verificationCode"
                  placeholder="请输入六位短信验证码"
                  size="large"
                  class="modern-input verification-input">
                  <i slot="prefix" class="el-icon-message"></i>
                </el-input>
                <el-button
                  :disabled="vCodeBtn.disable"
                  :loading="vCodeBtn.loading"
                  @click="generateVCodeHandler"
                  class="verification-btn"
                  size="large">
                  {{ vCodeBtn.text }}
                </el-button>
              </div>
            </el-form-item>
          </div>

          <!-- 邮箱 -->
          <div class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.email')" prop="email">
              <el-input
                ref="email"
                v-model="dataForm.email"
                :placeholder="$t('saasCompanyRegister.emailPlaceHolder')"
                size="large"
                class="modern-input">
                <i slot="prefix" class="el-icon-message"></i>
              </el-input>
            </el-form-item>
          </div>

          <!-- 物流类型 -->
          <div class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.logisticsTypeList')" prop="logisticsTypeList">
              <div class="logistics-types">
                <el-checkbox-group v-model="dataForm.logisticsTypeList" class="modern-checkbox-group">
                  <el-checkbox
                    v-for="(item, index) in logisticsTypeList"
                    :key="index"
                    :label="item.dictValue"
                    class="modern-checkbox">
                    <span class="checkbox-label">{{item.dictName}}</span>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </div>

          <!-- 销售员（条件显示） -->
          <div v-show="salesmanVisiable" class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.salesman')" prop="salesman">
              <el-input
                ref="salesman"
                readonly
                v-model="dataForm.salesman"
                :placeholder="$t('saasCompanyRegister.salesmanPlaceHolder')"
                size="large"
                class="modern-input">
                <i slot="prefix" class="el-icon-user"></i>
              </el-input>
            </el-form-item>
          </div>

          <!-- 访问码（条件显示） -->
          <div v-show="visitCodeVisiable" class="form-group">
            <el-form-item :label="$t('saasCompanyRegister.visitCode')" prop="visitCode">
              <el-input
                ref="visitCode"
                readonly
                v-model="dataForm.visitCode"
                :placeholder="$t('saasCompanyRegister.visitCode')"
                size="large"
                class="modern-input">
                <i slot="prefix" class="el-icon-key"></i>
              </el-input>
            </el-form-item>
          </div>

          <!-- 隐私协议 -->
          <div class="form-group">
            <el-form-item prop="privacy" class="privacy-item">
              <div class="privacy-agreement">
                <el-checkbox v-model="privacy" class="privacy-checkbox">
                  <span class="agreement-text">
                    我已阅读并同意
                    <el-link type="primary" @click.native="openDialog(urls[0])" class="agreement-link">
                      《软件服务及许可协议》
                    </el-link>
                    和
                    <el-link type="primary" @click.native="openDialog(urls[1])" class="agreement-link">
                      《隐私政策》
                    </el-link>
                  </span>
                </el-checkbox>
              </div>
            </el-form-item>
          </div>

          <!-- 提交按钮 -->
          <div class="form-group submit-group">
            <el-button
              :disabled="!privacy"
              type="primary"
              @click="dataFormSubmitHandle()"
              :loading="saveLoading"
              class="submit-btn"
              size="large">
              <i class="el-icon-check" v-if="!saveLoading"></i>
              {{ saveLoading ? '注册中...' : '立即注册' }}
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 页脚 -->
      <div class="footer">
        <p class="copyright">
          Copyright &copy; 2016 - 深圳市互联通科技有限公司 版权所有
          <br class="mobile-break">
          备案号：粤ICP备16007275号-1
        </p>
      </div>
    </div>
    <!-- 协议弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      :fullscreen="isMobile"
      :width="isMobile ? '100%' : '80%'"
      :before-close="handleClose"
      class="agreement-dialog">
      <div slot="title" class="dialog-title">
        <i class="el-icon-document"></i>
        {{ dialogTitle }}
      </div>
      <iframe
        :src="url"
        frameborder="0"
        scrolling="auto"
        width="100%"
        :height="isMobile ? '70vh' : '600px'"
        class="agreement-iframe">
      </iframe>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false" size="large">
          <i class="el-icon-check"></i>
          我已阅读
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { messages } from '@/i18n'
import { isEmail } from '@/utils/validate'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      i18nMessages: messages,
      dialogVisible: false,
      dialogTitle: '',
      isSaved: false,
      saveLoading: false,
      isMobile: false,
      dataForm: {
        id: '',
        comId: '',
        code: '',
        name: '',
        contact: '',
        phone: '',
        verificationCode: '',
        qq: '',
        weixin: '',
        email: '',
        logisticsType: '',
        salesman: '',
        waiter: '',
        country: 'CN',
        province: '',
        city: '',
        district: '',
        street: '',
        postcode: '',
        visitCode: '',
        status: '',
        logisticsTypeList: ['12']
      },
      privacy: false,
      urls: [
        'http://www.goto56.com/AUP.html',
        'http://www.goto56.com/privacy.html'
      ],
      url: '',
      logisticsTypeChecks: '',
      logisticsTypeList: [
        { 'dictValue': '10', 'dictName': '专线小包' },
        { 'dictValue': '11', 'dictName': '国际快递' },
        { 'dictValue': '12', 'dictName': 'FBA空海运' }
      ],
      params: {},
      loading: false,
      companyNameOptions: [],
      vCodeBtn: {
        disable: false,
        loading: false,
        text: '获取验证码'
      },
      vcodeExpireCallId: '',
      remainSeconds: 600,
      salesmanVisiable: false,
      visitCodeVisiable: false
    }
  },
  computed: {
    dataRule () {
      const isEmailValidtor = (rule, value, callback) => {
        if (!isEmail(this.dataForm.email)) {
          return callback(new Error(this.$t('saasCompanyRegister.illegalEmail')))
        }
        callback()
      }
      return {
        logisticsTypeList: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        verificationCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        email: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isEmailValidtor, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.params = this.$route.query
    this.dataForm = { ...this.dataForm, ...this.params }
    if (this.params.salesman) {
      this.salesmanVisiable = true
    }
    if (this.params.visitCode) {
      this.visitCodeVisiable = true
    }
    this.checkMobile()
  },
  mounted () {
    window.addEventListener('resize', this.checkMobile)
  },
  beforeDestroy () {
    if (this.vcodeExpireCallId !== '') {
      clearInterval(this.vcodeExpireCallId)
    }
    window.removeEventListener('resize', this.checkMobile)
  },
  methods: {
    // 检查是否为移动端
    checkMobile () {
      this.isMobile = window.innerWidth <= 768
    },
    // 前往登录页面
    goToLogin () {
      this.$router.push('/login')
    },
    queryCompanyName (query) {
      this.companyNameOptions = []
      query = query.trim() || ''
      if (query !== '' && query.length > 2) {
        this.loading = true
        this.$http.get(`/saas/companyregister/queryCompanys/${query}`).then(({ data: res }) => {
          if (res.code === 0) {
            this.companyNameOptions = res.data
          }
        }).catch(() => {
        }).finally(() => {
          this.loading = false
        })
      }
    },
    openDialog (url) {
      this.dialogVisible = true
      this.url = url
      // 设置弹窗标题
      if (url.includes('AUP.html')) {
        this.dialogTitle = '软件服务及许可协议'
      } else if (url.includes('privacy.html')) {
        this.dialogTitle = '隐私政策'
      }
    },
    handleClose () {
      this.dialogVisible = false
    },
    // 禁用获取验证码按钮
    setVCodeDisable () {
      if (this.remainSeconds > 0) {
        this.vCodeBtn = {
          disable: true,
          loading: false,
          text: `${this.remainSeconds--}秒后重新获取`
        }
      } else {
        if (this.vcodeExpireCallId !== '') {
          clearInterval(this.vcodeExpireCallId)
        }
        this.remainSeconds = 60 // 改为60秒倒计时
        this.vCodeBtn = {
          disable: false,
          loading: false,
          text: '获取验证码'
        }
      }
    },
    // 生成短信验证码
    generateVCodeHandler () {
      if (!this.dataForm.phone) {
        return this.$message.error('请先输入手机号码')
      }

      this.vCodeBtn.loading = true

      this.$http.get(`/saas/companyregister/generateVCode/${this.dataForm.phone}`).then(({ data: res }) => {
        if (res.code !== 0) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('验证码已发送，请注意查收')
        // 开始倒计时
        this.remainSeconds = 60
        this.vcodeExpireCallId = setInterval(this.setVCodeDisable, 1000)
      }).catch(() => {
        this.$message.error('验证码发送失败，请稍后重试')
      }).finally(() => {
        this.vCodeBtn.loading = false
      })
    },
    validatedPassword (firstInput, secondInput) {
      if (firstInput === secondInput) {
        this.dataForm.newPassword = firstInput
        return true
      }
      return false
    },
    cancalReadOnly (onOff) {
      this.$nextTick(() => {
        if (!onOff) {
          const Selects = this.$refs
          // 如果只有1个下拉框，这段就足够了
          if (Selects.nameSelect) {
            const input = Selects.nameSelect.$el.querySelector('.el-input__inner')
            input.removeAttribute('readonly')
          }
        }
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      // 聚焦点
      // if (!this.dataForm.passwordSecond) {
      //   this.$refs.passwordSecond.focus()
      //   return
      // } else if (!this.dataForm.passwordFirst) {
      //   this.$refs.passwordFirst.focus()
      //   return
      // }

      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.saveLoading = true
        this.$http.post('/saas/companyregister/save', this.dataForm).then(({ data: res }) => {
          this.id = ''
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          // 显示注册成功信息
          this.isSaved = true
        }).catch(() => {
        }).finally(() => {
          this.saveLoading = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style lang="scss" scoped>
// 现代化注册页面样式
.modern-register-wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

// 背景装饰
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

// 主容器
.register-container {
  width: 100%;
  max-width: 480px;
  position: relative;
  z-index: 2;
}

// 品牌头部
.brand-header {
  text-align: center;
  margin-bottom: 40px;

  .brand-logo {
    .logo-icon {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.3);

      i {
        font-size: 36px;
        color: #fff;
      }
    }

    .brand-title {
      font-size: 32px;
      font-weight: 700;
      color: #fff;
      margin: 0 0 8px 0;
      letter-spacing: 2px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .brand-subtitle {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
      font-weight: 300;
    }
  }
}

// 注册成功页面
.success-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);

  .success-icon {
    margin-bottom: 30px;

    i {
      font-size: 80px;
      color: #67C23A;
      animation: successPulse 2s ease-in-out infinite;
    }
  }

  .success-title {
    font-size: 28px;
    color: #303133;
    margin-bottom: 30px;
    font-weight: 600;
  }

  .success-message {
    margin-bottom: 40px;

    p {
      font-size: 16px;
      color: #606266;
      line-height: 1.6;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .highlight-success {
      color: #67C23A;
      font-weight: 600;
    }

    .highlight-trial {
      color: #E6A23C;
      font-weight: 600;
      font-size: 18px;
    }

    .email-notice {
      margin-top: 20px;
      padding: 15px;
      background: #f0f9ff;
      border-radius: 8px;
      border-left: 4px solid #409EFF;
    }

    .email-address {
      color: #409EFF;
      font-weight: 600;
    }
  }

  .success-actions {
    .el-button {
      padding: 15px 40px;
      font-size: 16px;
      border-radius: 25px;
      font-weight: 600;
    }
  }
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 表单容器
.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);

  .form-header {
    text-align: center;
    margin-bottom: 40px;

    .form-title {
      font-size: 28px;
      color: #303133;
      margin-bottom: 8px;
      font-weight: 600;
    }

    .form-subtitle {
      font-size: 16px;
      color: #909399;
      margin: 0;
    }
  }
}

// 现代化表单样式
.modern-form {
  .form-group {
    margin-bottom: 25px;

    &.submit-group {
      margin-top: 40px;
      margin-bottom: 0;
    }
  }

  // 表单项标签
  ::v-deep .el-form-item__label {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
    line-height: 1.4;
  }

  // 现代化输入框
  .modern-input {
    ::v-deep .el-input__inner {
      height: 50px;
      border-radius: 12px;
      border: 2px solid #E4E7ED;
      font-size: 16px;
      padding-left: 45px;
      transition: all 0.3s ease;
      background: #fff;

      &:focus {
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }

      &::placeholder {
        color: #C0C4CC;
        font-size: 14px;
      }
    }

    ::v-deep .el-input__prefix {
      left: 15px;

      .el-input__icon {
        font-size: 18px;
        color: #909399;
        line-height: 50px;
      }
    }
  }

  // 验证码输入组
  .verification-input-group {
    display: flex;
    gap: 12px;

    .verification-input {
      flex: 1;
    }

    .verification-btn {
      min-width: 140px;
      height: 50px;
      border-radius: 12px;
      font-weight: 600;
      white-space: nowrap;

      &:not(.is-disabled) {
        background: linear-gradient(135deg, #409EFF, #36A3F7);
        border-color: #409EFF;
        color: #fff;

        &:hover {
          background: linear-gradient(135deg, #36A3F7, #2B8FE8);
        }
      }
    }
  }

  // 物流类型选择
  .logistics-types {
    .modern-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .modern-checkbox {
        ::v-deep .el-checkbox__input {
          .el-checkbox__inner {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid #DCDFE6;

            &:hover {
              border-color: #409EFF;
            }

            &::after {
              width: 6px;
              height: 10px;
              left: 6px;
              top: 2px;
            }
          }

          &.is-checked .el-checkbox__inner {
            background-color: #409EFF;
            border-color: #409EFF;
          }
        }

        .checkbox-label {
          font-size: 16px;
          color: #303133;
          font-weight: 500;
          margin-left: 8px;
        }
      }
    }
  }

  // 隐私协议
  .privacy-item {
    ::v-deep .el-form-item__content {
      line-height: 1.6;
    }

    .privacy-agreement {
      .privacy-checkbox {
        ::v-deep .el-checkbox__input {
          .el-checkbox__inner {
            width: 18px;
            height: 18px;
            border-radius: 4px;
          }
        }

        .agreement-text {
          font-size: 14px;
          color: #606266;
          line-height: 1.6;
          margin-left: 8px;

          .agreement-link {
            font-weight: 600;
            text-decoration: underline;

            &:hover {
              color: #36A3F7;
            }
          }
        }
      }
    }
  }

  // 提交按钮
  .submit-btn {
    width: 100%;
    height: 55px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, #409EFF, #36A3F7);
    border: none;
    letter-spacing: 1px;
    transition: all 0.3s ease;

    &:hover:not(.is-disabled) {
      background: linear-gradient(135deg, #36A3F7, #2B8FE8);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
    }

    &.is-disabled {
      background: #C0C4CC;
      cursor: not-allowed;
    }

    i {
      margin-right: 8px;
    }
  }
}

// 页脚
.footer {
  text-align: center;
  margin-top: 40px;

  .copyright {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;

    .mobile-break {
      display: none;
    }
  }
}

// 协议弹窗
.agreement-dialog {
  ::v-deep .el-dialog__header {
    background: linear-gradient(135deg, #409EFF, #36A3F7);
    color: #fff;
    border-radius: 8px 8px 0 0;

    .dialog-title {
      font-size: 18px;
      font-weight: 600;

      i {
        margin-right: 8px;
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding: 0;
  }

  .agreement-iframe {
    border-radius: 0 0 8px 8px;
  }

  ::v-deep .el-dialog__footer {
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;

    .el-button {
      padding: 12px 30px;
      border-radius: 8px;
      font-weight: 600;

      i {
        margin-right: 6px;
      }
    }
  }
}

// 移动端响应式样式
@media (max-width: 768px) {
  .modern-register-wrapper {
    padding: 15px;
    align-items: flex-start;
    padding-top: 30px;
  }

  .register-container {
    max-width: 100%;
  }

  .brand-header {
    margin-bottom: 30px;

    .brand-logo {
      .logo-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;

        i {
          font-size: 28px;
        }
      }

      .brand-title {
        font-size: 24px;
        letter-spacing: 1px;
      }

      .brand-subtitle {
        font-size: 14px;
      }
    }
  }

  .success-container {
    padding: 40px 25px;
    border-radius: 15px;

    .success-icon i {
      font-size: 60px;
    }

    .success-title {
      font-size: 22px;
    }

    .success-message {
      p {
        font-size: 15px;
      }

      .email-notice {
        padding: 12px;
        margin-top: 15px;
      }
    }

    .success-actions .el-button {
      padding: 12px 30px;
      font-size: 15px;
    }
  }

  .form-container {
    padding: 30px 25px;
    border-radius: 15px;

    .form-header {
      margin-bottom: 30px;

      .form-title {
        font-size: 22px;
      }

      .form-subtitle {
        font-size: 14px;
      }
    }
  }

  .modern-form {
    .form-group {
      margin-bottom: 20px;

      &.submit-group {
        margin-top: 30px;
      }
    }

    .modern-input {
      ::v-deep .el-input__inner {
        height: 48px;
        font-size: 16px; // 防止iOS缩放
        border-radius: 10px;
      }

      ::v-deep .el-input__prefix .el-input__icon {
        line-height: 48px;
      }
    }

    .verification-input-group {
      flex-direction: column;
      gap: 15px;

      .verification-btn {
        width: 100%;
        height: 48px;
        min-width: auto;
      }
    }

    .logistics-types .modern-checkbox-group {
      gap: 12px;

      .modern-checkbox .checkbox-label {
        font-size: 15px;
      }
    }

    .privacy-item .privacy-agreement .privacy-checkbox .agreement-text {
      font-size: 13px;
    }

    .submit-btn {
      height: 50px;
      font-size: 16px;
      border-radius: 10px;
    }
  }

  .footer {
    margin-top: 30px;

    .copyright {
      font-size: 12px;

      .mobile-break {
        display: block;
      }
    }
  }

  // 装饰圆圈在移动端调整
  .decoration-circle {
    &.circle-1 {
      width: 120px;
      height: 120px;
      top: 5%;
      left: 5%;
    }

    &.circle-2 {
      width: 80px;
      height: 80px;
      top: 70%;
      right: 10%;
    }

    &.circle-3 {
      width: 60px;
      height: 60px;
      bottom: 15%;
      left: 15%;
    }
  }
}

// 小屏幕手机适配
@media (max-width: 480px) {
  .modern-register-wrapper {
    padding: 10px;
    padding-top: 20px;
  }

  .form-container,
  .success-container {
    padding: 25px 20px;
  }

  .brand-header .brand-logo {
    .brand-title {
      font-size: 20px;
    }

    .brand-subtitle {
      font-size: 13px;
    }
  }

  .modern-form {
    .verification-input-group {
      gap: 12px;
    }

    .privacy-item .privacy-agreement .privacy-checkbox .agreement-text {
      font-size: 12px;
    }
  }
}

// 横屏适配
@media (max-height: 600px) and (orientation: landscape) {
  .modern-register-wrapper {
    align-items: flex-start;
    padding-top: 20px;
  }

  .brand-header {
    margin-bottom: 20px;

    .brand-logo {
      .logo-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 10px;

        i {
          font-size: 24px;
        }
      }

      .brand-title {
        font-size: 20px;
      }

      .brand-subtitle {
        font-size: 13px;
      }
    }
  }

  .form-container {
    padding: 25px 30px;
  }

  .modern-form .form-group {
    margin-bottom: 15px;

    &.submit-group {
      margin-top: 20px;
    }
  }
}
</style>
