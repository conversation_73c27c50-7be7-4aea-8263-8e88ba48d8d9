<template>
  <div :class="bodyClass">
    <div class="panel-hd">{{$t(title ? title : 'import')}}</div>
    <div class="importExcelPanel" v-show="visible">
      <el-row >
        <el-col :md="{span:10}">
          <el-row style="display: flex;justify-content: center;">
            <div>
              <description label='导入申报信息'>
                <el-radio-group v-model="hasDeclareInfo" style='display: flex' @change='hasDeclareInfoChange'>
                  <el-radio :label="1">导入</el-radio>
                  <el-radio :label="0">不导入</el-radio>
                </el-radio-group>
              </description>
            </div>
          </el-row>
          <el-row v-if="selectCustomer" style="display: flex;justify-content: center;" >
            <div>
              <description :label="$t('baReceivableSettle.settlementObjectName')">
                <el-select style="width: 280px" v-model="customerId" clearable :placeholder="$t('twoCharToSelectForCustomer')"
                           filterable  remote reserve-keyword :remote-method="getCustomerByCodeOrName">
                  <el-option v-for="(item, index) in customerList" :key="index" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </description>
            </div>
          </el-row>
          <el-row v-if="selectTemplate" style="display: flex;justify-content: center;" >
            <div>
              <description :label="$t('selectImportTemplate')">
                <el-select style="width: 280px" v-model="importTemplateType" clearable :placeholder="$t('select')">
                  <el-option v-for="(item, index) in importTemplateTypeList" :key="index" :label="item.name" :value="item.value"></el-option>
                </el-select>
              </description>
            </div>
          </el-row>
          <el-row>
            <el-upload
                :action="getUrl"
                accept=".xls,.xlsx"
                :data="param"
                drag
                :disabled="uploadDisable"
                :before-upload="beforeUploadHandle"
                :on-success="successHandle"
                :on-error="errorHandle"
                :headers="getToken"
                class="text-center">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text" v-html="$t('upload.text')"></div>
                <div class="el-upload__tip" style='display: block' slot="tip" v-show="showTip" v-if="showMsg">{{ showMsg }}</div>
                <div class="el-upload__tip" style='display: block' slot="tip" v-else>{{ $t('upload.tip', { 'format': 'xls、xlsx' }) }}
                  <!-- <a v-if='fileUrl1' :href="fileUrl1" target="_blank">{{ importTemplateTypeList[0].name }}<i class="el-icon-download"></i></a>
                  <a v-if='fileUrl2' :href="fileUrl2" target="_blank" style="padding-left: 20px">{{ importTemplateTypeList[1].name }}<i class="el-icon-download"></i></a>
                  <a v-if='fileUrl3' :href="fileUrl3" target="_blank" style="padding-left: 20px">{{ importTemplateTypeList[2].name }}<i class="el-icon-download"></i></a> -->
                  <!-- <a v-if='amazonUrl' :href="amazonUrl" target="_blank">{{$t('fba.import_amazon_module')}}<i class="el-icon-download"></i></a> -->

                  <p v-for="(item, index) in importTemplateTypeList" :key="index">
                    <el-link :underline="false" class="el-icon-download" @click="downloadFile(item.downloadUrl, item.name)"><span style="margin-left: 5px;">{{ item.name }}</span></el-link>
                  </p>

                </div>
            </el-upload>
          </el-row>
        </el-col>
        <el-col :md="{span:14}">
          <el-card class="box-card" shadow="never" >
            <div slot="header" class="clearfix">
              <span>{{$t('import_log_info')}}</span>
            </div>
            <div style="height:560px; overflow:hidden; overflow-y:auto; position: relative; z-index: 2;" v-loading="uploadDisable" element-loading-text="请勿离开页面，正在读取数据中......" element-loading-spinner="el-icon-loading"
            >
              <div class="info" style="white-space: pre-wrap;" v-html="message" ></div>
              <!-- <el-input type="textarea" disabled v-model="" :autosize="{ minRows: 30, maxRows: 4}" ></el-input> -->
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button :loading="uploadDisable" @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import { saveAs } from 'file-saver'
import Cookies from 'js-cookie'
import { clearLoginInfo } from '@/utils'
export default {
  props: {
    defaultSelect: false,
    importExcelUrl: '',
    downLoadUrl1: '',
    downLoadUrl2: '',
    downLoadUrl3: '',
    downLoadAmazonUrl: '',
    downVerticalUrl: '',
    fileType: {
      // 配置限制上传文件
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否展示提示语
    showTip: {
      type: Boolean,
      default: true
    },
    selectTemplate: {
      type: Boolean,
      default: false
    },
    selectCustomer: {
      type: Boolean,
      default: false
    },
    // 展示提示语内容
    showMsg: {
      type: String,
      default: ''
    },
    bodyClass: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    // 上传文件大小限制
    sizeLimit: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      visible: false,
      uploadDisable: false, // 避免重复上传
      hasDeclareInfo: 1,
      templateList: [],
      url: '',
      templateType: '',
      fileUrl1: '',
      fileUrl2: '',
      fileUrl3: '',
      amazonUrl: '',
      verticalUrl: '',
      message: '',
      token: '',
      lang: '',
      param: {},
      customerId: null,
      importTemplateType: '', /** 下拉列表的值，模版的类型或者模版ID */
      customerList: [],
      importTemplateTypeList: []
    }
  },
  computed: {
    getUrl () {
      let url = this.customerId ? this.url + '&customerId=' + this.customerId : this.url
      console.log('this.importTemplateType', this.importTemplateType)
      if (this.selectTemplate && this.importTemplateType) {
        let currentTemplate = this.templateList.filter(item => item.value === this.importTemplateType)[0]
        if (currentTemplate.id) {
          url += '&templateType=' + currentTemplate.type + '&templateId=' + currentTemplate.id
        } else {
          url += '&templateType=' + currentTemplate.value
        }
      }
      console.log('url', url)
      return url
    },
    getToken () {
      return {
        token: this.token,
        'Accept-Language': this.lang
      }
    }
  },
  methods: {
    init (param) {
      this.visible = true
      // console.log(this.showMsg)
      this.token = Cookies.get('sys_token')
      this.url = this.importExcelUrl
      this.lang = Cookies.get('language') || 'zh-CN'
      if (param) {
        this.param = param
      } else {
        this.param = null
      }
      console.log(this.importTemplateTypeList)
    },
    importTemplateTypeListHandle () {
      if (this.templateList) {
        this.importTemplateTypeList = this.templateList
        if (this.defaultSelect && this.templateList.length > 0) {
          this.importTemplateType = this.templateList[0].value
        }
      } else {
        this.importTemplateTypeList = []
      }
    },
    hasDeclareInfoChange () {
      this.$emit('hasDeclareInfoChange', this.hasDeclareInfo)
    },
    async downloadFile (downloadUrl, fileName) {
      let url = this.getDownloadUrl(downloadUrl)
      const response = await fetch(url)
      const blob = await response.blob()
      saveAs(blob, fileName)
    },
    getDownloadUrl (downloadUrl) {
      if (downloadUrl.indexOf('http://') === 0 || downloadUrl.indexOf('https://') === 0) {
        return downloadUrl
      }
      return `static/${downloadUrl}`
    },
    getCustomerByCodeOrName (query) {
      if (query !== '' && query.length >= 2) {
        this.loading = true
        this.$http.get(`/bd/customer/listByCodeOrName/` + query).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.customerList = res.data
          this.loading = false
        }).catch(() => { })
      } else {
        this.customerList = []
      }
    },
    // 上传之前
    beforeUploadHandle (file) {
      this.uploadDisable = true
      this.message = ''
      let regStr = ''
      let formatTip = ''
      if (this.selectCustomer && !this.customerId && this.importTemplateType !== 7) {
        this.uploadDisable = false
        this.$message.error('请选择客户')
        return false
      }
      if (this.selectTemplate && !this.importTemplateType) {
        this.uploadDisable = false
        this.$message.error('请选择导入模板类型')
        return false
      }
      if (this.fileType.length) {
        let fileTypeStr = []
        this.fileType.forEach((item, index) => {
          formatTip += `${index > 0 ? '、' : ''}${item}`
          fileTypeStr.push(`(.${item})$`)
        })
        fileTypeStr = fileTypeStr.join('|')
        regStr = fileTypeStr
      } else {
        regStr = '(.xlsx)$|(.xls)$'
        formatTip = '.xls、.xlsx'
      }
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { 'format': `${formatTip}` }))
        this.uploadDisable = false
        return false
      }
      // 文件名只能中文，字母，数字，中划线和下划线
      // if (!(/^[0-9a-zA-Z._\-\u4E00-\u9FA5\uF900-\uFA2D]+$/.test(file.name))) {
      //   this.$message.error(this.$t('upload.fileNameFormat'))
      //   this.uploadDisable = false
      //   return false
      // }
      // if (file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      //   this.$message.error(this.$t('upload.tip', { 'format': 'xls、xlsx' }))
      //   return false
      // }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        return false // 必须返回false
      }
    },
    errorHandle (error) {
      console.log('error', error)
      this.uploadDisable = false
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    // 上传成功
    successHandle (res, file) {
      // console.log('res', res)
      if (res.code !== 0) {
        this.uploadDisable = false
        this.message = res.msg && res.msg.replace(/<br>/g, '\r\n')
        if (this.message === '未授权') {
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        }
        this.$message({
          message: '发生错误，请看导入日志进行检查',
          type: 'error',
          duration: 1500
        })
      } else {
        this.message = this.$t('prompt.success')
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.uploadDisable = false
            this.$emit('refreshDataList', res.data)
          }
        })
      }
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backImportExcel')
    }
  },
  watch: {
    templateList: {
      handler (value, oldVal) {
        console.log('templateList', value)
        this.templateList = value
        this.importTemplateTypeListHandle()
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: false
    }
  }
}
</script>
