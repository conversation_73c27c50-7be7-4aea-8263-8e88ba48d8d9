<template>
  <div class="add-body panel_body">
    <div class="addOrUpdatePanel" >
      <el-popover
        placement="right-start"
        title="申报总金额规则"
        width="600"
        trigger="hover">
        <div class="popover-content">
          说明：起始申报总金额和截止申报总金额遵循左开右闭原则。即大于起始申报总金额，小于等于截止申报总金额
        </div>
        <el-button type="text" style="margin-left: 5px" slot="reference">
          <i class="el-icon-info el-icon--right warning">起始申报总金额和截止申报总金额遵循左开右闭原则</i>
        </el-button>
      </el-popover>
      <el-popover
          placement="right-start"
          title="申报总金额规则"
          width="600"
          trigger="hover">
        <div class="popover-content">
          说明：分摊金额保留3位小数，3位小数后直接舍弃
        </div>
        <div class="popover-content">
          申报单价小于0.001时，将设申报单价为0.001
        </div>
        <el-button type="text" style="margin-left: 45px" slot="reference">
          <i class="el-icon-info el-icon--right warning">修正后的总金额将按比例分摊到申报单价上</i>
        </el-button>
      </el-popover>
      <el-form :model="dataForm" id="correctedSumSettingData" ref="correctedSumSettingData">
        <el-table id="correctedTable"
          v-loading="dataListLoading" :data="dataForm.tableDataList"
          border
          style="width: 100%"
          @selection-change='selectRow'>
          <el-table-column :label="$t('bdExcelExportTemplateDetail.columnNo')" type="index" width="60" align="center"></el-table-column>
          <el-table-column v-if="true" :label="$t('bdLogisticsChannelCorrectedDeclareSum.matchMinSum')" prop="matchMinSumD" :render-header="addRedStar">
            <template slot-scope="scope">
              <el-form-item :prop="'tableDataList.' + scope.$index + '.matchMinSumD'" :rules='dataRule.matchMinSumD'>
                <el-input class="require_des" v-model="scope.row.matchMinSumD" maxlength="26"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="true" :label="$t('bdLogisticsChannelCorrectedDeclareSum.matchMaxSum')" prop="matchMaxSumD" :render-header="addRedStar">
            <template slot-scope="scope">
              <el-form-item :prop="'tableDataList.' + scope.$index + '.matchMaxSumD'" :rules='dataRule.matchMaxSumD'>
                <el-input class="require_des" v-model="scope.row.matchMaxSumD" maxlength="26"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="true" :label="$t('bdLogisticsChannelCorrectedDeclareSum.correctedSum')" prop="correctedSumD" :render-header="addRedStar">
            <template slot-scope="scope">
              <el-form-item :prop="'tableDataList.' + scope.$index + '.correctedSumD'" :rules='dataRule.correctedSumD'>
                <el-input class="require_des" v-model="scope.row.correctedSumD" maxlength="26"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="true" :label="$t('bdLogisticsChannelCorrectedDeclareSum.type')" prop="type" :render-header="addRedStar">
            <template slot-scope="scope">
              <el-form-item :prop="'tableDataList.' + scope.$index + '.type'" :rules='dataRule.type'>
                <el-select filterable v-model="scope.row.type"
                           :placeholder="$t('bdLogisticsChannelCorrectedDeclareSum.type')">
                  <el-option v-for="item in correctedDeclareSumTypeList" :key="item.dictValue" :label="item.dictName"
                             :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="60">
            <template slot="header" slot-scope="scope">
              <span>{{ $t('handle') }}</span>
            </template>
            <template slot-scope="scope">
              <el-link :underline='false' class="el-icon-plus el-link--default" style="margin-bottom:16px"   @click.prevent="addRow(scope.$index+1)"></el-link>
              <el-link :underline='false' class="el-icon-minus el-link--warning" style="margin-bottom:16px"  @click.prevent="delData(scope.$index)"></el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
  </div>
</template>

<script>
import { isDecimal3 } from '@/utils/validate'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        type: 1,
        tableDataList: []
      },
      selectlistRow: [],
      correctedDeclareSumTypeList: [
        { dictValue: 1, dictName: '固定总金额' },
        { dictValue: 2, dictName: '总金额上减少' }
      ],
      rowNum: 1,
      dataListLoading: false
    }
  },
  methods: {
    validated () {
      let ret = false
      // 若仅有一行且起始终止修正申报总金额都为空，清空校验
      if (this.dataForm.tableDataList.length === 1) {
        let fisrtRow = this.dataForm.tableDataList[0]
        if (fisrtRow.matchMinSumD === '' && fisrtRow.matchMaxSumD === '' && fisrtRow.correctedSumD === '') {
          this.dataForm.tableDataList = []
          this.$refs['correctedSumSettingData'].clearValidate()
          return true
        }
      }
      this.$refs['correctedSumSettingData'].validate((valid) => {
        if (valid) {
          ret = true
        }
      })
      if (ret) {
        this.$emit('onCorrectedDeclareSumData', this.dataForm.tableDataList)
      }
      return ret
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()

        if (this.dataForm.id) {
          this.dataForm.tableDataList = []
          this.getInfo()
        } else {
          this.$refs['correctedSumSettingData'].resetFields()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.dataListLoading = true
      this.$http.get(`/bd/logisticschannelcorrecteddeclaresum/list/${this.dataForm.id}`).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data.length > 0) {
          this.dataForm.tableDataList = res.data
        } else {
          this.addRow(0)
        }
      }).catch(() => {})
    },
    // 增加行
    addRow (index) {
      let list = {
        matchMinSumD: '',
        matchMaxSumD: '',
        correctedSumD: '',
        type: 1
      }
      this.dataForm.tableDataList.splice(index, 0, list)
      // // 列序号重排
      this.dataForm.tableDataList.forEach((v, i) => {
        this.dataForm.tableDataList[i].columnNo = i
      })
      this.rowNum += 1
    },
    // 删除方法
    // 删除选中行
    delData (index) {
      // i 为选中的索引
      this.dataForm.tableDataList.splice(index, 1)
      // // 列序号重排
      this.dataForm.tableDataList.forEach((v, i) => {
        this.dataForm.tableDataList[i].columnNo = i
      })
      // 删除完数据之后清除勾选框
      this.$refs.table.clearSelection()
      if (this.dataForm.tableDataList.length === 0) {
        this.addRow(0)
      }
    },
    // 获取表格选中时的数据
    selectRow (val) {
      this.selectlistRow = val
    },
    getValuesMethod () {
      return this.dataForm.tableDataList
    },
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    }
  },
  computed: {
    dataRule () {
      const validateDecimal2 = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isDecimal3(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': '0到3位小数的正数' })))
        }
        callback()
      }
      const maxValue = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && value > 10000) {
          return callback(new Error(this.$t('validate.format', { 'attr': '不能大于10000' })))
        }
        callback()
      }
      const compareMinMaxValidator = (rule, value, callback) => {
        let field = rule.field
        let fields = field.split('.')
        let index = 0
        if (fields.length === 3) {
          index = fields[1]
        }
        // 获取获取值的方法
        let getvaluesMethod = rule.getValuesMethod
        // 调用getvaluesMethod方法，获取对象值
        let formData = getvaluesMethod()[index]
        // 有一个为空，可能还没有输入值，此时不比较
        if (formData.matchMinSumD === '' || formData.matchMaxSumD === '') {
          return callback()
        }

        // ===========================================================
        // 比较最小值不能大于最大值

        // 两个都有值，比较
        if (parseFloat(formData.matchMinSumD) <= parseFloat(formData.matchMaxSumD)) {
          // 最小申报总金额大于最大申报总金额
          // 先清除两个申报总金额的校验告警提示，目的是清除另一个申报总金额的不一致的提示
          this.$refs['correctedSumSettingData'].clearValidate(['tableDataList.' + index + '.matchMinSumD', 'tableDataList.' + index + '.matchMaxSumD'])
          callback()
        } else {
          callback(new Error('起始申报总金额不能大于终止申报总金额'))
        }
      }
      return {
        matchMinSumD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal2, trigger: 'blur' },
          { validator: maxValue, trigger: 'blur' },
          { validator: compareMinMaxValidator, trigger: 'blur', 'getValuesMethod': this.getValuesMethod }
        ],
        matchMaxSumD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal2, trigger: 'blur' },
          { validator: maxValue, trigger: 'blur' },
          { validator: compareMinMaxValidator, trigger: 'blur', 'getValuesMethod': this.getValuesMethod }
        ],
        correctedSumD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal2, trigger: 'blur' },
          { validator: maxValue, trigger: 'blur' }
        ],
        type: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  }
}
</script>

<!--<style lang="scss" scoped>-->
<!--  ::v-deep #correctedTable .el-form-item  {-->
<!--    margin-bottom: 0px !important;-->
<!--  }-->
<!--</style>-->
