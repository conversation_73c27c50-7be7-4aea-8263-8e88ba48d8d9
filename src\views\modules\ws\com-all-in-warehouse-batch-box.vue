<template>
  <el-dialog title="批量导入箱明细重量尺" :visible.sync="visible"  :close-on-click-modal="false" :close-on-press-escape="false" :before-close="closeHandle">
<!--    <div>-->
<!--      {{ title }}-->
<!--    </div>-->
    <el-input type="textarea" v-model="value" :autosize="{ minRows: 8, maxRows: 20}" autocomplete="off" :placeholder="title + '\n\n\n直接从excel中复制并粘贴到此处'"></el-input>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeHandle">取 消</el-button>
      <el-button type="primary" @click="submitHandler">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      togetherWeight: false,
      oneByOneForLabel: false,
      value: ''
    }
  },
  computed: {
    title () {
      let title = '长  宽  高  重量  箱数'
      if (this.togetherWeight) {
        title = '长  宽  高  箱数'
      }
      if (this.oneByOneForLabel) {
        title += '  箱号/FBA唛头'
      }
      return title
    }
  },
  methods: {
    init (togetherWeight, oneByOneForLabel) {
      this.togetherWeight = togetherWeight
      this.oneByOneForLabel = oneByOneForLabel
      this.visible = true
    },
    closeHandle () {
      this.visible = false
      this.value = ''
    },
    submitHandler () {
      if (!this.value || this.value === '') {
        return this.$message.error('请填写箱信息')
      }
      this.visible = false

      let boxList = []
      let rows = this.value.trim().split('\n')
      for (let i = 0; i < rows.length; i++) {
        let row = rows[i]
        let cols = row.split('\t')
        let box = {
          'lengthD': cols[0],
          'widthD': cols[1],
          'heightD': cols[2],
          'weightD': '',
          'packageQty': '',
          'multiplePackageNos': ''
        }
        if (!this.togetherWeight) {
          box.weightD = cols.length > 3 ? cols[3] : ''
          box.packageQty = cols.length > 4 ? cols[4] : ''
          if (this.oneByOneForLabel) {
            box.multiplePackageNos = cols.length > 5 ? cols[5] : ''
          }
        } else {
          box.packageQty = cols.length > 3 ? cols[3] : ''
          if (this.oneByOneForLabel) {
            box.multiplePackageNos = cols.length > 4 ? cols[4] : ''
          }
        }
        boxList.push(box)
      }
      this.$emit('callback', boxList)
      console.log(boxList)
      this.value = ''
    }
  }
}
</script>

<style scoped>

</style>
