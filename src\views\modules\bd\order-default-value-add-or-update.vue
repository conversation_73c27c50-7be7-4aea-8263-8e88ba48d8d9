<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{'订单默认值设置'}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <!-- 基础信息 -->
        <el-row>
          <el-col>
            <area-box title="基础信息"  class="chanel_info clearfix">
              <el-row class="optBtn">
                <el-col class="text-left">
                  <el-button size="mini" type="primary" plain @click="addBaseInfo()">{{ $t('add') }}</el-button>
                </el-col>
              </el-row>
              <el-row >
                <el-col >
                  <el-table class="tableForm" :data="dataForm.baseData" style="width: 100%;">
                    <el-table-column prop="fieldName" :label="$t('bdOrderDefaultValue.fieldName')" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'baseData.' + scope.$index + '.fieldKey'" :rules="dataRule.fieldKey">
                          <el-select @change="changeBaseInfoProperty(scope.$index)" class="w-percent-100" v-model="scope.row.fieldKey" clearable :placeholder="$t('select')" filterable>
                            <el-option v-for="item in orderBaseEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="required" :label="$t('bdOrderDefaultValue.defaultValue')" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'baseData.' + scope.$index + '.defaultValue'" :rules="dataRule.defaultValue">
                          <el-input :disabled="scope.row.defaultValueType===11" v-model="scope.row.defaultValue" maxlength="60" @blur="defaultValueValidate(scope.row)" style="max-width: 100%; margin: 0"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="fillType" :label="$t('bdOrderDefaultValue.fillType')" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'baseData.' + scope.$index + '.fillType'" :rules="dataRule.fieldKey">
                          <el-select @change="changeBaseInfoProperty(scope.$index)" class="w-percent-100" v-model="scope.row.fillType" clearable :placeholder="$t('select')" filterable>
                            <el-option v-for="item in fillTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="50">
                      <template slot-scope="scope">
                        <!-- 删除 -->
                        <popconfirm i18nOperateValue="delete" @clickHandle="deleteBaseInfo(scope.$index)"></popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </area-box>
          </el-col>
        </el-row>
        <!--包裹明细 -->
        <el-row>
          <el-col>
            <area-box :title="$t('bdOrderVerificationDetail.packageCargoVerification')"  class="chanel_info clearfix">
              <el-row class="optBtn">
                <el-col class="text-left">
                  <el-button size="mini" type="primary" plain @click="addPackageCargo()">{{ $t('add') }}</el-button>
                </el-col>
              </el-row>
              <el-row >
                <el-col >
                  <el-table class="tableForm" :data="dataForm.packageCargoData" style="width: 100%;">
                    <el-table-column prop="fieldName" :label="$t('bdOrderDefaultValue.fieldName')" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'packageCargoData.' + scope.$index + '.fieldKey'" :rules="dataRule.fieldKey">
                          <el-select @change="changePackageCargoProperty(scope.$index)" class="w-percent-100" v-model="scope.row.fieldKey" clearable :placeholder="$t('select')" filterable>
                            <el-option v-for="item in packageCargoEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <!--                <el-table-column prop="defaultValueType" :label="$t('bdOrderDefaultValue.defaultValueType')" header-align="center" align="center">-->
                    <!--                    <template slot-scope="scope">-->
                    <!--                        <el-form-item :prop="'packageCargoData.' + scope.$index + '.defaultValueType'" :rules="dataRule.defaultValueType">-->
                    <!--                            <el-select @change="changePackageCargoProperty(scope.$index)" class="w-percent-100" v-model="scope.row.defaultValueType" clearable :placeholder="$t('select')" filterable>-->
                    <!--                                <el-option v-for="item in defaultValueTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>-->
                    <!--                            </el-select>-->
                    <!--                        </el-form-item>-->
                    <!--                    </template>-->
                    <!--                </el-table-column>-->
                    <!--                <el-table-column prop="linkFieldNames" :label="$t('bdOrderDefaultValue.linkFieldName')" header-align="center" align="center">-->
                    <!--                    <template slot-scope="scope">-->
                    <!--                        <el-form-item :prop="'packageCargoData.' + scope.$index + '.linkFieldNames'" :rules="dataRule.linkFieldNames">-->
                    <!--                            <el-select :disabled="scope.row.defaultValueType===10" @change="changePackageCargoProperty(scope.$index)" class="w-percent-100" v-model="scope.row.linkFieldNames" clearable :placeholder="$t('select')" multiple filterable>-->
                    <!--                                <el-option v-for="item in packageCargoEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>-->
                    <!--                            </el-select>-->
                    <!--                        </el-form-item>-->
                    <!--                    </template>-->
                    <!--                </el-table-column>-->
                    <el-table-column prop="required" :label="$t('bdOrderDefaultValue.defaultValue')" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'packageCargoData.' + scope.$index + '.defaultValue'" :rules="dataRule.defaultValue">
                          <el-input :disabled="scope.row.defaultValueType===11" v-model="scope.row.defaultValue" maxlength="60" @blur="defaultValueValidate(scope.row)" style="max-width: 100%; margin: 0"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="fillType" :label="$t('bdOrderDefaultValue.fillType')" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'packageCargoData.' + scope.$index + '.fillType'" :rules="dataRule.fieldKey">
                          <el-select @change="changePackageCargoProperty(scope.$index)" class="w-percent-100" v-model="scope.row.fillType" clearable :placeholder="$t('select')" filterable>
                            <el-option v-for="item in fillTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="50">
                      <template slot-scope="scope">
                        <!-- 删除 -->
                        <popconfirm i18nOperateValue="delete" @clickHandle="deletePackageCargo(scope.$index)"></popconfirm>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </area-box>
          </el-col>
        </el-row>
        <!--收件人-->
        <el-row>
          <el-col>
            <area-box :title="$t('bdOrderVerificationDetail.consigneeVerification')"  class="chanel_info clearfix">
              <el-row class="optBtn">
                <el-col class="text-left">
                  <el-button size="mini" type="primary" plain @click="addConsignee()">{{ $t('add') }}</el-button>
                </el-col>
              </el-row>
              <el-row >
                <el-col >
                  <div >
                    <el-table class="tableForm" v-loading="false" :data="dataForm.consigneeData" style="width: 100%;">
                      <el-table-column prop="fieldName" :label="$t('bdOrderDefaultValue.fieldName')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'consigneeData.' + scope.$index + '.fieldKey'" :rules="dataRule.fieldKey">
                            <el-select @change="changeConsigneeProperty(scope.$index, scope.row.fieldKey)" class="w-percent-100" v-model="scope.row.fieldKey" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in consigneeEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="defaultValueType" :label="$t('bdOrderDefaultValue.defaultValueType')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'consigneeData.' + scope.$index + '.defaultValueType'" :rules="dataRule.defaultValueType">
                            <el-select :disabled="scope.row.fieldType!=='STRING'" class="w-percent-100" v-model="scope.row.defaultValueType" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in defaultValueTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="linkFieldNames" :label="$t('bdOrderDefaultValue.linkFieldName')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'consigneeData.' + scope.$index + '.linkFieldNames'" :rules="dataRule.linkFieldNames">
                            <el-select :disabled="scope.row.defaultValueType!==11 && scope.row.defaultValueType!==13" class="w-percent-100" v-model="scope.row.linkFieldNames" clearable :placeholder="$t('select')" multiple filterable allow-create>
                              <el-option v-for="item in consigneeEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="required" :label="$t('bdOrderDefaultValue.defaultValue')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'consigneeData.' + scope.$index + '.defaultValue'" :rules="dataRule.defaultValue">
                            <el-input :disabled="scope.row.defaultValueType!==10" v-model="scope.row.defaultValue" maxlength="60" @blur="defaultValueValidate(scope.row)" style="max-width: 100%; margin: 0"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="fillType" :label="$t('bdOrderDefaultValue.fillType')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'consigneeData.' + scope.$index + '.fillType'" :rules="dataRule.fieldKey">
                            <el-select class="w-percent-100" v-model="scope.row.fillType" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in fillTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="50">
                        <template slot-scope="scope">
                          <!-- 删除 -->
                          <popconfirm i18nOperateValue="delete" @clickHandle="deleteConsignee(scope.$index)"></popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-col>
              </el-row>
            </area-box>
          </el-col>
        </el-row>
        <!--发件人-->
        <el-row>
          <el-col>
            <area-box :title="$t('bdOrderVerificationDetail.shipperVerification')"  class="chanel_info clearfix">
              <el-row class="optBtn">
                <el-col class="text-left">
                  <el-button size="mini" type="primary" plain @click="addShipper()">{{ $t('add') }}</el-button>
                </el-col>
              </el-row>
              <el-row >
                <el-col>
                  <div >
                    <el-table class="tableForm" v-loading="false" :data="dataForm.shipperData" style="width: 100%;">
                      <el-table-column prop="fieldName" :label="$t('bdOrderDefaultValue.fieldName')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'shipperData.' + scope.$index + '.fieldKey'" :rules="dataRule.fieldKey">
                            <el-select @change="changeShipperProperty(scope.$index, scope.row.fieldKey)" class="w-percent-100" v-model="scope.row.fieldKey" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in shipperEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="defaultValueType" :label="$t('bdOrderDefaultValue.defaultValueType')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'shipperData.' + scope.$index + '.defaultValueType'" :rules="dataRule.defaultValueType">
                            <el-select :disabled="scope.row.fieldType!=='STRING'" class="w-percent-100" v-model="scope.row.defaultValueType" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in defaultValueTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="linkFieldNames" :label="$t('bdOrderDefaultValue.linkFieldName')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'shipperData.' + scope.$index + '.linkFieldNames'" :rules="dataRule.linkFieldNames">
                            <el-select :disabled="scope.row.defaultValueType!==11 && scope.row.defaultValueType!==13" class="w-percent-100" v-model="scope.row.linkFieldNames" clearable :placeholder="$t('select')" multiple filterable allow-create>
                              <el-option v-for="item in shipperEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="required" :label="$t('bdOrderDefaultValue.defaultValue')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'shipperData.' + scope.$index + '.defaultValue'" :rules="dataRule.defaultValue">
                            <el-input :disabled="scope.row.defaultValueType!==10" v-model="scope.row.defaultValue" maxlength="60" @blur="defaultValueValidate(scope.row)" style="max-width: 100%; margin: 0"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="fillType" :label="$t('bdOrderDefaultValue.fillType')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'shipperData.' + scope.$index + '.fillType'" :rules="dataRule.fieldKey">
                            <el-select class="w-percent-100" v-model="scope.row.fillType" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in fillTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="50">
                        <template slot-scope="scope">
                          <!-- 删除 -->
                          <popconfirm i18nOperateValue="delete" @clickHandle="deleteShipper(scope.$index)"></popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-col>
              </el-row>
            </area-box>
          </el-col>
        </el-row>
        <!--报关明细-->
        <el-row>
          <el-col>
            <area-box :title="$t('bdOrderVerificationDetail.declareVerification')"  class="chanel_info clearfix">
              <el-row class="optBtn">
                <el-col class="text-left">
                  <el-button type="primary" plain size="mini" @click="addDeclare()">{{ $t('add') }}</el-button>
                </el-col>
              </el-row>
              <el-row >
                <el-col >
                  <div >
                    <el-table class="tableForm" :data="dataForm.declareData" style="width: 100%;">
                      <el-table-column prop="fieldName" :label="$t('bdOrderDefaultValue.fieldName')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'declareData.' + scope.$index + '.fieldKey'" :rules="dataRule.fieldKey">
                            <el-select @change="changeDeclareProperty(scope.$index, scope.row.fieldKey)" class="w-percent-100" v-model="scope.row.fieldKey" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in declareEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="defaultValueType" :label="$t('bdOrderDefaultValue.defaultValueType')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'declareData.' + scope.$index + '.defaultValueType'" :rules="dataRule.defaultValueType">
                            <el-select :disabled="scope.row.fieldType!=='STRING'" class="w-percent-100" v-model="scope.row.defaultValueType" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in defaultValueTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="linkFieldNames" :label="$t('bdOrderDefaultValue.linkFieldName')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'declareData.' + scope.$index + '.linkFieldNames'" :rules="dataRule.linkFieldNames">
                            <el-select :disabled="scope.row.defaultValueType!==11 && scope.row.defaultValueType!==13" class="w-percent-100" v-model="scope.row.linkFieldNames" clearable :placeholder="$t('select')" multiple filterable allow-create>
                              <el-option v-for="item in declareEnumList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="required" :label="$t('bdOrderDefaultValue.defaultValue')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'declareData.' + scope.$index + '.defaultValue'" :rules="dataRule.defaultValue">
                            <el-input :disabled="scope.row.defaultValueType!==10" v-model="scope.row.defaultValue" maxlength="60" @blur="defaultValueValidate(scope.row)" style="max-width: 100%; margin: 0"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop="fillType" :label="$t('bdOrderDefaultValue.fillType')" header-align="center" align="center">
                        <template slot-scope="scope">
                          <el-form-item :prop="'declareData.' + scope.$index + '.fillType'" :rules="dataRule.fieldKey">
                            <el-select class="w-percent-100" v-model="scope.row.fillType" clearable :placeholder="$t('select')" filterable>
                              <el-option v-for="item in fillTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="50">
                        <template slot-scope="scope">
                          <!-- 删除 -->
                          <popconfirm i18nOperateValue="delete" @clickHandle="deleteDeclare(scope.$index)"></popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-col>
              </el-row>
            </area-box>
          </el-col>
        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import api from '@/api'
import areaBox from '@/components/areaBox'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseData from '@/api/baseData'
import { isPlusInteger2, is1Decimal1, isDecimal3 } from '@/utils/validate'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      dataForm: {
        id: '',
        logisticsChannelId: '',
        consigneeData: [],
        baseData: [],
        shipperData: [],
        packageCargoData: [],
        declareData: []
      },
      defaultValueTypeList: [],
      orderBaseEnumList: [],
      fillTypeList: [],
      consigneeEnumList: [],
      shipperEnumList: [],
      packageCargoEnumList: [],
      declareEnumList: []
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  computed: {
    dataRule () {
      const validateWeight = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isDecimal3(value)) {
          return callback(new Error(this.$t('validate.plusdecimal', { 'number': 3 })))
        }
        let parseVal = String(parseInt(value))
        if (parseVal.length > 3) {
          return callback(new Error(this.$t('validate.maxLength', { 'max': 3 })))
        }
        callback()
      }
      const validateSize = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !is1Decimal1(value)) {
          return callback(new Error(this.$t('validate.plusdecimal', { 'number': 1 })))
        }
        callback()
      }
      const validateLength = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.positiveInteger')))
        }
        callback()
      }
      return {
        logisticsChannelId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fieldKey: [
          { required: true, message: this.$t('validate.required'), trigger: ['blur', 'change'] }
        ],
        fieldName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fieldType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        defaultValueType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fillType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        length: [
          { validator: validateLength, trigger: 'blur' }
        ],
        size: [
          { validator: validateSize, trigger: 'blur' }
        ],
        weight: [
          // { required: false, message: this.$t('validate.required'), trigger: 'change' },
          { validator: validateWeight, trigger: ['blur', 'change'] }
        ],
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    areaBox
  },
  methods: {
    defaultValueValidate (item, callback) { // 默认值校验
      if (item.defaultValueType !== 10 && item.defaultValueType !== 13) {
        return true
      }
      let fieldName = item.fieldName + ','
      let fieldType = item.fieldType
      let fieldDefault = item.defaultValue
      if (fieldType === 'DOUBLE' && !isDecimal3(fieldDefault)) {
        return this.$message.error(fieldName + this.$t('validate.plusdecimal', { 'number': 3 }))
      }
      if (fieldType === 'INTEGER' && !isPlusInteger2(fieldDefault)) {
        return this.$message.error(fieldName + this.$t('validate.positiveInteger'))
      }
    },
    // 基础信息
    changeBaseInfoProperty (index) {
      let datas = this.dataForm.baseData
      let baseInfo = datas[index]
      this.orderBaseEnumList.forEach((v) => {
        if (v.code === baseInfo.fieldKey) {
          this.dataForm.baseData[index].fieldName = v.name
          this.dataForm.baseData[index].fieldType = v.type
          this.dataForm.baseData[index].maxValueD = ''
          this.dataForm.baseData[index].minValueD = ''
        }
      })
      if (datas.length > 0) {
        let i = 0
        datas.forEach((data) => {
          if (data.fieldKey === baseInfo.fieldKey && data.country === baseInfo.country) {
            i++
          }
        })
        if (i > 1) {
          this.dataForm.baseData[index].fieldKey = ''
        }
      }
    },
    // 报关明细
    changeDeclareProperty (index, $val) {
      if (!$val) {
        this.dataForm.declareData[index].fieldName = ''
        this.dataForm.declareData[index].fieldType = ''
        this.dataForm.declareData[index].defaultValue = ''
        return false
      }
      let datas = this.dataForm.declareData
      let declare = datas[index]
      this.declareEnumList.forEach((v) => {
        if (v.code === declare.fieldKey) {
          this.dataForm.declareData[index].fieldName = v.name
          this.dataForm.declareData[index].fieldType = v.type
          this.dataForm.declareData[index].defaultValue = ''
        }
      })
      if (datas.length > 0) {
        let i = 0
        datas.forEach((data) => {
          // console.log(data.fieldKey, declare.fieldKey)
          if (data.fieldKey === declare.fieldKey) {
            i++
          }
        })
        if (i > 1) {
          this.dataForm.declareData[index].fieldKey = ''
        }
      }
    },
    // 货品明细
    changePackageCargoProperty (index) {
      let datas = this.dataForm.packageCargoData
      let packageCargo = datas[index]
      this.packageCargoEnumList.forEach((v) => {
        if (v.code === packageCargo.fieldKey) {
          this.dataForm.packageCargoData[index].fieldName = v.name
          this.dataForm.packageCargoData[index].fieldType = v.type
          this.dataForm.packageCargoData[index].defaultValue = ''
        }
      })
      if (datas.length > 0) {
        let i = 0
        datas.forEach((data) => {
          if (data.fieldKey === packageCargo.fieldKey) {
            i++
          }
        })
        if (i > 1) {
          this.dataForm.packageCargoData[index].fieldKey = ''
        }
      }
    },
    // 收件人信息
    changeConsigneeProperty (index, $val) {
      if (!$val) {
        this.dataForm.consigneeData[index].fieldName = ''
        this.dataForm.consigneeData[index].fieldType = ''
        this.dataForm.consigneeData[index].defaultValue = ''
        return false
      }
      let datas = this.dataForm.consigneeData
      let consignee = datas[index]
      this.consigneeEnumList.forEach((v) => {
        if (v.code === consignee.fieldKey) {
          this.dataForm.consigneeData[index].fieldName = v.name
          this.dataForm.consigneeData[index].fieldType = v.type
          this.dataForm.consigneeData[index].defaultValue = ''
        }
      })
      if (datas.length > 0) {
        let i = 0
        datas.forEach((data) => {
          if (data.fieldKey === consignee.fieldKey) {
            i++
          }
        })
        if (i > 1) {
          this.dataForm.consigneeData[index].fieldKey = ''
        }
      }
    },
    // 发件人信息
    changeShipperProperty (index, $val) {
      if (!$val) {
        this.dataForm.shipperData[index].fieldName = ''
        this.dataForm.shipperData[index].fieldType = ''
        this.dataForm.shipperData[index].defaultValue = ''
        return false
      }
      let datas = this.dataForm.shipperData
      let consignee = datas[index]
      this.shipperEnumList.forEach((v) => {
        if (v.code === consignee.fieldKey) {
          this.dataForm.shipperData[index].fieldName = v.name
          this.dataForm.shipperData[index].fieldType = v.type
          this.dataForm.shipperData[index].defaultValue = ''
        }
      })
      if (datas.length > 0) {
        let i = 0
        datas.forEach((data) => {
          if (data.fieldKey === consignee.fieldKey) {
            i++
          }
        })
        if (i > 1) {
          this.dataForm.shipperData[index].fieldKey = ''
        }
      }
    },
    addConsignee () {
      this.dataForm.consigneeData.push({
        fieldKey: '',
        fieldName: '',
        fieldType: '',
        defaultValueType: 10,
        fillType: 10,
        defaultValue: ''
      })
      // this.dataForm.checkVals = this.dataForm.consigneeData
    },
    addShipper () {
      this.dataForm.shipperData.push({
        fieldKey: '',
        fieldName: '',
        fieldType: '',
        defaultValueType: 10,
        fillType: 10,
        defaultValue: ''
      })
      // this.dataForm.checkVals = this.dataForm.consigneeData
    },
    deleteConsignee (index) {
      this.dataForm.consigneeData.splice(index, 1)
    },
    deleteShipper (index) {
      this.dataForm.shipperData.splice(index, 1)
    },
    addBaseInfo () {
      this.dataForm.baseData.push({
        fieldKey: '',
        fieldName: '',
        fieldType: '',
        defaultValueType: 10,
        fillType: 10,
        defaultValue: ''
      })
    },
    addDeclare () {
      this.dataForm.declareData.push({
        fieldKey: '',
        fieldName: '',
        fieldType: '',
        defaultValueType: 10,
        fillType: 10,
        defaultValue: ''
      })
      // this.dataForm.checkVals = this.dataForm.consigneeData
    },
    deleteDeclare (index) {
      this.dataForm.declareData.splice(index, 1)
    },
    deleteBaseInfo (index) {
      this.dataForm.baseData.splice(index, 1)
    },
    deletePackageCargo (index) {
      this.dataForm.packageCargoData.splice(index, 1)
    },
    addPackageCargo () {
      this.dataForm.packageCargoData.push({
        fieldKey: '',
        fieldName: '',
        fieldType: '',
        defaultValueType: 10,
        fillType: 10,
        defaultValue: ''
      })
    },
    async getDict () {
      // 获取相关字典
      this.fillTypeList = await this.getDictTypeList('fillType')
      this.defaultValueTypeList = await this.getDictTypeList('defaultValueType')
    },
    // 同步通过接口获取基础数据
    async getBaseData () {
      // 收件人信息列表
      this.consigneeEnumList = await baseData(api.consigneeEnumList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 发件人信息列表
      this.shipperEnumList = await baseData(api.shipperEnumList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 基础信息列表
      this.orderBaseEnumList = await baseData(api.orderBaseEnumList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 包裹明细列表
      this.packageCargoEnumList = await baseData(api.packageCargoEnumList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 报关信息列表
      this.declareEnumList = await baseData(api.declareEnumList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.logisticsChannelId) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/bd/orderdefaultvalue/listByLogisticsChannelId/${this.dataForm.logisticsChannelId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.dataForm.packageCargoData = res.data.packageCargoData || []
        this.dataForm.consigneeData = res.data.consigneeData || []
        this.dataForm.shipperData = res.data.shipperData || []
        this.dataForm.declareData = res.data.declareData || []
        this.dataForm.baseData = res.data.baseData || []
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let packageCargo = this.dataForm.packageCargoData
        let declare = this.dataForm.declareData
        let base = this.dataForm.baseData

        if (packageCargo != null && packageCargo.length > 0) {
          for (let i = 0; i < packageCargo.length; i++) {
            let item = packageCargo[i]
            let fieldName = item.fieldName + ','
            let fieldType = item.fieldType
            let fieldDefault = item.defaultValue
            if (fieldType === 'DOUBLE' && !isDecimal3(fieldDefault)) {
              return this.$message.error(fieldName + this.$t('validate.plusdecimal', { 'number': 3 }))
            }
            if (fieldType === 'INTEGER' && !isPlusInteger2(fieldDefault)) {
              return this.$message.error(fieldName + this.$t('validate.positiveInteger'))
            }
          }
        }
        if (declare != null && declare.length > 0) {
          for (let i = 0; i < declare.length; i++) {
            let item = declare[i]
            this.defaultValueValidate(item)
            let fieldName = item.fieldName + ','
            let fieldType = item.fieldType
            let fieldDefault = item.defaultValue
            if (fieldType === 'DOUBLE' && !isDecimal3(fieldDefault)) {
              return this.$message.error(fieldName + this.$t('validate.plusdecimal', { 'number': 3 }))
            }
            if (fieldType === 'INTEGER' && !isPlusInteger2(fieldDefault)) {
              return this.$message.error(fieldName + this.$t('validate.positiveInteger'))
            }
          }
        }
        if (base != null && base.length > 0) {
          for (let i = 0; i < base.length; i++) {
            let item = base[i]
            let fieldName = item.fieldName + ','
            let fieldType = item.fieldType
            let fieldDefault = item.defaultValue
            if (fieldType === 'DOUBLE' && !isDecimal3(fieldDefault)) {
              return this.$message.error(fieldName + this.$t('validate.plusdecimal', { 'number': 3 }))
            }
            if (fieldType === 'INTEGER' && !isPlusInteger2(fieldDefault)) {
              return this.$message.error(fieldName + this.$t('validate.positiveInteger'))
            }
          }
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/bd/orderdefaultvalue/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('cancelOrderDefaultValue')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelOrderDefaultValue')
    }
  }
}
</script>
