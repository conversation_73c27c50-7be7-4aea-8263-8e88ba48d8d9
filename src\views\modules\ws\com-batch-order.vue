<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box margin_bottom10" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="80px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10" >
                <el-col :span="8">
                  <three-no-input ref="threeNoInput" :deliveryNo.sync="dataForm.deliveryNos"
                                  :hideItem="[1,2,4,5,6,7]" :autosize="threeNoInputAutoSize" :noSize="5000"/>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('wsComBatchOrder.logisticsChannelCode')" prop="logisticsChannelCode">
                    <el-select v-model="dataForm.logisticsChannelCode" filterable clearable>
                      <el-option v-for="(item, index) in logisticsChannelList" :key="index" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('wsComBatchOrder.batchNo')" prop="batchNo">
                    <el-input v-model="dataForm.batchNo" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('system.status')" prop="status">
                    <el-select v-model="dataForm.status" filterable clearable>
                      <el-option v-for="item in statusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12">
            <el-button size="mini" type="primary" v-if="$hasPermission('ws:comBatchOrder:deleteOrder')"  plain @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini" type="primary" plain v-if="$hasPermission('ws:comBatchOrder:addOrder')" @click="addHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <template v-else-if="item.prop === 'batchFileUrl'">
                    <span v-html="formatterFn(scope, item.prop)"></span>
                  </template>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false" v-if="$hasPermission('ws:comBatchOrder:view')"  @click="viewHandle(scope.row)">{{ $t('view') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('ws:comBatchOrder:update') && scope.row.status === 10"  @click="updateHandle(scope.row)">{{ $t('update') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('ws:comBatchOrder:addItem') && scope.row.status === 10"  @click="addItemHandle(scope.row)">{{ $t('wsComBatchOrder.addItem') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('ws:comBatchOrder:forecast') && scope.row.status === 10" v-loading="forecastLoading"  @click="forecastHandle(scope.row.id)">{{ $t('forecast') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('ws:comBatchOrder:delivery') && scope.row.status === 20" v-loading="forecastLoading"  @click="deliveryHandle(scope.row.id)">{{ $t('wsComBatchOrder.delivery') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="searchHandle" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <addDialog  ref="addDialog" @refreshDataList="getDataList" @addDialogAfterHandle="addDialogAfterHandle"></addDialog>
    <addItemDialog  ref="addItemDialog" @refreshDataList="searchHandle" @addItemDialogAfterHandle="addItemDialogAfterHandle"></addItemDialog>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterCodeNativeName, formatterName, formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './com-batch-order-add-or-update'
import ViewDetail from './com-batch-order-view-detail'
import AddDialog from './com-batch-order-add-dialog'
import AddItemDialog from './com-batch-order-add-item-dialog'
import baseData from '@/api/baseData'
import baseDataApi from '@/api'
import threeNoInput from '@/components/multiple-no-input.vue'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '80', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'createDate', label: this.$t('system.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '0', prop: 'customerId', label: this.$t('wsComBatchOrder.customerId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'customerName', label: this.$t('wsComBatchOrder.customerName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'batchNo', label: this.$t('wsComBatchOrder.batchNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsChannelCode', label: this.$t('wsComBatchOrder.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'originPostcode', label: this.$t('wsComBatchOrder.originPostcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'parcelQty', label: this.$t('wsComBatchOrder.parcelQty'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'batchFileUrl', label: this.$t('wsComBatchOrder.batchFileUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'providerId', label: this.$t('wsComBatchOrder.providerId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'errorMessage', label: this.$t('wsComBatchOrder.errorMessage'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ws/combatchorder/page',
        getDataListIsPage: true,
        getDataListURLOfRequestType: 'post',
        deleteURL: '/ws/combatchorder',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        deliveryNos: ''
      },
      forecastLoading: false,
      logisticsChannelList: [],
      providerList: [],
      statusList: [],
      activeName: 'all',
      tableName: 'ws-combatchorder'
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    /**
     * 用http渲染文件路径
     * @param fileUrl 文件路径
     */
    formatterFileWidthUrl (fileUrl) {
      let name = ''
      if (!fileUrl) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      name = '<a href="' + fileUrl + '" target="_blank">' + fileUrl + '</a>'
      return name
    },
    searchHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      if (!initFlag) {
        return
      }
      this.getDataList()
    },
    async getDict () {
      this.statusList = await this.getDictTypeList('wsComBatchOrderStatus')
    },
    async getBaseData () {
      this.providerList = await baseData(baseDataApi.providerList, { status: 11 }).catch(() => {})
      this.logisticsChannelList = await baseData(baseDataApi.logisticsChannelByParamsList, { autoFilter: true, isEnable: true }).catch((res) => {
        const msg = res.msg ? res.msg : res
        this.$message.error(msg)
      })
    },
    // 查看
    viewHandle (data) {
      this.viewVisible = true
      this.$nextTick(() => {
        this.$refs.viewDetail.batchDataForm.id = data.id
        this.$refs.viewDetail.dataForm.batchId = data.id
        this.$refs.viewDetail.init()
      })
    },
    addHandle () {
      this.$refs.addDialog.dataForm.batchNo = ''
      this.$refs.addDialog.dataForm.id = ''
      this.$refs.addDialog.dataForm.parcelQty = ''
      this.$refs.addDialog.dataForm.providerId = ''
      this.$refs.addDialog.dataForm.customerId = ''
      this.$refs.addDialog.dataForm.customerName = ''
      this.$refs.addDialog.dataForm.originPostcode = ''
      this.$refs.addDialog.dataForm.logisticsChannelCode = ''
      this.$refs.addDialog.canEditCustomerOrPostcode = false
      this.$refs.addDialog.init()
    },
    updateHandle (row) {
      this.$refs.addDialog.dataForm.batchNo = row.batchNo
      this.$refs.addDialog.dataForm.id = row.id
      this.$refs.addDialog.dataForm.parcelQty = row.parcelQty
      this.$refs.addDialog.dataForm.providerId = row.providerId
      this.$refs.addDialog.dataForm.customerId = row.customerId
      this.$refs.addDialog.dataForm.customerName = row.customerName
      this.$refs.addDialog.dataForm.originPostcode = row.originPostcode
      this.$refs.addDialog.dataForm.logisticsChannelCode = row.logisticsChannelCode
      this.$refs.addDialog.canEditCustomerOrPostcode = row.parcelQty > 0
      this.$refs.addDialog.init()
    },
    addDialogAfterHandle () {
      this.queryPageByParam()
    },
    addItemHandle (row) {
      this.$refs.addItemDialog.dataForm.batchId = row.id
      this.$refs.addItemDialog.dataForm.batchNo = row.batchNo
      this.$refs.addItemDialog.init()
    },
    addItemDialogAfterHandle () {
      this.queryPageByParam()
    },
    forecastHandle (batchId) {
      this.forecastLoading = true
      this.$http.post('/ws/combatchorder/forecast', 'id=' + batchId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.queryPageByParam()
      }).catch(() => {
      }).finally(() => { this.forecastLoading = false })
    },
    deliveryHandle (batchId) {
      this.forecastLoading = true
      this.$http.post('/ws/combatchorder/delivery', 'id=' + batchId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.queryPageByParam()
      }).catch(() => {
      }).finally(() => { this.forecastLoading = false })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'batchFileUrl':
          value = this.formatterFileWidthUrl(scope.row.batchFileUrl)
          break
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'providerId':
          value = formatterName(scope.row.providerId, this.providerList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeNativeName(scope.row.logisticsChannelCode, this.logisticsChannelList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      return { minRows: 3, maxRows: 6 }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    threeNoInput,
    AddItemDialog,
    AddDialog,
    AddOrUpdate,
    ViewDetail,
    tableSet
  }
}
</script>
