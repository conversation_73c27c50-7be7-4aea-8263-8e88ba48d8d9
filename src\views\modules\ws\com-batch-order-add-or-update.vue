<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('wsComBatchOrder.providerId')" prop="providerId">
              <el-input v-model="dataForm.providerId" :placeholder="$t('wsComBatchOrder.providerId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('wsComBatchOrder.batchNo')" prop="batchNo">
              <el-input v-model="dataForm.batchNo" :placeholder="$t('wsComBatchOrder.batchNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('wsComBatchOrder.parcelQty')" prop="parcelQty">
              <el-input v-model="dataForm.parcelQty" :placeholder="$t('wsComBatchOrder.parcelQty')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('wsComBatchOrder.batchFileUrl')" prop="batchFileUrl">
              <el-input v-model="dataForm.batchFileUrl" :placeholder="$t('wsComBatchOrder.batchFileUrl')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('wsComBatchOrder.errorMessage')" prop="errorMessage">
              <el-input v-model="dataForm.errorMessage" :placeholder="$t('wsComBatchOrder.errorMessage')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        providerId: '',
        batchNo: '',
        parcelQty: '',
        batchFileUrl: '',
        errorMessage: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        companyId: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        providerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        batchNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        parcelQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        batchFileUrl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        errorMessage: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updater: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updateDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        version: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        companyId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/ws/combatchorder/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/ws/combatchorder/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
