<template>
  <div class="add-body panel_body">
    <div class="mod-co__inorder flex_wrap">
      <el-card class="margin_bottom15" type="border-card" shadow="never">
        <el-form ref="form" label-width="120px">
        <el-row :gutter="10" type="flex">
          <el-col >
            <el-row :gutter="10">
              <el-col :sm="24" :md="8">
                <el-form-item :label="$t('bdLogisticsChannel.code')">
                  <span v-text="dataForm.code"></span>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item :label="$t('bdLogisticsChannel.name')">
                  <span v-text="dataForm.name"></span>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item :label="$t('bdLogisticsChannel.logisticsChannelType')">
                  <span>{{ dataForm.logisticsChannelType | formatterName(logisticsChannelTypeList)}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :sm="24" :md="8">
                <el-form-item :label="$t('bdLogisticsChannel.providerId')">
                  <span>{{ dataForm.providerId | formatterName(providerList)}}</span>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item :label="$t('system.status')">
                  <span>{{ dataForm.status | formatterType(baseStatus)}}</span>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item :label="$t('system.createDate')">
                  <span>{{ dataForm.createDate | gtmToLtm }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      </el-card>
      <el-tabs class="no_shadow" v-model="activeName" type="border-card" @tab-click="toTabs()">
          <el-tab-pane label="渠道规则" name="channelRole" >
            <logisticsProductChannelRule  ref="logisticsChannelRule" @onLogisticsChannelRuleData="logisticsChannelRuleData" :propData="setLogisticsChannelRuleData" ></logisticsProductChannelRule>
          </el-tab-pane>
          <el-tab-pane label="面单规则" name="channelLabel" >
            <lableRule  ref="lableRule" @onLableRule="lableRuleData" :propData="setLableRuleData" ></lableRule>
          </el-tab-pane>
          <el-tab-pane label="运单号设置" name="waybillSource" >
            <waybillNoSource ref="waybillNoSource" @onWaybillNoSourceData="waybillNoSourceData" @onApiTypeChange="apiTypeChange" :propData="setWaybillNoSourceData"></waybillNoSource>
          </el-tab-pane>
          <el-tab-pane label="订单校验" name="orderVerify" >
            <orderVerifyTemplatePublic ref="orderVerifyTemplatePublic" @onOrderVerifyTemplateData="orderVerifyTemplateData" :propData="setOrderVerifyTemplateData"></orderVerifyTemplatePublic>
          </el-tab-pane>
          <el-tab-pane label="回邮地址" name="returnAddress" >
            <returnMailingAddressPublic ref="returnMailingAddressPublic" @onReturnMailingAddress="returnMailingAddressData" :propData="setReturnMailingAddressData"></returnMailingAddressPublic>
          </el-tab-pane>
          <el-tab-pane label="分区" name="channelZone" >
            <zoneTemplatePublic ref="zoneTemplatePublic" @onZoneTemplateData="zoneTemplateData" :propData="setZoneTemplateData"></zoneTemplatePublic>
          </el-tab-pane>
          <el-tab-pane label="偏远分区" name="remoteZone" >
            <remoteZoneTemplatePublic ref="remoteZoneTemplatePublic" @onRemoteZoneTemplateData="remoteZoneTemplateData" :propData="setRemoteZoneTemplateData"></remoteZoneTemplatePublic>
          </el-tab-pane>
        <el-tab-pane label="轨迹抓取设置" name="trackGet" >
          <trackGetSetting ref="trackGetSetting" @onTrackGetSettingData="trackGetSettingData"></trackGetSetting>
        </el-tab-pane>
        <el-tab-pane label="计费设置" name="rateSetting" >
          <rateSetting ref="rateSetting" @onRateSettingData="rateSettingData"></rateSetting>
        </el-tab-pane>
        <el-tab-pane label="修正重量设置" name="correctedWeight" >
          <correctedWeight ref="correctedWeight" @onCorrectedWeightData="correctedWeightData"></correctedWeight>
        </el-tab-pane>
          <el-tab-pane label="面单PDF编辑" name="labelChange" >
          <label-change ref="labelChange" @onLabelChangeData="labelChangeData"></label-change>
        </el-tab-pane>
        <el-tab-pane label="获取最终单号设置" name="getFinalTrackNo" >
          <getFinalTrackNo ref="getFinalTrackNo" @onGetFinalTrackNoData="getFinalTrackNoData"></getFinalTrackNo>
        </el-tab-pane>
        <el-tab-pane label="自定义预上网轨迹" name="setInBoundPathItem" >
          <setInBoundPathItem ref="setInBoundPathItem" @onSetInBoundPathItemData="setInBoundPathItemData"></setInBoundPathItem>
        </el-tab-pane>
        </el-tabs>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
        <el-button @click="$closeFn" >{{ $t('close') }}</el-button>
      </div>
    </div>
    </div>
</template>

<script>
import debounce from 'lodash/debounce'
import closeMixins from '@/mixins/closeMixins'
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import api from '@/api'
import baseData from '@/api/baseData'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './logistics-channel-add-or-update'
import ViewDetail from './logistics-channel-view-detail'
import logisticsProductChannelRule from './logistics-product-channel-rule-add-or-update'
import lableRule from './lable-rule'
import channelApi from './logistics-channel-api-add-or-update'
import waybillNoSource from './waybill-no-source-add-or-update'
import orderVerifyTemplatePublic from './order-verification-template-public'
import zoneTemplatePublic from './zone-template-public'
import remoteZoneTemplatePublic from './remote-zone-template-public'
import trackGetSetting from './track-get-setting'
import returnMailingAddressPublic from './return-mailing-address-public'
import rateSetting from './logistics-channel-rate-setting'
import correctedWeight from './logistics-channel-corrected-weight-setting'
import labelChange from './logistics-channel-label-change-setting'
import setInBoundPathItem from './logistics-channel-inbound-path-item-setting'
import getFinalTrackNo from './logistics-channel-get-finalno-setting'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, closeMixins],
  data () {
    return {
      mixinViewModuleOptions: {
        activatedIsNeed: false
      },
      dataForm: {
        id: '',
        code: '',
        name: '',
        logisticsChannelType: '',
        providerId: '',
        status: '',
        createDate: '',
        trackPlatformType: '',
        carrierCode: '',
        trackNoSource: '',
        quotationSource: '10',
        trackPushNode: '',
        showProviderTransitionNo: '',
        providerTransitionNoRule: '',
        providerTransitionNoRuleRemark: '',
        getFinalTnoNode: '',
        getFinalTnoDelayTime: '',
        pushInboundPathNode: '',
        logisticsChannelRule: {},
        logisticsChannelOther: {},
        correctedWeightList: [],
        labelChangeList: [],
        inboundPathList: [],
        apiDetailList: []
      },
      baseStatus: [],
      returnMailingAddress: {},
      orderVerifyTemplate: {},
      zoneTemplate: {},
      remoteZoneTemplate: {},
      waybillNoSource: {},
      logisticsChannelTypeList: [],
      providerList: [],
      logisticsChannelStatusList: [],
      logisticsChannelSettingList: [],
      bdAddrTypeList: [],
      activeName: 'channelRole'
    }
  },
  activated () {
    if (this.$route.params.refresh) {
      this.getInfo()
    }
    this.$router.push({ params: { refresh: false } })
  },
  created () {
    Promise.all([
      this.getDict(),
      this.getBaseData()
    ]).then(() => {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
      })
    })
  },
  methods: {
    getInfo () {
      this.dataForm.id = this.$route.query.logisticsChannelId
      if (this.dataForm.id) {
        this.getLogisticsChannelInfo()
      }
      this.activeName = 'channelRole'
      this.$refs.logisticsChannelRule.clearValidated()
    },
    toTabs () {
      if (this.activeName === 'returnAddress') { // 回邮地址
      } else if (this.activeName === 'trackGet') { // 轨迹抓取设置
        this.$refs.trackGetSetting.init(this.dataForm.trackNoSource)
      }
    },
    returnMailingAddressData (data) {
      console.log('this.returnMailingAddress', this.returnMailingAddress)
      this.returnMailingAddress = data || {}
    },
    remoteZoneTemplateData (data) {
      this.remoteZoneTemplate = data || {}
    },
    trackGetSettingData (data) {
      if (data) {
        this.dataForm.trackPlatformType = data.trackPlatformType
        this.dataForm.carrierCode = data.carrierCode
        this.dataForm.trackNoSource = data.trackNoSource
        this.dataForm.trackPushNode = data.trackPushNode
      }
    },
    zoneTemplateData (data) {
      this.zoneTemplate = data || {}
    },
    orderVerifyTemplateData (data) {
      this.orderVerifyTemplate = data || {}
    },
    waybillNoSourceData (data) {
      this.waybillNoSource = data || {}
    },
    apiTypeChange (waybillNoSource) {
      this.setRateValue(waybillNoSource)
    },
    lableRuleData (data) {
      this.dataForm.logisticsChannelOther = data || {}
    },
    logisticsChannelRuleData (data) {
      this.dataForm.logisticsChannelRule = data || {}
    },
    rateSettingData (data) {
      if (data) {
        this.dataForm.quotationSource = data.quotationSource
      }
    },
    getFinalTrackNoData (data) {
      if (data) {
        this.dataForm.showProviderTransitionNo = data.showProviderTransitionNo
        this.dataForm.providerTransitionNoRule = data.providerTransitionNoRule
        this.dataForm.providerTransitionNoRuleRemark = data.providerTransitionNoRuleRemark
        this.dataForm.getFinalTnoNode = data.getFinalTnoNode
        this.dataForm.getFinalTnoDelayTime = data.getFinalTnoDelayTime
      }
    },
    setInBoundPathItemData (data) {
      if (data) {
        console.log('data.isEditInboundPath', data.isEditInboundPath)
        this.dataForm.isEditInboundPath = data.isEditInboundPath
        this.dataForm.pushInboundPathNode = data.pushInboundPathNode
        this.dataForm.inboundPathList = data.tableDataList || {}
      }
    },
    correctedWeightData (data) {
      if (data) {
        this.dataForm.correctedWeightList = data || {}
      }
    },
    labelChangeData (data) {
      if (data) {
        this.dataForm.labelChangeList = data || {}
      }
    },
    getLogisticsChannelInfo () {
      this.$http.get(`/bd/logisticschannel/getAllById/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        // 初始化
        let rule = {
          id: '',
          chargeableWeightRule: 0,
          volDiv: '',
          bubbleRate: '',
          bubbleCountingFormula: '',
          mismatchingBubbleCountingChargeableWeightRule: 10,
          carryFrontierD: '',
          bigCarryD: '',
          smallCarryD: '',
          multiWeightRule: 0,
          multiWeightCarry: '',
          overrunRule: '',
          constraintRule: '',
          creator: '',
          createDate: ''
        }
        // 初始化
        let lable = {
          id: '',
          lableType: 0,
          bagNoSource: 10,
          bagLabelSource: 10,
          postageProofLabelSource: 10,
          signProofLabelSource: 10,
          orderVerifyId: '',
          returnAddrId: '',
          zoneTemplateId: '',
          remoteZoneTemplateId: '',
          labelTemplateId: '',
          handoverListId: '',
          proformaInvoiceId: '',
          bagCardId: '',
          customsDeclarationDocId: '',
          creator: '',
          createDate: ''
        }
        // 开发人员请勿随意更改下列参数 例如各组件dataForm.id 必须赋值，因为组件会根据id判断是否清空表单
        // 初始化rue
        this.$refs.logisticsChannelRule.dataForm = this.dataForm.logisticsChannelRule || rule
        // 面单规则设置
        this.$refs.lableRule.dataForm = this.dataForm.logisticsChannelOther || lable
        this.$refs.lableRule.isLogisticsProduct = false
        this.$refs.lableRule.dataForm.lableType = this.dataForm.lableType || 0
        this.$refs.lableRule.dataForm.bagLabelSource = this.dataForm.bagLabelSource || 10
        this.$refs.lableRule.dataForm.postageProofLabelSource = this.dataForm.postageProofLabelSource || 10
        this.$refs.lableRule.dataForm.signProofLabelSource = this.dataForm.signProofLabelSource || 10
        this.$refs.lableRule.dataForm.bagNoSource = this.dataForm.bagNoSource || 10
        this.$refs.logisticsChannelRule.isLogisticsProduct = false
        // 订单校验模板设置
        this.$refs.orderVerifyTemplatePublic.isLogisticsChannel = true
        this.$refs.orderVerifyTemplatePublic.dataForm.id = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.id
        this.$refs.orderVerifyTemplatePublic.dataForm.orderVerifyId = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.orderVerifyId
        this.$refs.orderVerifyTemplatePublic.dataForm.provinceRegionCodeMatch = !this.dataForm.logisticsChannelOther ? 0 : this.dataForm.logisticsChannelOther.provinceRegionCodeMatch
        // 普通分区模板设置
        this.$refs.zoneTemplatePublic.dataForm.id = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.id
        this.$refs.zoneTemplatePublic.dataForm.zoneTemplateId = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.zoneTemplateId
        // 偏远分区模板设置
        this.$refs.remoteZoneTemplatePublic.dataForm.id = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.id
        this.$refs.remoteZoneTemplatePublic.dataForm.remoteZoneTemplateId = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.remoteZoneTemplateId
        // 轨迹抓取设置
        this.$refs.trackGetSetting.dataForm.id = this.dataForm.id
        this.$refs.trackGetSetting.dataForm.trackPlatformType = this.dataForm.trackPlatformType
        this.$refs.trackGetSetting.dataForm.carrierCode = this.dataForm.carrierCode
        this.$refs.trackGetSetting.dataForm.trackPushNode = this.dataForm.trackPushNode
        this.$refs.trackGetSetting.dataForm.trackNoSource = this.dataForm.trackNoSource
        // 计费设置
        this.$refs.rateSetting.dataForm.id = this.dataForm.id
        this.$refs.rateSetting.dataForm.quotationSource = this.dataForm.quotationSource
        this.setRateValue(this.dataForm.waybillNoSource)
        // 修正重量设置
        this.$refs.correctedWeight.dataForm.id = this.dataForm.id
        // 面单调整
        this.$refs.labelChange.dataForm.id = this.dataForm.id
        // 获取最终单号设置
        this.$refs.getFinalTrackNo.dataForm.id = this.dataForm.id
        this.$refs.getFinalTrackNo.dataForm.apiPushNode = this.dataForm.apiPushNode | 0
        this.$refs.getFinalTrackNo.dataForm.showProviderTransitionNo = this.dataForm.showProviderTransitionNo
        this.$refs.getFinalTrackNo.dataForm.providerTransitionNoRule = this.dataForm.providerTransitionNoRule
        this.$refs.getFinalTrackNo.dataForm.providerTransitionNoRuleRemark = this.dataForm.providerTransitionNoRuleRemark
        this.$refs.getFinalTrackNo.dataForm.getFinalTnoNode = this.dataForm.getFinalTnoNode
        this.$refs.getFinalTrackNo.dataForm.getFinalTnoDelayTime = this.dataForm.getFinalTnoDelayTime
        // 自定义预上网轨迹
        this.$refs.setInBoundPathItem.dataForm.id = this.dataForm.id
        this.$refs.setInBoundPathItem.dataForm.isEditInboundPath = this.dataForm.isEditInboundPath
        this.$refs.setInBoundPathItem.dataForm.pushInboundPathNode = this.dataForm.pushInboundPathNode
        // 运单号设置
        this.$refs.waybillNoSource.dataForm.id = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.id
        let isWaybillNoSourceNull = this.dataForm.waybillNoSource === '' || this.dataForm.waybillNoSource === null || this.dataForm.waybillNoSource === undefined
        this.$refs.waybillNoSource.dataForm.waybillNoSource = !isWaybillNoSourceNull ? this.dataForm.waybillNoSource : 1 // 默认为号段组
        this.$refs.waybillNoSource.dataForm.waybillNoGroupId = !this.dataForm.logisticsChannelOther ? '' : this.dataForm.logisticsChannelOther.waybillNoGroupId
        this.$refs.waybillNoSource.dataForm.apiTypeId = this.dataForm.apiTypeId
        this.$refs.waybillNoSource.dataForm.apiPushNodeArray = this.dataForm.apiPushNodeArray
        this.$refs.waybillNoSource.dataForm.apiRelatedSettingsArray = this.dataForm.apiRelatedSettingsArray
        this.$refs.waybillNoSource.dataForm.pushApiNoType = this.dataForm.pushApiNoType
        this.$refs.waybillNoSource.dataForm.apiDetailList = this.dataForm.apiDetailList
        this.$refs.waybillNoSource.isLogisticsProduct = false
        this.$nextTick(() => {
          this.$refs.logisticsChannelRule.init()
          this.$refs.lableRule.init()
          this.$refs.waybillNoSource.init()
          this.$refs.orderVerifyTemplatePublic.init()
          this.$refs.zoneTemplatePublic.init()
          this.$refs.remoteZoneTemplatePublic.init()
          this.$refs.rateSetting.init()
          this.$refs.correctedWeight.init()
          this.$refs.labelChange.init()
          this.$refs.getFinalTrackNo.init()
          this.$refs.setInBoundPathItem.init()
          // 回邮地址
          let returnMailingAddressPublicData
          if (this.dataForm.logisticsChannelOther) {
            returnMailingAddressPublicData = {
              'id': this.dataForm.logisticsChannelOther.id,
              'returnAddrId': this.dataForm.logisticsChannelOther.returnAddrId,
              'logisticsType': 12,
              'addrByTypeList': this.dataForm.logisticsChannelOther.addrByTypeList || []
            }
          } else {
            returnMailingAddressPublicData = {
              'id': this.dataForm.id,
              'returnAddrId': '',
              'logisticsType': 12,
              'addrByTypeList': []
            }
          }
          this.bdAddrTypeList.forEach(item => {
            let type = parseInt(item.dictValue)
            let typeArray = returnMailingAddressPublicData.addrByTypeList.map(item => item.type)
            if (typeArray.indexOf(type) < 0) {
              let typeAddItem = {
                type: type,
                addrId: ''
              }
              returnMailingAddressPublicData.addrByTypeList.push(typeAddItem)
            }
          })
          this.$refs.returnMailingAddressPublic.init(returnMailingAddressPublicData)

          // console.log('完成了')
        })
      }).catch(() => {})
    },
    // 同步更新计费设置里的单号来源
    setRateValue (waybillNoSource) {
      // 计费设置
      if (waybillNoSource !== 2) {
        this.dataForm.quotationSource = 10
        this.$refs.rateSetting.dataForm.quotationSource = this.dataForm.quotationSource
      }
      this.$refs.rateSetting.dataForm.waybillNoSource = waybillNoSource
    },
    async getDict () {
      // 获取相关字典
      this.logisticsChannelTypeList = await this.getDictTypeList('logisticsChannelType') // 渠道类型
      this.baseStatus = await this.getDictTypeList('baseStatus') // 渠道状态
      this.logisticsChannelSettingList = await this.getDictTypeList('logisticsChannelSetting') // 尾程派送渠道设置
      this.bdAddrTypeList = await this.getDictTypeList('BdAddrType')
    },
    async getBaseData () {
      // 服务商列表
      this.providerList = await baseData(api.providerList, { type: 2048 }).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      this.logisticsChannelTypeList = await baseData(api.channelTypeList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    statusName (val) {
      let name = ''
      this.logisticsChannelSettingList.forEach((item) => {
        if (item.dictValue === val) {
          name = item.dictName
        }
      })
      return name
    },

    isBadge (item) {
      let ret = true
      if (item.value === 70) {
        ret = false
        return ret
      }
      return ret
    },
    tabsClick (tab, event) {
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      console.log('提交')
      // 物流渠道规则验证
      // if (!this.$refs['logisticsChannelRule'].validated()) {
      this.$refs['logisticsChannelRule'].validated((valid, data) => {
        // console.log(55, valid)
        if (!valid) {
          this.$message({
            message: '物流渠道规则的必填项未填写或填写错误,请填写',
            type: 'warning',
            duration: 3000
          })
          return false
        }
        this.dataForm.logisticsChannelRule = data
        // if (this.dataForm.shippingAddressSource === 0) {
        if (!this.$refs['returnMailingAddressPublic'].validated()) {
          this.$message({
            message: '回邮地址的必填项未填写或填写错误,请填写',
            type: 'warning',
            duration: 3000
          })
          return false
        }
        // }
        if (
          !this.$refs['lableRule'].validated() || !this.$refs['waybillNoSource'].validated() ||
          !this.$refs['orderVerifyTemplatePublic'].validated() || !this.$refs['zoneTemplatePublic'].validated() ||
          !this.$refs['remoteZoneTemplatePublic'].validated() || !this.$refs['trackGetSetting'].validated() ||
          !this.$refs['rateSetting'].validated() ||
            !this.$refs['correctedWeight'].validated() ||
            !this.$refs['labelChange'].validated() ||
            !this.$refs['getFinalTrackNo'].validated() ||
            !this.$refs['setInBoundPathItem'].validated()
        ) {
          this.$message({
            message: '校验未通过',
            type: 'warning',
            duration: 3000
          })
          return false
        }
        this.dataForm.waybillNoSource = this.waybillNoSource.waybillNoSource
        if (this.dataForm.waybillNoSource !== 2) {
          this.dataForm.quotationSource = 10
        }
        this.dataForm.logisticsChannelOther.waybillNoGroupId = this.waybillNoSource.waybillNoGroupId
        this.dataForm.logisticsChannelOther.orderVerifyId = this.orderVerifyTemplate.orderVerifyId
        this.dataForm.logisticsChannelOther.provinceRegionCodeMatch = this.orderVerifyTemplate.provinceRegionCodeMatch
        this.dataForm.logisticsChannelOther.zoneTemplateId = this.zoneTemplate.zoneTemplateId
        this.dataForm.logisticsChannelOther.remoteZoneTemplateId = this.remoteZoneTemplate.remoteZoneTemplateId
        this.dataForm.logisticsChannelOther.returnAddrId = this.returnMailingAddress.returnAddrId
        this.dataForm.logisticsChannelOther.addrByTypeList = this.returnMailingAddress.addrByTypeList
        console.log('returnAddrId', this.returnMailingAddress.returnAddrId)
        this.dataForm.apiDetailList = this.waybillNoSource.apiDetailList
        this.dataForm.apiTypeId = this.waybillNoSource.apiTypeId
        this.dataForm.pushApiNoType = this.waybillNoSource.pushApiNoType
        this.dataForm.apiPushNodeArray = this.waybillNoSource.apiPushNodeArray
        this.dataForm.apiRelatedSettingsArray = this.waybillNoSource.apiRelatedSettingsArray
        this.dataForm.lableType = this.dataForm.logisticsChannelOther.lableType
        this.dataForm.bagNoSource = this.dataForm.logisticsChannelOther.bagNoSource
        this.dataForm.bagLabelSource = this.dataForm.logisticsChannelOther.bagLabelSource
        this.dataForm.postageProofLabelSource = this.dataForm.logisticsChannelOther.postageProofLabelSource
        this.dataForm.signProofLabelSource = this.dataForm.logisticsChannelOther.signProofLabelSource
        this.dataForm.logisticsChannelOther.earlyWarningInventory = this.waybillNoSource.earlyWarningInventory
        this.dataForm.logisticsChannelOther.earlyWarningContact = this.waybillNoSource.earlyWarningContact

        this.$http['post']('/bd/logisticschannel/setLogisticsChannelInfo', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$closeFn()
              this.$router.push({ name: `bd-logistics-channel` })
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    setReturnMailingAddressData () {
      return this.returnMailingAddress || {}
    },
    setLogisticsChannelRuleData () {
      return this.dataForm.logisticsChannelRule || {}
    },
    setLableRuleData () {
      return this.dataForm.logisticsChannelOther || {}
    },
    setWaybillNoSourceData () {
      return this.waybillNoSource || {}
    },
    setOrderVerifyTemplateData () {
      return this.orderVerifyTemplate || {}
    },
    setZoneTemplateData () {
      return this.zoneTemplate || {}
    },
    setRemoteZoneTemplateData () {
      return this.remoteZoneTemplate || {}
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet,
    logisticsProductChannelRule,
    lableRule,
    channelApi,
    waybillNoSource,
    orderVerifyTemplatePublic,
    zoneTemplatePublic,
    trackGetSetting,
    remoteZoneTemplatePublic,
    returnMailingAddressPublic,
    rateSetting,
    correctedWeight,
    labelChange,
    setInBoundPathItem,
    getFinalTrackNo
  }
}
</script>
