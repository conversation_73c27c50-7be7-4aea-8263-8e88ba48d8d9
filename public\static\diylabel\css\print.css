/*
* @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
* @Date:   2016-12-12 17:13:16
* @Last Modified by:   Administrator
* @Last Modified time: 2016-12-19 19:38:30
*/
body { font-family: "Microsoft YaHei";/*,arial,verdana,tahoma,sans-serif*/ min-height:100%; font-size:14px; margin:0; padding:0; }
.disn { display:none!important; }
.indexBody { background-color:#333;font-family: "Microsoft YaHei";}
.custom-header { position:fixed; top:0; left:0; right:0; background-color:#fff; height:65px; z-index:3;}
.label-type{ position:fixed; top:65px; bottom:0; left:0; width:200px; background-color:#fff; z-index:2; border-right:1px solid #cfd9db; border-top:1px solid #cfd9db;}
.label-type .panel-group .panel{ border-radius:0; border:1px solid #ddd; margin-top:-1px; border-right:0; background-color:#fff; overflow:inherit;}
.label-type .panel-group .panel + .panel{ margin:-1px 0 0;}
.label-type .panel-group .panel .panel-heading{border-radius:0;}
.label-type .panel-group .panel-body{ border:1px 0 0 !important; box-sizing:border-box; padding:0; text-align:center; overflow-y:auto; overflow-x:hidden;}
.label-type .title i.glyphicon{ margin-right:5px;}
.label-type .panel-heading>.panel-title{ font-size:14px; font-weight:600; line-height:32px;}
.label-type .panel-heading>.panel-title>a{ text-decoration:none; color:#666; padding:0!important;}
.label-type .panel-heading>.panel-title>a:hover{ color:#337ab7;}
.label-type .panel-heading{ padding:5px 15px;}

.dragitem{text-align:center; text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);  color:#6a6a6a; border:1px solid #cfd9db; border-radius:3px;  cursor:move;}
.dragitem:hover{border-color: #c6d2d5; color: #777;}
.dragitem .detail{ display:none;}
.dragitem.ui-draggable.ui-draggable-dragging{ z-index:10; }
.dragitem.character{ margin:10px; padding:5px 7px; background-color:#fff; text-align:left; position:relative; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.dragitem.character:hover{background-color: #ededed;}
.dragitem.character .title{font-size: 13px; line-height: 20px;}
.dragitem.character + .dragitem.character{margin-top: 5px;}
.dragitem .ico-checkbox-unchecked2, .dragitem .ico-checkbox{ display:none;}
.dragitem.draw{ width:60px; height:60px; display:inline-block; margin:10px; padding:3px; background-color:#f5f5f5;}
.dragitem.draw:hover{background-color: #ededed;}
.dragitem.draw i{ line-height:35px; font-size:24px;}
.dragitem.draw .title{font-size: 12px; line-height: 20px; display:block; text-align:center;}
.dragitem.image{ margin:10px auto; width:140px; padding:5px; background-color:#fff; text-align:center; position:relative; overflow: hidden;}
.dragitem.image img{ max-width:100%; max-height:50px;}
.dragitem.skulist .glyphicon-ok, .dragitem.declarelist .glyphicon-ok { display:none; }
.dragitem.hasSkulist { border-color:#3c763d; background-color:#dff0d8!important; }
.dragitem.hasSkulist .glyphicon-ok { display:block; }

.radio-weight-position, .radio-currency-position {
    position: relative;
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    vertical-align: middle;
    cursor: pointer;
}
.custom-label { position:absolute; left:0; right:0; bottom:0; top:0; z-index:1; overflow:auto;}
.label-content, .copy-label-content { position:absolute; left:50%; top:25%; z-index:1; display:none; border-radius:8px; box-shadow:0 0 10px rgba(0,0,0,.8); background-color:#F8F8F8; padding:4px; box-sizing:border-box; }
.label-content .viewCover { position:absolute; left:0; right:0; bottom:0; top:0; z-index:100; cursor:move; display:none; }
.label-content .ui-widget-content { border:1px solid #ddd; position:absolute; top:4px; right:4px; bottom:4px; left:4px; }
.label-content .ui-widget-content.active { border:1px dashed #2D9FFF; background:rgba(45, 159, 255, .2); }
.label-content .senditem { font-size:12px; line-height:1; cursor:pointer; position:absolute; overflow:hidden; font-family:"FZLanTingHei-L-GBK";}
/*".label-content .senditem "样式在生成PDF时因无.label-content的DIV无效，故添加".senditem"样式*/
.senditem { font-size:12px; line-height:1; cursor:pointer; position:absolute; overflow:hidden; font-family:"FZLanTingHei-L-GBK";}
.label-content .senditem .title { padding-right:5px; font-family: "FZLanTingHei-L-GBK"; text-align:left; }
.label-content .senditem .country_cn { display:none; }
.label-content .senditem:hover { background:#f5f5f5!important;color:#999; }
.label-content .senditem:hover .ui-icon { opacity:1!important; }
.label-content .senditem.active { -outline:1px solid rgba(45, 159, 255, .9); box-shadow:0 0 5px 1px #2d9fff; z-index:99; background-color:#fff;}
.label-content .senditem.active .ui-icon { opacity: 1; }
.label-content .senditem.barcode { overflow:inherit; font-size:0;}
.label-content .senditem.barcode img { max-width:100%; min-width:100px; min-height:28px; width:100%; height:100%; }
.label-content .senditem.barcode .codeNumber { padding-top:2px; position:absolute; display:block; width:100%; font-size:12px;}
.label-content .senditem.line-x { border-top:1px solid #000; left:0; right:0; overflow:inherit;background-color:#000000;height:1px; }
.label-content .senditem.line-x:after, .label-content .senditem.line-y:after { content:''; position:absolute; left:50%; top:50%; width:10px; height:10px; background:#2d9fff; margin-left:-5px; margin-top:-5px; border-radius:100%; cursor:pointer; opacity:0.35; }
.label-content .senditem.line-x:hover, .label-content .senditem.line-y:hover { border-color:#000!important;background-color:#000000; }
.label-content .senditem.line-x:hover:after, .label-content .senditem.line-x.active:after, .label-content .senditem.line-y:hover:after, .label-content .senditem.line-y.active:after { opacity:0.8; }
.label-content .senditem.line-y { border-left:1px solid #000; top:0; bottom:0; overflow:inherit;background-color:#000000;height:1px; }
.label-content .senditem.circletext { border:2px solid #000; line-height:50px; text-align:center; border-radius:100%; overflow:inherit; box-sizing:content-box; }
.label-content .senditem.circletext .detail { font-weight:bold;font-size:24px; }
.label-content .senditem.onlineimage { font-size:40px; text-align:center; }
.label-content .senditem.onlineimage img.placeholder, .label-content .senditem.onlineimage img.online { max-width:100%; max-height:100%; position:absolute; left:0; right:0; bottom:0; top:0; margin:auto; }
.label-content .senditem.imageitem img {width:100%; height:100%;}
.label-content .senditem.skulist, .label-content .senditem.declarelist { overflow:inherit; }

.view-panel{ display:none; position:absolute; top:20px; right:30px; height:34px; z-index:10;}
.view-panel-show{ position:absolute; top:20px; right:30px; height:34px; z-index:10;}

.skulist-table{ width:100%; border-collapse:collapse; table-layout:fixed; border-collapse: collapse; border-spacing: 0;}
.skulist-table th, .skulist-table td{ border:1px solid #000; border-style:solid; border-color:#000; box-sizing:border-box; padding:0;}
.skulist-table thead th > span, .skulist-table tbody td > span{ display:block; overflow:hidden;}
.skulist-table .price, .skulist-table .total{ text-align:right;}
.skulist-table .number, .skulist-table .weight,.skulist-table .total_qty_declare, .skulist-table .weight_declare, .skulist-table .price_declare,.skulist-table .qty_declare, .skulist-table .customsNo_declare{ text-align:center;}
.skulist-table .photo{ text-align:center; vertical-align:middle;}
.skulist-table.no-tdborder td, .skulist-table.no-tdborder th{ border-width:0;}
.skulist-table.min th{ padding:0 3px;}
.skulist-table.min tbody td{ height:30px;  padding:0 3px;}
.skulist-table.min tfoot td{ height:15px;  padding:0 3px;}
.skulist-table.min thead span{line-height:15px; height:15px; font-weight:normal; text-overflow: ellipsis; white-space: nowrap;overflow:hidden;}
.skulist-table.min tfoot { font-size:12px; text-align:right; }
.skulist-table.min tfoot p{overflow: hidden;text-overflow: ellipsis; white-space: nowrap; display:block;line-height:15px; height:15px; padding:0; margin:0;}
.skulist-table.min tbody span{overflow: hidden; line-height:15px; height:30px; display:block;}
.skulist-table.min .photo{width:30px; padding:0;}
.skulist-table.min .photo img{ max-height:30px; max-width:30px;}
.skulist-table.declare tfoot th{ height:40px;}
.skulist-table.declare thead th{ height:30px;}
.skulist-table.declare tbody td,.skulist-table.declare tfoot td{ height:15px;}
.skulist-table.declare thead th > span{line-height:15px; height:30px; font-weight:normal; }
.skulist-table.declare tbody td > span{line-height:15px;}
.skulist-table.declare tbody td > span span { padding-right:5px;}
.skulist-table.declare tfoot th > span{ font-weight:normal;line-height:13px; height:40px; display:block; overflow:hidden;}
.skulist-table.declare tfoot td > span{line-height:15px; height:15px; overflow:hidden;}
.copy-drop .skulist-table.declare  tbody .weight_declare, .copy-drop .skulist-table.declare  tbody .price_declare{ display:none;}
.hotkey-panel{ position:absolute; left:220px; bottom:20px; border:2px solid #D9EDF7; border-radius:5px; background-color:#D9EDF7; padding:8px; width:180px; -webkit-box-shadow: 0 0 10px #999; -moz-box-shadow: 0 0 10px #999;box-shadow: 0 0 10px #999; z-index:3; -webkit-opacity:0; -moz-opacity:0; opacity:0;transition: all 0.5s ease 0s;}
.hotkey-panel .close-hotkey{color:#999; font-size: 20px; right:8px; position: absolute; top:11px;transition: all 0.5s ease 0s;}
.hotkey-panel .close-hotkey:hover{ color:#333;}
.hotkey-panel h3{ font-size:18px; margin-top:0; padding-bottom:5px; color:#2d9fff; font-weight:bold; }
.hotkey-panel ul{ padding:0; list-style:none; margin:0;}
.hotkey-panel li{ height:22px; display:inline-block; margin:0 8px 5px 0; color:#333; }
.hotkey-panel li strong{ padding-left:5px; color:#333; }
.key{margin:0 5px; padding:0 5px; font-weight:bold; text-align: center; font-size:11px; font-family:"FZLanTingHei-L-GBK";/*Tahoma, Geneva, sans-serif;*/color: #2d9fff;background: #EFF0F2; border-top: 1px solid #F5F5F5; text-shadow: 0px 1px 0px #F5F5F5; -webkit-box-shadow: inset 0 0 15px #eee, 0 1px 0 #c3c3c3, 0 2px 0 #c9c9c9, 0 2px 3px #333; -moz-box-shadow: inset 0 0 25px #eee, 0 1px 0 #c3c3c3, 0 2px 0 #c9c9c9, 0 2px 3px #333;box-shadow: inset 0 0 25px #eee, 0 1px 0 #c3c3c3, 0 2px 0 #c9c9c9, 0 2px 3px #333; display: inline-block;border-radius: 1px;}

.label-set{ transition:all 0.2s ease 0s; position:fixed; top:65px; bottom:0; right:-240px; z-index:2; width:240px; background-color:#fff; border-left:1px solid #cfd9db; border-top:1px solid #cfd9db; overflow-y:auto; overflow-x:hidden; opacity:0.5; -webkit-opacity:0.5; -moz-opacity:0.5;}
.label-set.opened{ right:0; opacity:1; -webkit-opacity:1; -moz-opacity:1;}
.label-set .close-label-set{ position:absolute; top:8px; left:10px; font-size:2em; color:#ccc; transition:all 0.5s ease 0s; z-index:2;}
.label-set .edit-btn{ display:block; margin:0 auto 10px; width:210px; overflow:hidden; text-align:center;}
.label-set .edit-btn .btn{ padding-left:28px; padding-right:28px;}
.label-set .group-warning{ margin:0 15px 10px;}
.label-set .panel { border-radius: 0; border-width: 0; position:relative; z-index:1; margin-bottom:0;}
.label-set .panel .nav-tabs li{ display:none;}
.label-set .panel .nav-tabs li.appear { display:table-cell;}
.label-set .nav-tabs.nav-justified>li>a:focus {background:#fff;}
.label-set .panel .panel-body{ padding:10px 15px 5px;}
.label-set .panel .panel-heading { margin-bottom:10px; }
.label-set .panel .panel-heading p{text-overflow: ellipsis; white-space: nowrap; overflow: hidden; padding:8px 10px; color:#2D9FFF; font-weight:bold;}
.label-set .panel .form-group{ margin-bottom:10px;}
.label-set .panel .form-control{ padding:0px 5px; border-radius:3px; height: 24px;}
.label-set .panel .td-group{height: 24px;margin-top:5px; }
.label-set .panel .td-group .form-control-select{left: 0px;top: 0px;width: 60%; }
.label-set .panel .td-group .form-text{ text-align: center; width: 40%;}
.label-set .panel .form-group .customnum{ height:24px;}
.label-set .panel .form-group .customnum .subtract, .label-set .panel .form-group .customnum .add{ top:2px;}
.label-set .panel .form-group .customnum input,.barcodeWidth input{ display:inline-block; width:50px; vertical-align:middle; }
.label-set .panel .form-group .btn{ padding:0 5px; height: 24px;}
.label-set .panel textarea.form-control{ height:70px;}
.label-set .panel .form-group .customnum a.subtract,.label-set .panel .form-group .customnum a.add{ top:0; font-size: 12px; font-weight: bold;}
.label-set .form-control[disabled], .label-set .form-control[readonly], .label-set fieldset[disabled] .form-control {	background-color: #eee; cursor:not-allowed; }
.viewtype .label-type, .viewtype .label-set, .viewtype .custom-header,.viewtype .hotkey-panel, .viewtype .senditem.line-x:after, .viewtype .senditem.line-y:after{ display:none;}
.multiple .checkbox-inline { display:block; margin-left:10px; }
.ico-resize-horizontal::before { content: '——'; }
.ico-resize-vertical:before { content: '|'; }
.ico-checkbox-unchecked, .ico-checkbox,.ico-checkbox-remove { width:13px; height:13px; line-height:1!important; margin-right:2px; position:relative; border:1px solid #A5A5A5; background-color:#DEDEDE; border-radius:2px; display:inline-block; vertical-align:middle; font-family:/*'Glyphicons Halflings';*/"FZLanTingHei-L-GBK";}
.ico-checkbox:before { content:'\e013'; position:absolute; width:12px; height:12px; left:-1px; top:0; font-style:normal; font-size:12px!important; }
.ico-checkbox-remove:before { content:'\e014'; position:absolute; width:12px; height:12px; left:-1px; top:0; font-style:normal; font-size:12px!important; }
.scale150{ -webkit-transform:scale(1.5); -moz-transform:scale(1.5); transform:scale(1.5);}
.scale200{ -webkit-transform:scale(2); -moz-transform:scale(2); transform:scale(2);}
.scale300{ -webkit-transform:scale(3); -moz-transform:scale(3); transform:scale(3);}
#textDetail, #option-field-declare textarea {resize:vertical; min-height:80px;}

/*******rewrite plugins css********/
.ui-widget-content .ui-icon { opacity:0; }


/*******printPreview********/
.printPreviewBody .label-content { box-shadow:none; background-color:#fff; padding:0; border-radius:0; top:0!important; left:0!important; right:0; bottom:0; margin:0 auto; margin-left:auto!important; }
.printPreviewBody .label-content .ui-widget-content { border:none; }
.printPreviewBody .senditem { cursor:default;}
.printPreviewBody .senditem:hover { background:#fff!important; color:#000; }
.printPreviewBody .senditem.active { outline:none; box-shadow:none; }
.printPreviewBody .senditem.line-x:after, .printPreviewBody .senditem.line-y:after { display:none; }
.printPreviewBody .senditem.skulist { overflow:inherit; }
.printPreviewBody .copy-label-content { box-shadow:none; background-color:#fff; }

.default-barcode{height:40px;line-height:40px;font-family: 'Code 128';font-size:55%;overflow: hidden;}
