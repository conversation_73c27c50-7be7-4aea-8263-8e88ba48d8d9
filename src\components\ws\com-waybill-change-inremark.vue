<template>
  <div>
    <el-dialog :title="$t('wsComWaybill.changeInRemark')" :visible.sync="visible" width="40%" style="min-width: 300px;" top="10px" :append-to-body="false"
        :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true" :before-close="backFunc" class="location_model">
      <el-form ref="dataForm" :model="dataForm"  @submit.native.prevent label-width="100px">
        <el-row :gutter="20" type="flex" justify="center" style="padding-top: 10px">
          <el-col :span="24">
              <el-form-item :label="$t('wsComWaybill.inRemark')" prop="inRemark">
                <el-input type="textarea" v-model="dataForm.inRemark" :placeholder="$t('wsComWaybill.inRemark')" :autosize="{ minRows: 5, maxRows: 8}" :maxlength="255" show-word-limit/>
              </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="loading" @click="submitHandle">{{$t('btn.submit')}}</el-button>
              <el-button @click="backFunc">{{ $t('cancel') }}</el-button>
            </el-form-item>
            </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>

export default {
  data () {
    return {
      dataForm: {
        id: '',
        inRemark: ''
      },
      isSuccess: true,
      dataList: [],
      loading: false,
      visible: false
    }
  },
  created () {
  },
  methods: {
    init (data) {
      this.dataForm = { ...data }
      this.loading = false
    },
    // 更新入库备注
    submitHandle () {
      this.$http.post('/ws/comwaybill/updateInRemark', this.dataForm).then(({ data: res }) => {
        // console.log('res', res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            // console.log('onClose')
            this.$emit('callback')
          }
        })
      }).catch(() => {})
    },
    backFunc () {
      this.visible = false
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
