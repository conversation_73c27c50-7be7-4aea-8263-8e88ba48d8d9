import t from './sys/zh-CN'

/* 此处写自定义的国际化 */
/* 公用 */
t.back = '返回'
t.close = '关闭'
t.fastMenu = '快捷菜单'
t.startTime = '开始时间'
t.endTime = '结束时间'
t.startDate = '开始日期'
t.endDate = '结束日期'
t.inputCountryNameOrCode = '请输入国家二字码，或国家名称'
t.baseInfo = '基础信息'
t.pushData = '数据推送'
t.yes = '是'
t.no = '否'
t.batchPrint = '批量打印'
t.batchDownloadLabel = '批量下载面单'
t.deletedCancel = '取消删除'
t.completeDeleted = '彻底删除'
t.trackByHand = '手工轨迹跟踪'
t.cancelForecast = '取消预报'
t.formulaDescription = '公式说明'
t.description = '说明'
t.currentPageSummary = '当前页合计'
t.descriptionMessage = descriptionMessage()

function descriptionMessage () {
  return '     1. 本尺寸体系支持 加（+）减（-）乘（*）除（/）四则运算。<br/> ' +
    '                 2. 可以含括号()(注：仅支持半角字符)。<br/> ' +
    '                 3. 公式中要分段， 不能使用 值1 < 变量 <= 值2  要是用 变量 > 值1 && 变量 <= 值2 <br/> ' +
    '                 4. && 表示并且。 只有两个条件为真整个条件才为真。<br/> ' +
    '                 6. == 表示相等。 左右相等条件为真。 <br/> ' +
    '                 5. || 表示或者。 只要两个条件中一个条件为真那么整个条件就为真。 <br/> ' +
    '                 7. L代表长，WD代表宽，H代表高，WG代表实重，VW代表材积重，P代表单票件数。 <br/> ' +
    '                 8. 变量之间不能连在一起。错误公式：LWH>200 <br/> ' +
    '                 9. 重量单位：kg,长宽高单位：cm <br/> ' +
    '                 10. 公式案例： <br/> ' +
    '      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ' +
    '      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1、(L+WD+H<100)&&WG<30 <br/> ' +
    '      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ' +
    '      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、L>100||(WD>50&&H>30) <br/> '
}

/* 上传 */
t.upload.sizeMsg = '上传文件大小不能超过{size}MB！'
t.upload.imageType = '上传图片只能是 JPG或PNG 格式!'

/* 公告 */
t.backList = '返回列表'

t.threeNoInput = {}
t.threeNoInput.outMessage = '单号不能超过{size}个'
t.threeNoInput.no = '单号'
t.threeNoInput.subno = '子单号'

/* 汇率 */
t.baCurrencyRate.status = '状态'
t.baCurrencyRate.noEnable = '未启用'
t.baCurrencyRate.allStatus = '所有'
t.baCurrencyRate.validateRate = '最多保留四位小数'

/* 运单号库 BdTrackingNo */
t.bdTrackingNo.startSerialNo = '起始序号'
t.bdTrackingNo.generateRecordCount = '生成记录数'
t.bdTrackingNo.add = '生成运单号'
t.bdTrackingNo.depose = '作废'
t.bdTrackingNo.delete = '删除'
t.bdTrackingNo.export = '导出'
t.bdTrackingNo.select = '请选择'
t.bdTrackingNo.cancel = '取消'
t.bdTrackingNo.confirm = '确定'
t.bdTrackingNo.nonSelectProduct = '未选择产品'
t.validate.bdTrackingNoRequiredPrefix = '前缀必须在3到4位'
t.validate.generateRecordCount = '最大值为100000'

/* 物流产品外部编码 BdLogisticsProductOuterCode */
t.validate.bdLogisticsProductOuterCodeLengthOuterCode = '外部编码长度在 1 到 32 个字符'
t.validate.bdLogisticsProductOuterCodeLengthOuterName = '外部编码名称长度在 1 到 64 个字符'

/* 港口 BdSeaport */

t.validate.bdSeaportCode = '港口代号长度在 1 到 5 个字符'
t.validate.bdSeaportCodeExist = '港口代号已存在'
t.validate.bdSeaportName = '中文名长度在 1 到 64 个字符'
t.validate.bdSeaportEnglishName = '英文名长度在 1 到 64 个字符'

/* 船公司 */
t.bdShippingCompany.export = '导出'

/* 空运航班 WsFlight */
t.wsFlight = {}
t.wsFlight.id = 'ID'
t.wsFlight.flightNo = '航班号'
t.wsFlight.airlinesCompany = '航空公司'
t.wsFlight.airRoutes = '空运航线'
t.wsFlight.type = '类型'
t.wsFlight.takeoffAirport = '起运机场'
t.wsFlight.transferAirport = '中转机场'
t.wsFlight.landingAirport = '降落机场'
t.wsFlight.takeoffDate = '起飞时间'
t.wsFlight.landingDate = '降落时间'
t.wsFlight.memo = '备注'
t.wsFlight.status = '状态'

/* 海运航次 WsVoyageNumber */
t.validate.wsVoyageNumberVoyageNoLength = '航次长度在 1 到 32 个字符'
t.validate.wsVoyageNumberVoyageNoAndCustomsClearanceDateIsExist = '该航次已经存在相同开船时间的其他海运航次'
t.validate.checkSailingDateGteCustomsClearanceDate = '截关时间必须小于等于开船时间'
t.validate.checkDeparturePortAndArrivalPortIsEqual = '起运港与目的港不能相同'
t.wsVoyageNumber.status = '状态'

/**
 * 表单检验
 */
t.validate.isCode = '只能输入3-32个字母、数字、下划线'
t.validate.isLength10 = '只能输入1-10位长度'
t.validate.isLength64 = '只能输入1-64位长度'
t.validate.isLength32 = '只能输入1-32位长度'
t.validate.isOverLength = '只能输入1-{max}位长度'
t.validate.whoIsOverLength = '{who}只能输入1-{max}位长度'
t.validate.minCharacters = '至少{min}个字符'
t.validate.requireNonZeroPositiveIntegers = '要求非零正整数'
t.validate.url = '请输入正确的URL地址，如http://www.XXX.com'
t.validate.existSpaces = '存在空格'
t.validate.cannotEndWithAComma = '不能以逗号结尾'
t.validate.extraComma = '出现多余逗号'
t.validate.isLength255 = '只能输入1-255位长度'
t.validate.isLength512 = '只能输入1-512位长度'
t.validate.isNonNegative = '输入的数字必须大于等于0'
t.validate.errorEmail = '邮箱格式错误'
t.validate.isNonNegativeAndRange = '只能输入数字,并且>={beginValue},<={endValue}'
t.validate.decimal = '最多保留{number}位小数'
t.validate.positiveInteger = '只能输入正整数'
t.validate.strRange = '只能输入{min}-{max}个字符'
t.validate.numberAndLetter = '只能输入数字和字母'
t.validate.numberAndLetterAndHyphen = '只能输入数字,字母,中横线'
t.validate.lengthRegex = '长宽高格式错误,只能输入正数,最多保留1位小数'
t.validate.weightRegex = '重量格式错误,只能输入正数,最多保留3位小数'
t.validate.notChinese = '不能输入中文和特殊符号,只能允许英文字母、数字、中划线、下划线和空格'
t.validate.letterAndNumberLine = '只能用字母、数字、中划线和下划线组合'
t.validate.notInputChinese = '不能输入中文'

t.bdAirlineCompany.status = '状态'
t.bdAirlineCompany.unaudit = '弃审'
t.bdAirlineCompany.edit = '编辑'
t.bdAirlineCompany.delete = '删除'
t.bdAirlineCompany.audit = '审核'
t.bdAirlineCompany.copy = '复制'
t.bdAirlineCompany.close = '关闭'

/* 容器称重 */
t.bagWWeighing = {}
t.bagWWeighing.bagNo = '容器号'
t.bagWWeighing.bagNoPlaceholder = '请扫描或输入容器号（袋/箱/托）'
t.bagWWeighing.grossWeight = '容器重(KG)'
t.bagWWeighing.grossWeightPlaceholder = '请输入称重的容器毛重（kg)'
t.validate.bagWeighingGrossWeightLength = '容器毛重长度在 1 到 20 个字符'
t.validate.bagWeighingGrossWeightDecimal = '容器毛重必须为非0正整数或1-3位小数'
t.bagWWeighing.bagNoIsNotExist = '出库容器为待出库的容器号信息不存在'
t.bagWWeighing.thereIsNoSuchBag = '没有此容器号'
t.bagWWeighing.notWaitOutWarehouseStatus = '此容器号不是待出库状态'
t.validate.bagWWeighingBagNoIsNotExist = '出库容器为未完结且待出库的容器号信息不存在'

/* 电子秤 */
t.electronicScale = {}
t.electronicScale.noConnectElectronicScale = '如已连接电子秤,重量还显示为0,则需刷新页面重新连接电子秤'
t.electronicScale.currentWeight = '当前秤重(KG):'

/* 加盟商容器称重 */
t.bagWeighing = {}
t.bagWeighing.noMessage = '容器号不存在，请确认容器号'
/* 自动化分拣-设备管理 */
t.srtSortingEquipment = {}
t.srtSortingEquipment.serialNo = '序号'
t.srtSortingEquipment.warehouseId = '所属仓库'
t.srtSortingEquipment.userId = '管理员'
t.srtSortingEquipment.equipmentNo = '设备编号'
t.srtSortingEquipment.equipmentName = '设备名称'
t.srtSortingEquipment.sortType = '分拣类型'
t.srtSortingEquipment.servicePointId = '所在网点'
t.srtSortingEquipment.planId = '计划编号'
t.srtSortingEquipment.boundPlan = '已绑定分拣计划'
t.srtSortingEquipment.status = '状态'
t.srtSortingEquipment.remarks = '备注'
t.srtSortingEquipment.updater = '更新人'
t.srtSortingEquipment.updateDate = '更新时间'
t.srtSortingEquipment.addEquipment = '新增设备'
t.srtSortingEquipment.inputEquipmentId = '请输入设备ID'
t.srtSortingEquipment.inputEquipmentNo = '请输入设备编号'
t.srtSortingEquipment.inputEquipmentName = '请输入设备名称'
t.srtSortingEquipment.inputPlan = '请输入分拣计划'

/* 自动化分拣-计划设置 */
t.srtSortingPlan = {}
t.srtSortingPlan.warehouseId = '仓库'
t.srtSortingPlan.planName = '分拣计划'
t.srtSortingPlan.status = '状态'
t.srtSortingPlan.sortingRule = '分拣规则'
t.srtSortingPlan.remarks = '备注'
t.srtSortingPlan.updateDate = '更新时间'
t.srtSortingPlan.updater = '更新人'
t.srtSortingPlan.addPlan = '新增计划'
t.srtSortingPlan.latticeSetting = '格口设置'

/* 自动化分拣-计划设置 */
t.srtSortingLattice = {}
t.srtSortingLattice.sortingLattice = '格口'
t.srtSortingLattice.matchingCondition = '匹配条件'
t.srtSortingLattice.matchingRemark = '匹配条件描述'
t.srtSortingLattice.addLattice = '新增格口'
t.srtSortingLattice.addLatticeByRegex = '新增格口(单号正则)'
t.srtSortingLattice.sortingRule = '当前分拣规则：'

/* 包裹装袋 */
t.parcelBagging = {}
t.parcelBagging.bagNo = '容器号'
t.parcelBagging.serialNo = '序号'
t.parcelBagging.providerId = '供应商'
t.parcelBagging.waybillNo = '物流公司单号'
t.parcelBagging.customerOrderNo = '客户单号'
t.parcelBagging.deliveryNo = '派送单号'
t.parcelBagging.boxNo = '箱号/FBA唛头'
t.parcelBagging.customer = '客户'
t.parcelBagging.reopenContainer = '重新打开'
t.parcelBagging.continueLoading = '继续装货'
t.parcelBagging.pushProvider = '大包API推送'
t.parcelBagging.cancelPushProvider = '大包API取消'
t.parcelBagging.parcelApiUpdate = '包裹API更新'
t.parcelBagging.objectName = '客户名称'
t.parcelBagging.logisticsProductCode = '物流产品'
t.parcelBagging.weight = '包裹实重(KG)'
t.parcelBagging.materialsCodeDialog = '物料编号'
t.parcelBagging.helpInfo = '<b>须知：</b><br>物流产品和限定渠道只能二选一，若没有选择限定渠道则袋牌必选!<br/>同时，限定的渠道设置要绑定袋牌！'
t.parcelBagging.helpInfoTitle = '须知：'
t.parcelBagging.iKnow = '我知道了'
t.parcelBagging.limit = '限定条件'

t.parcelBagging.totalVotes = '总件数'
t.parcelBagging.totalWeight = '总重量'
t.parcelBagging.totalVotesFailed = '失败数'
t.parcelBagging.totalVolume = '方数'
t.parcelBagging.logisticsChannelCode = '渠道'
t.parcelBagging.baggingDetails = '装袋明细清单'

t.parcelBagging.bagNoPlaceholder = '扫描未关闭容器号或创建新容器号'
t.parcelBagging.bagNoPlaceholder2 = '填写未关闭容器号或创建新容器号'
t.parcelBagging.deliveryNoPlaceholder = '扫描派送单号'
t.parcelBagging.boxNoPlaceholder = '扫描FBA唛头号/客户箱号'

t.parcelBagging.createBagNoButton = '创建'
t.parcelBagging.copyBagNoButton = '复制容器号'
t.parcelBagging.printBagNoButton = '打印标签'
t.parcelBagging.cancelBagNoButton = '撤销装入'
t.parcelBagging.batchCancelBagNoButton = '批量撤销装入'
t.parcelBagging.finishBagNoButton = '完结容器'
t.parcelBagging.finishBagNoAndBatchOutButton = '完结容器并出库'
t.parcelBagging.batchPutInBag = '批量装入'
t.parcelBagging.generateBagNoButton = '获取容器号'
t.parcelBagging.reviewBoxNoAndDeliveryNo = 'FBA复核派送单号与箱号'
t.parcelBagging.CustomersLimit = '限制客户'
t.parcelBagging.printBoxLabelAfterSealingContainer = '完结容器后打印容器标签'

t.parcelBagging.bagNoDialog = '容器号'
t.parcelBagging.materielsCodeDialog = '物料名称'
t.parcelBagging.NoLimitDialog = '不限定'
t.parcelBagging.deliveryChannelCodeDialog = '物流渠道'
t.parcelBagging.logisticsProductCodeDialog = '物流产品'
t.parcelBagging.logisticsProductAddProviderDialog = '物流产品+供应商'
t.parcelBagging.deliveryChannelZoneDialog = '渠道分区'
t.parcelBagging.bagCardDialog = '袋牌标签'
t.parcelBagging.limitWeight = '限重(KG)'
t.parcelBagging.remark = '备注'

t.parcelBagging.bagNoDialogPlaceholder = '支持自动创建和手动创建'

t.validate.parcelBaggingBagNoIsExist = '出库容器信息已存在'
t.parcelBagging.bagNoIsExist = '出库容器信息已存在'
t.parcelBagging.bagNoIsNotBlank = '容器号不能为空'

/* 供应商文件 BdProviderDocument */
t.bdProviderDocument = {}
t.bdProviderDocument.id = 'ID'
t.bdProviderDocument.providerId = '供应商ID'
t.bdProviderDocument.credentialsFile = '工商执照/身份证文件'
t.bdProviderDocument.text = '工商信息'

/* 分区模板 BdZoneTemplate */
t.bdZoneTemplate.zoneDetailMaintain = '分区明细维护'
t.bdZoneTemplate.templateName = '模板名称'

/* 运单操作日志 WsComWaybillOperateLog */
t.wsComWaybillOperateLog = {}
t.wsComWaybillOperateLog.id = 'ID'
t.wsComWaybillOperateLog.waybillId = '运单编号'
t.wsComWaybillOperateLog.operateNode = '操作节点'
t.wsComWaybillOperateLog.opeareteDescription = '操作描述'

/* 运单 WsComWaybill */
t.wsComWaybill = {}
t.wsComWaybill.id = 'ID'
t.wsComWaybill.info = '运单信息'
t.wsComWaybill.cust = '客户信息'
t.wsComWaybill.orderLogisticsType = '订单物流类型'
t.wsComWaybill.mergeType = '运单类型'
t.wsComWaybill.exception = '异常单详情'
t.wsComWaybill.log = '操作日志'
t.wsComWaybill.orderId = '订单编号'
t.wsComWaybill.subDeliveryNo = '子单派送单号'
t.wsComWaybill.subPackageNo = '箱号/FBA唛头'
t.wsComWaybill.customerOrderNo = '客户单号'
t.wsComWaybill.waybillNo = '运单号'
t.wsComWaybill.postalTrackingNo = '邮政单号'
t.wsComWaybill.deliveryNo = '派送单号'
t.wsComWaybill.originalDeliveryNo = '原派送单号'
t.wsComWaybill.logisticsProductCode = '物流产品'
t.wsComWaybill.logisticsChannelCode = '物流渠道'
t.wsComWaybill.providerCode = '供应商'
t.wsComWaybill.forecastWeight = '预报重'
t.wsComWaybill.weight = '入库重(KG)'
t.wsComWaybill.measureWeightD = '入库操作重(KG)'
t.wsComWaybill.forecastVolumeWeight = '预报体积重'
t.wsComWaybill.volumeWeight = '入库体积重(KG)'
t.wsComWaybill.volume = '入库方数'
t.wsComWaybill.inPackageQty = '入库包裹数'
t.wsComWaybill.outPackageQty = '出库包裹数'
t.wsComWaybill.length = '长(CM)'
t.wsComWaybill.width = '宽(CM)'
t.wsComWaybill.height = '高(CM)'
t.wsComWaybill.inAdjustWeight = '入库重(KG)'
t.wsComWaybill.outAdjustWeight = '出库重(KG)'
t.wsComWaybill.weightUnit = '重量单位'
t.wsComWaybill.storageLocation = '异常上架库位'
t.wsComWaybill.goodsCategory = '货物类别'
t.wsComWaybill.parcelType = '包裹类型'
t.wsComWaybill.electric = '是否带电'
t.wsComWaybill.magnetized = '是否带磁'
t.wsComWaybill.liquid = '是否液体'
t.wsComWaybill.powder = '是否粉末'
t.wsComWaybill.remote = '是否偏远'
t.wsComWaybill.consigneeCountry = '收货国家'
t.wsComWaybill.consigneePostcode = '收货邮编'
t.wsComWaybill.customerRemark = '客户备注'
t.wsComWaybill.serviceId = '客服'
t.wsComWaybill.salesmanId = '业务员'
t.wsComWaybill.objectId = '客户'
t.wsComWaybill.objectName = '客户名称'
t.wsComWaybill.objectCode = '客户对象代号'
t.wsComWaybill.objectType = '对象类别'
t.wsComWaybill.warehouseId = '所属仓库'
t.wsComWaybill.plateformType = '来源平台'
t.wsComWaybill.packageQty = '包裹数'
t.wsComWaybill.exceptionFlag = '是否异常'
t.wsComWaybill.arrearage = '是否欠费'
t.wsComWaybill.addressInfo = '收寄件人信息'
t.wsComWaybill.declareInfo = '申报商品信息'
t.wsComWaybill.TrackInfo = '运单轨迹信息'
t.wsComWaybill.exceptionInfo = '异常单信息'
t.wsComWaybill.remark = '备注'
t.wsComWaybill.inRemark = '入库备注'
t.wsComWaybill.status = '运单状态'
t.wsComWaybill.pushOrderToProviderStatus = '同步状态'
t.wsComWaybill.pushOrderToProviderErrorMessage = '错误信息'
t.wsComWaybill.pushManifestToProviderStatus = '发送货物清单状态'
t.wsComWaybill.pushManifestToProviderErrorMessage = '发送货物清单错误信息'
t.wsComWaybill.uploadInvoiceToProviderStatus = '上传发票状态'
t.wsComWaybill.uploadInvoiceToProviderErrorMessage = '上传发票错误信息'
t.wsComWaybill.updateCustomerOrderStatus = '同步状态'
t.wsComWaybill.outToPushCustomerStatus = '同步状态'
t.wsComWaybill.updateCustomerOrderErrorMessage = '错误信息'
t.wsComWaybill.outToPushCustomerErrorMessage = '错误信息'
t.wsComWaybill.getFinalTnoStatus = '获取单号状态'
t.wsComWaybill.getFinalTnoErrorMessage = '错误信息'
t.wsComWaybill.createDate = '创建时间'
t.wsComWaybill.diffWeight = '重量误差'
t.wsComWaybill.waybillTotalCount = '运单总数'
t.wsComWaybill.volume = '入库方数'
t.wsComWaybill.forecastDate = '预报时间'
t.wsComWaybill.inWarehouseDate = '入库时间'
t.wsComWaybill.outWarehouseDate = '出库时间'
t.wsComWaybill.customerPhone = '客户联系方式'
t.wsComWaybill.customerContact = '客户联系人'
t.wsComWaybill.logisticsChannelNew = '新渠道'
t.wsComWaybill.logisticsProductNew = '新产品'
t.wsComWaybill.operator = '操作人'
t.wsComWaybill.inedQty = '已入库'
t.wsComWaybill.asynNoStatus = '异步单号状态'

t.wsComWaybill.shipper = '发货人'
t.wsComWaybill.shipperName = '姓名'
t.wsComWaybill.shipperCompany = '公司'
t.wsComWaybill.shipperPhone = '电话'
t.wsComWaybill.shipperEmail = 'Email'
t.wsComWaybill.shipperCountry = '国家'
t.wsComWaybill.shipperProvince = '省/州'
t.wsComWaybill.shipperCity = '城市'
t.wsComWaybill.shipperDistrict = '区'
t.wsComWaybill.shipperAddress = '地址'
t.wsComWaybill.shipperPostcode = '邮编'
t.wsComWaybill.shipperDoorplate = '门牌'
t.wsComWaybill.shipperStreet = '街道'

t.wsComWaybill.consignee = '收货人'
t.wsComWaybill.consigneeName = '姓名'
t.wsComWaybill.consigneeCompany = '公司'
t.wsComWaybill.consigneePhone = '电话'
t.wsComWaybill.consigneeEmail = 'Email'
// t.wsComWaybill.consigneeCountry = '国家'
t.wsComWaybill.consigneeProvince = '省/州'
t.wsComWaybill.consigneeCity = '城市'
t.wsComWaybill.consigneeDistrict = '区'
t.wsComWaybill.consigneeAddress = '地址'
t.wsComWaybill.consigneePostcode = '收货邮编'
t.wsComWaybill.consigneeDoorplate = '门牌'
t.wsComWaybill.consigneeStreet = '街道'
t.wsComWaybill.consigneeIdcard = '证件号'

t.wsComWaybill.declare = {}
t.wsComWaybill.declare.orderId = '订单编号'
t.wsComWaybill.declare.chineseName = '中文品名'
t.wsComWaybill.declare.englishName = '英文品名'
t.wsComWaybill.declare.quantity = '数量'
t.wsComWaybill.declare.unitNetWeight = '单位净重(KG)'
t.wsComWaybill.declare.unitDeclarePrice = '申报单价'
t.wsComWaybill.declare.declareSum = '申报总价'
t.wsComWaybill.declare.brand = '品牌'
t.wsComWaybill.declare.goodsBarcode = '货物条码'
t.wsComWaybill.declare.sku = 'SKU'
t.wsComWaybill.declare.hsCode = '海关编码'
t.wsComWaybill.declare.productModel = '产品型号'
t.wsComWaybill.declare.material = '材质'
t.wsComWaybill.declare.purpose = '用途'
t.wsComWaybill.declare.origin = '原产地'
t.wsComWaybill.declare.pickingRemark = '配货备注'
t.wsComWaybill.declare.productUrl = '商品URL'
t.wsComWaybill.declare.picUrl = '图片'

t.wsComWaybill.batchIntercept = '批量拦截'
t.wsComWaybill.interceptOpeareteDescription = '批量拦截'
t.wsComWaybill.changeChannel = '强制变更渠道'
t.wsComWaybill.pushOrderToProvider = '同步订单到供应商'
t.wsComWaybill.sendShipmentManifests = '发送货物清单'
t.wsComWaybill.uploadInvoice = '上传发票'
t.wsComWaybill.getFinalTnoByProvider = '获取最终尾程单号'
t.wsComWaybill.pushOrderInfoToCustomer = '推送订单信息到客户'
t.wsComWaybill.inAndPushToCustomer = '入仓信息推送客户'
t.wsComWaybill.outAndPushToCustomer = '出仓信息推送客户'
t.wsComWaybill.batchUpdateOrderNo = '更新供应商派送单号'
t.wsComWaybill.queryProviderOrderStatus = '查询供应商订单状态'
t.wsComWaybill.changeReason = '变更原因'
t.wsComWaybill.forecast = '尾程预报'
t.wsComWaybill.batchSortRate = '批量比价'
t.wsComWaybill.cancelForecast = '取消尾程预报'
t.wsComWaybill.changeLogisitcsChannel = '变更渠道'
t.wsComWaybill.forcedInbound = '强制完成入库'
t.wsComWaybill.inWarehouseAfterCancel = '重新入库'
t.wsComWaybill.forcedOutbound = '完结出库'
t.wsComWaybill.cancelMerge = '取消合并'
t.wsComWaybill.splitOrder = '拆单'
t.wsComWaybill.printLastChannel = '打印尾程面单'
t.wsComWaybill.downloadLastChannel = '下载尾程面单'
t.wsComWaybill.printLabelBarcode = '打印面单条码'
t.wsComWaybill.printProofOfPostage = '打印发货证明'
t.wsComWaybill.printProofOfSignFor = '打印签收证明'
t.wsComWaybill.modifyDeclareInfo = '修改申报信息'
t.wsComWaybill.modifyDeclareInfoBatch = '批量修改申报信息'
t.wsComWaybill.modifyInRemark = '修改入库备注'
t.wsComWaybill.modifyPackageInfo = '修改箱信息'
t.wsComWaybill.modifyFbaPackageInfo = '修改FBA箱信息'
t.wsComWaybill.copyInboundInfo = '复制到货信息'
t.wsComWaybill.sortRate = '比价预报'
t.wsComWaybill.usedAndForecasting = '选用并预报'
t.wsComWaybill.reRate = '重新询价'
t.wsComWaybill.reSortRate = '清空并重新比价'
t.wsComWaybill.changeProductLogistics = '变更产品'
t.wsComWaybill.changeLogisticsChannel = '变更渠道'
t.wsComWaybill.resetChannelProvider = '重置渠道供应商'
t.wsComWaybill.changeWarehouse = '变更仓库'
t.wsComWaybill.changeInboundTime = '变更入库时间'
t.wsComWaybill.changeInRemark = '修改入库备注'
t.wsComWaybill.quickInWarehouse = '快速入库'
t.wsComWaybill.cancelInWarehouse = '撤销入库'
t.wsComWaybill.reforecast = '重新预报'
t.wsComWaybill.masterDeliveryNo = '主派送单号'
t.wsComWaybill.copyDeliveryNo = '请从excel表格中复制出所有派送单号粘贴到此处'
t.wsComWaybill.copyDeliveryNoAndBox = '请从excel表格中复制出所有箱号（FBA唛头号）和派送单号粘贴到此处,左列为箱号（FBA唛头号）,右列为派送单号'
t.wsComWaybill.forcedOutboundTips = '该操作会作废未出库的包裹，并不能再使用，确定强制完成出库吗？'

t.wsComInSubWaybill = {}
t.wsComInSubWaybill.length = '长(CM)'
t.wsComInSubWaybill.width = '宽(CM)'
t.wsComInSubWaybill.height = '高(CM)'
t.wsComInSubWaybill.weight = '重量(KG)'
t.wsComInSubWaybill.deliveryNo = '包裹派送单号'
t.wsComInSubWaybill.packageNo = '箱号/FBA唛头'
t.wsComInSubWaybill.masterWaybillNo = '主运单号'
t.wsComInSubWaybill.haveMeasured = '已测量'

/* 整票入库 allInWarehouse */
t.comAllInWarehouse = {}
t.comAllInWarehouse.noPlaceholder = '请扫描或输入对应单号,按回车键后获取单据信息'
t.comAllInWarehouse.totalWeightPlaceholder = '请输入该订单的总重量，按回车键后录入箱信息'
t.comAllInWarehouse.togetherWeight = '整票称重'
t.comAllInWarehouse.oneByOneForLabel = '箱号一一对应'
t.comAllInWarehouse.inTime = '入库时间'
t.comAllInWarehouse.inRemark = '备注'
/* 单票入库 FraSingleInWarehouse */
t.comSingleInWarehouse = {}
t.comSingleInWarehouse.no = '单号'
t.comSingleInWarehouse.noPlaceholder = '请扫描或输{no}'
t.comSingleInWarehouse.setting = '预设选项'
t.comSingleInWarehouse.settingTestVolume = '测量体积'
t.comSingleInWarehouse.settingWarehouseSort = '入库后分拣'
t.comSingleInWarehouse.printLabel = '打印尾程面单'
t.comSingleInWarehouse.playVoiceMode = '播报选项'
t.comSingleInWarehouse.playVoiceModeWeight = '播报重量'
t.comSingleInWarehouse.playVoiceModeCountry = '播报国家/地区'
t.comSingleInWarehouse.playVoiceModeLogisticsChannel = '播报渠道'
t.comSingleInWarehouse.inWarehouseTableTitle = '入库记录'
t.comSingleInWarehouse.singleInWarehouseHandle = '提交'
t.comSingleInWarehouse.diffWeight = '重量差异'
t.comSingleInWarehouse.isDecimal3 = '1-3位小数'
t.comSingleInWarehouse.isPlusInteger2 = '非0正整数'

t.comParcelSort = {}
t.comParcelSort.no = '单号'
t.comParcelSort.noPlaceholder = '请扫描或输入运单号/派送单号'
t.comParcelSort.setting = '预设选项'
t.comParcelSort.settingLogisticsChannel = '按照渠道'
t.comParcelSort.settingCountry = '按照国家/地区'
t.comParcelSort.settingZone = '按照分区'
t.comParcelSort.playVoiceModeLogisticsProduct = '按照产品'
t.comParcelSort.singleInWarehouseHandle = '提交'
t.comBagList = {}
t.comBagList.bagInfo = '容器信息'
t.comBagList.operateLog = '操作日志'
t.comBagList.remark = '备注'
t.comBagList.waybillInfo = '运单列表'
t.comBagList.logActionName = '日志动作'
t.comBagList.logDescription = '日志描述'
t.comBagList.creator = '创建人'
t.comBagList.createDate = '创建时间'
t.comBagList.createDateIsNotEmpty = '创建时间不能为空'
t.comBagList.totalNetWeight = '总净重'
t.comBagList.packageTotalVolume = '包裹总体积（M³）'
t.comBagList.createDateTimeBetweedWeek = '导出范围：查询条件的创建时间不能超过两个月'

t.comMawb = {}
t.comMawb.bagInfo = '容器信息'
t.comMawb.operateLog = '操作日志'
t.comMawb.operateMode = '操作模块'
t.comMawb.operate = '主单操作'
t.comMawb.remark = '备注'
t.comMawb.hawbList = '分单列表'
t.comMawb.billList = '包裹列表'
t.comMawb.returnBillList = '退货包裹列表'
t.comMawb.logActionName = '日志动作'
t.comMawb.logDescription = '日志描述'
t.comMawb.creator = '创建人'
t.comMawb.createDate = '创建时间'

t.comTransferBatch = {}
t.comTransferBatch.bagInfo = '容器信息'
t.comTransferBatch.operateLog = '操作日志'
t.comTransferBatch.remark = '备注'
t.comTransferBatch.bagInfo = '容器列表'
t.comTransferBatch.logActionName = '日志动作'
t.comTransferBatch.logDescription = '日志描述'
t.comTransferBatch.creator = '创建人'
t.comTransferBatch.createDate = '创建时间'

t.wsComOutWarehouseBatch.bagInfo = '容器信息'
t.wsComOutWarehouseBatch.operateLog = '操作日志'
t.wsComOutWarehouseBatch.remark = '备注'
t.wsComOutWarehouseBatch.bagInfo = '容器列表'
t.wsComOutWarehouseBatch.logActionName = '日志动作'
t.wsComOutWarehouseBatch.logDescription = '日志描述'
t.wsComOutWarehouseBatch.creator = '创建人'
t.wsComOutWarehouseBatch.createDate = '创建时间'
t.wsComOutWarehouseBatch.costHandle = '成本维护'
t.wsComOutWarehouseBatch.trackHandle = '轨迹维护'
t.wsComOutWarehouseBatch.mainHandle = '主单维护'
t.wsComOutWarehouseBatch.deliveryExportHandle = '交接单导出'
t.wsComOutWarehouseBatch.declareExportHandle = '申报信息导出'
t.wsComOutWarehouseBatch.postOfficePredictionExportHandle = '邮局预报导出'
t.wsComOutWarehouseBatch.select = '请选择列表数据'
t.wsComOutWarehouseBatch.declareExport = '申报明细导出'
t.wsComOutWarehouseBatch.declareTemplate = '申报模板'

t.btn = {}
t.btn.createBatch = '创建批次'
t.btn.finishBatch = '完结批次'
t.btn.downloadTemplate = '下载模板'
t.btn.submit = '提交'
t.btn.cancelBatch = '撤销发运'
t.btn.exportList = '导出清单'
t.btn.createBag = '创建容器'
t.btn.finishBag = '完结容器'
t.btn.confirmAbandon = '确认弃件'
t.btn.abandonFinish = '弃件完结'
t.btn.customerAbandon = '客户弃件'
t.btn.applyAbandon = '申请弃件'
t.btn.applyBack = '申请退货'
t.btn.backCustomer = '退回客户'
t.btn.keepTransfer = '转为正常'
t.btn.balance = '余额'
t.btn.debtRelease = '欠费放行'
t.btn.searchBtRelease = '搜索结果放行'
t.btn.debtReleaseExportd = '欠费导出'
t.btn.releaseType = '放行类型'
t.btn.releaseStatus = '放行状态'
t.btn.contactCustomer = '联系用户'
t.btn.repeatForecast = '重新预报'
t.btn.changeProduct = '更换产品'
t.btn.changeChannel = '更换渠道'
t.btn.saveConsignee = '保存收货人'
t.btn.saveShipper = '保存发货人'
t.btn.addWorkOrder = '点击此新增工单'
t.btn.uploadAttachment = '上传附件'
t.btn.divideWorkOrder = '分配工单'
t.btn.replayAndFinish = '完结工单'
t.btn.onlySubmitReplay = '提交'
t.btn.bindCustomer = '绑定客户'
t.btn.setWeight = '设置重量'
t.btn.upload = '提交上传'
t.btn.addFee = '增加费用项'
t.btn.reCreatePush = '重新生成主单推送单'
t.btn.trackMaintain = '轨迹维护'
t.btn.trackAddDetail = '增加轨迹明细'
t.btn.addBagHandle = '添加容器'
t.btn.addBillHandle = '添加包裹'
t.btn.addHawbHandle = '添加干线分单'
t.btn.addMemo = '添加备注'
t.btn.addMawbNo = '添加容器'
t.btn.addHawbHandle = '添加分单'
t.btn.deleteMawbNoBag = '删除容器'
t.btn.deleteMawbNoBill = '删除包裹'
t.btn.deleteMawbNoHawb = '删除分单'
t.btn.addLibrary = '添加黑名单'
t.btn.importAdd = '导入新增'
t.btn.importDelete = '导入删除'
t.btn.moreOperate = '更多操作'
t.btn.export = '导出'

t.label = {}
t.label.importFile = '导入文件'
t.label.no = '单号'
t.label.sysContainerLabelNo = '系统容器号'
t.label.sysContainerLabelNoTips = '用系统容器号获取供应商容器标签(袋/托)'
t.label.box = '箱'
t.label.forecastInfo = '预报信息'
t.label.inWarehouseStatistics = '入库统计信息'
t.label.boxType = '箱型'
t.label.volume = '体积'
t.label.weight = '重量(KG)'
t.label.volume = '体积'
t.label.length = '长(CM)'
t.label.width = '宽(CM)'
t.label.height = '高(CM)'
t.label.unitWeight = '重量(KG)/箱'
t.label.unitLength = '长(CM)/箱'
t.label.unitWidth = '宽(CM)/箱'
t.label.unitHeight = '高(CM)/箱'
t.label.customer = '客户'
t.label.cno = '客户单号'
t.label.forecastBoxQty = '预报箱数'
t.label.forecastWeight = '预报重量'
t.label.totalCount = '总票数'
t.label.packageQty = '预报件数'
t.label.inPackageQty = '已入件数'
t.label.outPackageQty = '已出件数'
t.label.totalWeight = '总重量(KG)'
t.label.failCount = '失败票数'
t.label.logisticsChannelNo = '渠道代码'
t.label.logisticsChannelName = '渠道名称'
t.label.setting = '预设选项'
t.label.playVoiceMode = '播报选项'
t.label.setting = '预设选项'
t.label.settingTestVolume = '测量体积'
t.label.settingCustomerWeight = '使用客户重量'
t.label.playVoiceMode = '播报选项'
t.label.playVoiceModeWeight = '播报重量'
t.label.playVoiceModeCountry = '播报国家/地区'
t.label.playVoiceModeLogisticsProduct = '播报产品'
t.label.playVoiceModeLogisticsChannel = '播报渠道'
t.label.autoCommit = '自动提交'
t.label.bagNo = '容器号'
t.label.bagNoExplanation = '（袋 / 箱 / 托）'
t.label.deliveryNo = '派送单号'
t.label.bagNoOrDeliveryNo = '包裹号/箱号/派送单号'
t.label.batchBagTotalCount = '容器总数'
t.label.bagCount = '容器总数'
t.label.receiveBagCount = '已收容器数'
t.label.waitBagCount = '待收容器数'
t.label.waybillTotalCount = '运单总数'
t.label.bagGrossWeight = '容器毛重'
t.label.bagNetWeight = '容器净重'
t.label.serialNo = '序号'
t.label.failCount = '失败总数'
t.label.storageLocation = '库位'
t.label.fixedStorageLocation = '固定库位'
t.label.inWarehouseTime = '入库时间'
t.label.processRemark = '处理备注'
t.label.releaseStatus = '放行状态'
t.label.releaseStatus = '放行状态'
t.label.releasePedestrians = '放行人'
t.label.releaseType = '放行类型'
t.label.releaseDate = '放行时间'
t.label.remark = '备注'
t.label.time = '时间'
t.label.ip = 'IP'
t.label.attachment = '查看附件'
t.label.warmPrompt = '温馨提示'
t.label.fee = '费用项'
t.label.customer = '客户'
t.label.remarks = '备注'
t.label.scanTips = '请扫描或输入{orderLogisticsType}{no}'
t.label.changeApiRefNo = '换号重报'

t.tabPane = {}
t.tabPane.batch = '批次信息'
t.tabPane.waybill = '运单信息'
t.tabPane.bag = '容器列表'
t.tabPane.shipperConsignee = '收发货人信息'
t.tabPane.declare = '报关信息'
t.tabPane.package = '包裹信息'
t.tabPane.reference = 'Reference ID'
t.tabPane.operateLog = '操作日志'
t.tabPane.exception = '异常信息'
t.tabPane.track = '轨迹信息'
t.tabPane.sortRate = '比价结果'

t.header = {}
t.header.batch = '批次信息'
t.header.operateLog = '操作日志'
t.header.remark = '备注信息'
t.header.bagChannelCount = '容器渠道统计'
t.header.consigneeDetailList = '收货明细清单'
t.header.shipperDetailList = '发运明细清单'
t.header.waybill = '运单信息'
t.header.customer = '客户信息'
t.header.exception = '异常信息'
t.header.operationLog = '操作日志'
t.header.shipper = '发货人'
t.header.consignee = '收货人'
t.header.track = '轨迹信息'
t.header.workOrder = '工单信息'
t.header.feedbackRecord = '反馈记录'
t.header.setEmergencyLevel = '设置紧急程度'
t.header.customerEvaluation = '客户评价'
t.header.answerService = '回答客服'
t.header.hasBindCustomer = '已绑定客户'
t.header.NotBindCustomer = '未绑定客户'
t.header.workCost = '作业单'
t.header.workFee = '作业费用'
t.header.workOrderNo = '作业单据'
t.header.baseInfo = '基础信息'
t.header.addressInfo = '地址信息'
t.header.contactsInfo = '联系人信息'
t.header.channelInfo = '对应渠道信息'
t.header.attachment = '附件信息'
t.header.apiInfo = '接口信息'

t.title = {}
t.title.abandon = '弃件'
t.title.debtRelease = '欠费放行'
t.title.repeatForecast = '重新预报'
t.title.keepTransfer = '转为正常'
t.title.changeProduct = '更换产品'
t.title.changeChannel = '更换渠道'
t.title.changeWarehouse = '变更仓库'
t.title.customerAbandon = '客户弃件'
t.title.applyBack = '申请退回'
t.title.excelUpload = 'Excel文件上传'
t.title.divideWorkOrder = '分配工单'
t.title.operationCost = '成本维护'
t.title.inWarehouseBatch = '入库批次'
t.title.pushMawb = '主单推送信息'
t.title.pushPackage = '包裹推送信息'
t.title.comMawb = '主单信息'
t.title.comHawb = '分单信息'
t.title.flight = '航空航班'
t.title.voyage = '海运航次'
t.title.addBag = '增加容器'
t.title.addBill = '增加运单'
t.title.addHawb = '增加干线分单'
t.title.addRemark = '添加备注'
t.title.approved = '通过审批'
t.title.notApproved = '不通过审批'

t.placeholder = {}
t.placeholder.no = '请扫描或输入单号'
t.placeholder.deliveryNo = '请扫描或输入派送单号'
t.placeholder.waybillOrDeliveryNo = '请扫描运单号/派送单号'
t.placeholder.waybillOrDeliveryNoOrPackageNo = '请扫描运单号/派送单号/包裹号/邮政单号'
t.placeholder.originalDeliveryNo = '请输入原派送单号'
t.placeholder.bag = '请扫描或者输入容器号（袋/箱/托）'
t.placeholder.bagNoOrDeliveryNo = '请扫描或者输入包裹号/箱号/派送单号'
t.placeholder.scanDeliveryNo = '请扫描或者输入派送单号'
t.placeholder.scanSysContainerNo = '请扫描或者输入系统容器号'
t.placeholder.exceptionRemark = '请输入异常描述内容，比如：内含液体违禁品'
t.placeholder.inputFee = '请输入费用'
t.placeholder.inputCustomerName = '请输入客户名称'
t.placeholder.bagNo = '请输入容器号'
t.placeholder.evaluationContent = '请输入您宝贵的意见或者建议'

t.bdLogisticsProductAuthorization.productAuthorization = '产品授权管理'
t.bdLogisticsProductAuthorization.channelAuthorization = '渠道授权管理'
t.bdLogisticsProductAuthorization.channel = '物流渠道'
t.bdLogisticsProductAuthorization.product = '物流产品'
t.bdLogisticsProductAuthorization.customer = '客户'
t.bdLogisticsProductAuthorization.unAuthorization = '已禁用'
t.bdLogisticsProductAuthorization.hasAuthorization = '已授权'
t.bdLogisticsProductAuthorization.setUp = '产品设置'
t.bdLogisticsProductAuthorization.logisticsChannelRouteMaintain = '渠道链路维护'
t.bdLogisticsProductAuthorization.contactCustomer = '联系客户'
t.bdLogisticsProductAuthorization.listType = '授权模式'
t.bdLogisticsProductAuthorization.blackList = '黑名单模式'
t.bdLogisticsProductAuthorization.whiteList = '白名单模式'
t.bdLogisticsProductAuthorization.blackListTips = '黑名单模式：默认启用所有物流产品(渠道)，需手动禁用'
t.bdLogisticsProductAuthorization.whiteListTips = '白名单模式：默认禁用所有物流产品(渠道)，需手动启用'

t.comDebtRetentionCount = {}
t.comDebtRetentionCount.debtWaybillCount = '欠费运单数量'
t.comDebtRetentionCount.debtAmount = '可用金额'
t.comDebtRetentionCount.inWarehouseTime = '入库时间'

/* 仓库物料表 WsComMaterials */
t.wsComMaterials.checkCodeIsExist = '校验重复'
t.validate.wsComMaterialsWeightLength = '重量长度在 1 到 20 个字符'
t.validate.wsComMaterialsWeightDecimal = '重量必须为大于等于0的正整数或1-3位小数'
t.validate.wsComMaterialsLengthLength = '长度必须在 1 到 10 个字符'
t.validate.wsComMaterialsWidthLength = '宽度必须在 1 到 10 个字符'
t.validate.wsComMaterialsHeightLength = '高度必须在 1 到 10 个字符'
t.validate.wsComMaterialsUntitledLength = '成本价格必须在 1 到 20 个字符'
t.validate.wsComMaterialsCodeLength = '物料代码必须在 1 到 36 个字符'
t.validate.wsComMaterialsLengthDecimal = '长度必须为非0正整数或1位小数'
t.validate.wsComMaterialsWidthDecimal = '宽度必须为非0正整数或1位小数'
t.validate.wsComMaterialsHeightDecimal = '高度必须为非0正整数或1位小数'
t.validate.wsComMaterialsUntitledDecimal = '成本价格必须为非0正整数或1-2位小数'
t.wsComMaterials.wsComMaterialsCodeOrWarehouseIdIsEmpty = '物料代码或仓库不能为空'
t.wsComMaterials.wsComMaterialsCodeOrWarehouseIdIsExist = '当前仓库对应的物料代码已存在,请修改物料代码或仓库'

/* 中心仓管理-待处理异常-comExceptionOperateWait */
t.comExceptionOperateWait = {}
t.comExceptionOperateWait.createDate = '下单时间'
t.comExceptionOperateWait.warehousingTime = '入库时间'
t.comExceptionOperateWait.inAdjustWeight = '运单重量(kg)'
t.comExceptionOperateWait.exceptionTimeRequired = '请选择标记异常时间'
t.comExceptionOperateWait.processTimeRequired = '请选择异常处理时间'

t.workOrderServiceCount = {}
t.workOrderServiceCount.hasAcceptedCount = '已受理工单数'
t.workOrderServiceCount.processingCount = '正处理工单数'
t.workOrderServiceCount.waitResponseCount = '等待回应工单数'
t.workOrderServiceCount.waitReplayCount = '等待答复工单数'
t.workOrderServiceCount.followUpTotalCount = '跟进中工单数'

t.workOrderCount = {}
t.workOrderCount.totalCount = '工单总数'
t.workOrderCount.waitAcceptCount = '等待受理'
t.workOrderCount.hasAcceptedCount = '已经受理'
t.workOrderCount.processingCount = '正在处理'
t.workOrderCount.waitResponseCount = '等待回应'
t.workOrderCount.waitReplayCount = '等待答复'
t.workOrderCount.workOutCount = '处理完成'
t.workOrderCount.finishCount = '处理完成'
t.workOrderCount.hasClosedCount = '已经关闭'

t.workOrderServiceGrade = {}
t.workOrderServiceGrade.highCommentTotal = '默认好评数'
t.workOrderServiceGrade.customerHighCommentTotal = '客户好评数'
t.workOrderServiceGrade.mediumCommentTotal = '中评数'
t.workOrderServiceGrade.poorCommentTotal = '差评数'

/* 客服管理-加盟商异常单-fraExceptionBill */
t.csmFraExceptionBill = {}
t.csmFraExceptionBill.exceptionTime = '异常时间'
t.csmFraExceptionBill.exceptionStatus = '异常处理状态'
t.csmFraExceptionBill.franchiseName = '所属加盟商'
t.csmFraExceptionBill.franchiseContact = '加盟商联系人'
t.csmFraExceptionBill.franchisePhone = '加盟商电话'
t.csmFraExceptionBill.serviceId = '专属客服'
t.csmFraExceptionBill.salesmanId = '所属业务员'
t.csmFraExceptionBill.orderTime = '下单时间'

/* 自定义pdf明细表 BdDiypdfDetail */
t.bdDiypdfDetail = {}
t.bdDiypdfDetail.id = 'ID'
t.bdDiypdfDetail.diypdfId = '自定义pdf编号'
t.bdDiypdfDetail.type = '类型'
t.bdDiypdfDetail.cssType = 'CSS类型'
t.bdDiypdfDetail.size = '尺寸'
t.bdDiypdfDetail.defaulted = '是否为默认'
t.bdDiypdfDetail.pdfHtml = '样式HTML'

/* 自定义pdf BdDiypdf */
t.bdDiypdf = {}
t.bdDiypdf.id = 'ID'
t.bdDiypdf.name = '名称'
t.bdDiypdf.type = '类型'
t.bdDiypdf.source = '来源'

/* 客服管理 - 加盟商异常单-csmExceptionOperateWait */
t.csmExceptionOperateWait = {}
t.csmExceptionOperateWait.exceptionTime = '标记异常时间'
t.csmExceptionOperateWait.warehouseId = '仓库'
t.csmExceptionOperateWait.exceptionStatus = '处理状态'
t.csmExceptionOperateWait.subOrderHandle = '子单处理'
t.csmExceptionOperateWait.waybillNo = '运单号'
t.csmExceptionOperateWait.cancel = '取消'
t.csmExceptionOperateWait.confirmProcessed = '确认已处理'
t.csmExceptionOperateWait.export = '导出订单'

/* 中心仓作业 - 出库发运-comOutWarehouseShipment */
t.comOutWarehouseShipment = {}
t.comOutWarehouseShipment.agentType = '自营/代理类型'
t.comOutWarehouseShipment.providerId = '代理供应商'
t.comOutWarehouseShipment.channelId = '尾程渠道'
t.comOutWarehouseShipment.batchNoDiy = '自定义批次号'
t.comOutWarehouseShipment.batchNoDiyPlaceHolder = '如自定义批次号不填写,则系统自动产生'
t.comOutWarehouseShipment.remarks = '仓库备注'

/* 字段映射明细 BdFieldMatchDetail */
t.bdFieldMatchDetail = {}
t.bdFieldMatchDetail.id = 'ID'
t.bdFieldMatchDetail.matchId = '模块编号'
t.bdFieldMatchDetail.objectType = '对象类型'
t.bdFieldMatchDetail.repeatableColumns = '可重复多列'
t.bdFieldMatchDetail.fieldName = '字段名'
t.bdFieldMatchDetail.fieldValue = '字段值'
t.bdFieldMatchDetail.digital = '是否数字'
t.bdFieldMatchDetail.allowEmpty = '是否允许空'
t.bdFieldMatchDetail.enumerate = '是否枚举'
t.bdFieldMatchDetail.enumerateType = '枚举类型'
t.bdFieldMatchDetail.enumerateName = '枚举名称'

/* 字段映射 BdFieldMatch */
t.bdFieldMatch = {}
t.bdFieldMatch.id = 'ID'
t.bdFieldMatch.name = '模块名'
t.bdFieldMatch.description = '描述'

/* dto关联映射 BdDtoMapping */
t.bdDtoMapping = {}
t.bdDtoMapping.id = 'ID'
t.bdDtoMapping.masterObjectType = '主对象类型'
t.bdDtoMapping.mappingObjectType = '关联对象类型'
t.bdDtoMapping.masterField = '主对象字段'
t.bdDtoMapping.mappingField = '关联对象字段'
t.bdDtoMapping.dtoFieldName = '属性对象名'

/* 中心仓管理 - 待弃件异常单-comExceptionAbandonWait */
t.comExceptionAbandonWait = {}
t.comExceptionAbandonWait.inAdjustWeight = '包裹重量(KG)'
t.comExceptionAbandonWait.waybillOrDeliveryNo = '请扫描或输入物流公司单号/派送单号'

/* 异步任务表 SysAsyncTask */
t.sysAsyncTask = {}
t.sysAsyncTask.id = '任务编号'
t.sysAsyncTask.name = '任务名称'
t.sysAsyncTask.taskType = '操作类型'
t.sysAsyncTask.endDate = '结束时间'
t.sysAsyncTask.executeTime = '执行时长(S)'
t.sysAsyncTask.resultType = '结果类型'
t.sysAsyncTask.dictKey = '公用的字典key，业务方根据自己的需求定义'
t.sysAsyncTask.resultMsg = '执行结果'
t.sysAsyncTask.remark = '备注'

/** 订单校验模板明细 */
t.bdOrderVerificationDetail.maintain = '模板明细维护'
t.bdOrderVerificationDetail.consigneeVerification = '收件人信息校验'
t.bdOrderVerificationDetail.shipperVerification = '发件人信息校验'
t.bdOrderVerificationDetail.packageCargoVerification = '包裹明细校验'
t.bdOrderVerificationDetail.baseInfoVerification = '基础信息校验'
t.bdOrderVerificationDetail.declareVerification = '报关明细校验'

t.validate.plusdecimal = '输入的数字不能小于0,且最多保留{number}位小数'

/* 客服管理 - 轨迹查询-tksTracking */
t.tksTracking = {}
t.tksTracking.waybillNo = '请输入1个或最多不超过100个运单号，用逗号或换行分隔'
t.tksTracking.enSearch = '查看中文轨迹'
t.tksTracking.result = '查询结果'
t.tksTracking.normal = '正常'
t.tksTracking.abnormal = '异常'
t.tksTracking.queryNO = '查询单号'
t.tksTracking.btyNo = '物流公司单号'
t.tksTracking.trackingNO = '派送单号'
t.tksTracking.logisticsCompanyTrackNo = '物流公司单号'
t.tksTracking.trackingNumber = '派送单号'
t.tksTracking.timeOutHour = '超时小时数'
t.tksTracking.diffHour = '距最新轨迹时间小时差'
t.tksTracking.trackDescription = '轨迹说明：所有轨迹发生时间都是发生地所在时区的时间'

/* 轨迹信息基础资料 bdTksTrackingCodeInfo */
t.bdTksTrackingCodeInfo = {}
t.bdTksTrackingCodeInfo.status = '状态'
t.bdTksTrackingCodeInfo.updateDate = '更新时间'
t.bdTksTrackingCodeInfo.updater = '更新人'

/* api平台参数 SysApiPlatformParam */
t.sysApiPlatformParam = {}
t.sysApiPlatformParam.id = 'ID'
t.sysApiPlatformParam.platformId = '平台ID'
t.sysApiPlatformParam.key = 'KEY'
t.sysApiPlatformParam.value = 'VALUE'
t.sysApiPlatformParam.enumerate = '是否枚举'
t.sysApiPlatformParam.enumerateValue = '枚举值'
t.sysApiPlatformParam.description = '描述'

/* api平台信息 SysApiPlatform */
t.sysApiPlatform = {}
t.sysApiPlatform.id = 'ID'
t.sysApiPlatform.name = '平台名称'
t.sysApiPlatform.maxRequestFrequency = '最大请求频次'
t.sysApiPlatform.apikey = 'APIKEY'
t.sysApiPlatform.apisecret = 'APISECRET'
t.sysApiPlatform.interfaceType = '接口类型'
t.sysApiPlatform.customerParam = '有客户参数'

/* api平台api账号 SysApiPlatformApiuser */
t.sysApiPlatformApiuser = {}
t.sysApiPlatformApiuser.id = 'ID'
t.sysApiPlatformApiuser.platformId = '平台ID'
t.sysApiPlatformApiuser.isApiPush = '是否API推送'
t.sysApiPlatformApiuser.apiPushNode = 'API推送节点'
t.sysApiPlatformApiuser.apiTypeId = 'API接口类型编号'

/* api平台api参数 SysApiPlatformApiParam */
t.sysApiPlatformApiParam = {}
t.sysApiPlatformApiParam.id = 'ID'
t.sysApiPlatformApiParam.platformId = '平台ID'
t.sysApiPlatformApiParam.paramKey = '参数名'
t.sysApiPlatformApiParam.paramValue = '参数值'
t.sysApiPlatformApiParam.remark = '备注'
t.sysApiPlatformApiParam.platformApiParamSetup = '平台接收API参数设置'

/* 客户api账号 BdCustomerApiuser */
t.bdCustomerApiuser = {}
t.bdCustomerApiuser.id = 'ID'
t.bdCustomerApiuser.customerId = '客户编号'
t.bdCustomerApiuser.usertoken = 'usertoken'

/* 客服管理 - 已处理异常单 comExceptionOperateFinish */
t.comExceptionOperateFinish = {}
t.comExceptionOperateFinish.processTimeRequired = '请选择异常处理时间'
t.comExceptionOperateFinish.export = '导出清单'

/* 渠道预报日志 BdChannelForecastLog */
t.bdChannelForecastLog = {}
t.bdChannelForecastLog.id = 'ID'
t.bdChannelForecastLog.waybillNo = '运单号'
t.bdChannelForecastLog.customerOrderNo = '客户单号'
t.bdChannelForecastLog.customerId = '客户'
t.bdChannelForecastLog.channelCode = '物流渠道'
t.bdChannelForecastLog.type = '类型'
t.bdChannelForecastLog.url = '请求url'
t.bdChannelForecastLog.head = '请求头'
t.bdChannelForecastLog.requestOrResponse = '请求/响应'
t.bdChannelForecastLog.responseTime = '响应时间(MS)'
t.bdChannelForecastLog.message = '消息'
t.bdChannelForecastLog.createDate = '创建时间'
t.bdChannelForecastLog.log = '报文'

/* 渠道预报标签参数 BdChannelForecastLabelParam */
t.bdChannelForecastLabelParam = {}
t.bdChannelForecastLabelParam.id = 'ID'
t.bdChannelForecastLabelParam.waybillNo = '运单号'
t.bdChannelForecastLabelParam.agentApiType = '代理接口类型'
t.bdChannelForecastLabelParam.agentParam = '代理参数'
t.bdChannelForecastLabelParam.requestParam = '请求参数'
t.bdChannelForecastLabelParam.replayLabel = '重新获取面单'
t.bdChannelForecastLabelParam.resetChannelParam = '重设渠道信息'

// 客服管理-中心仓异常单
t.wsComExceptionBill.createDateIsNotEmpty = '创建时间不能为空'
t.wsComExceptionBill.createDateTimeBetweedMonth = '创建时间不能超过一月'

// 短信模版表
t.sysSmsTemplate = {}
t.sysSmsTemplate.id = 'ID'
t.sysSmsTemplate.content = '模版内容'
t.sysSmsTemplate.templateName = '模版名称'

// 客户余额预警表
t.bdCustomerAlert = {}
t.bdCustomerAlert.id = 'ID'
t.bdCustomerAlert.userId = '客户'
t.bdCustomerAlert.alertAmount = '预警金额'

t.wsComOutWareHouseShipment = {}
t.wsComOutWareHouseShipment.totalBag = '容器总数'
t.wsComOutWareHouseShipment.shippingList = '发运明细清单:'
t.wsComOutWareHouseShipment.batchPutInShipment = '批量发运'
t.wsComOutWareHouseShipment.batchCancelPutInShipment = '批量撤销发运'
t.wsComOutWareHouseShipment.batchNoIsNotBlank = '批次号不能为空'

/* FBA仓库信息 BdFbaWarehouseInfo */
t.bdFbaWarehouseInfo = {}
t.bdFbaWarehouseInfo.id = 'ID'
t.bdFbaWarehouseInfo.code = '编码'
t.bdFbaWarehouseInfo.name = '名称'
t.bdFbaWarehouseInfo.enName = '英文名称'
t.bdFbaWarehouseInfo.standardUnit = '标准单位'
t.bdFbaWarehouseInfo.lengthUnit = '长度单位'
t.bdFbaWarehouseInfo.weightUnit = '重量单位'
t.bdFbaWarehouseInfo.country = '国家'
t.bdFbaWarehouseInfo.province = '省/州'
t.bdFbaWarehouseInfo.city = '市'
t.bdFbaWarehouseInfo.district = '区'
t.bdFbaWarehouseInfo.street = '地址'
t.bdFbaWarehouseInfo.postcode = '邮编'
t.bdFbaWarehouseInfo.contact = '联系人'
t.bdFbaWarehouseInfo.phone = '电话'
t.bdFbaWarehouseInfo.companyName = '公司名'

t.fba = {}
t.fba.warehouseCode = '亚马逊仓库'
t.fba.forecastWeight = '预报重量'
t.fba.unitNetWeight = '单位净重'
t.fba.mergeOrder = '合并运单'
t.fba.vatNo = 'VAT'
t.fba.confirmMerge = '确认合并'
t.fba.confirmSplit = '确认拆单'
t.fba.select = '选择'
t.fba.deselect = '取消选择'
t.fba.logisticsProductsUsedAfterMerger = '合并后使用的物流产品'
t.fba.standardUnit = '标准单位'
t.fba.weightUnit = '重量单位'
t.fba.lengthUnit = '长度单位'
t.fba.totalBox = '总箱数'
t.fba.totalWeight = '总重量'
t.fba.currency = '申报币种'
t.fba.uploadAttachments = '上传附件'
t.fba.importBoxCargoInfo = '导入箱货信息'
t.fba.inputReference = '录入Reference'
t.fba.enterBoxInfo = '录入箱信息'
t.fba.boxNo = '箱号/FBA唛头'
t.fba.boxSerialNo = '箱序号'
t.fba.sku = 'SKU'
t.fba.enName = '英文名'
t.fba.cnName = '中文名'
t.fba.qty = '数量'
t.fba.unitDeclarePrice = '申报单价'
t.fba.unitNetWeight = '单位净重'
t.fba.totalPrice = '总价值'
t.fba.hsCode = 'HSCODE'
t.fba.countryOfOrigin = '原产地'
t.fba.material = '材质'
t.fba.specificationAndModel = '规格型号'
t.fba.purpose = '用途'
t.fba.brand = '品牌'
t.fba.hsCode9610 = '9610HSCODE'
t.fba.productUrl = '销售网址'
t.fba.picUrl = '图片'
t.fba.uploadPic = '点击此处上传'
t.fba.pickingRemark = '配货备注'
t.fba.declareUnit = '申报单位'
t.fba.moreProductInfo = '更多商品信息'
t.fba.convenientEntry = '便捷录入'
t.fba.boxNoOrMarkNo = '箱号/FBA唛头'
t.fba.subCustomerOrderNo = '子客户单号'
t.fba.customerBoxNo = '客户箱号'
t.fba.boxCount = '箱数'
t.fba.changeBoxInfo = '变更箱信息'
t.fba.scanBoxNoOrMarkNoPlaceholder = '扫描一个或多个箱号/FBA唛头'
t.fba.serialNo = '序号'
t.fba.inputPackageNoForWholePackageInPlaceholder = '<P>1.箱号可以是完整的箱号或者箱号尾部的序号,通过中横线或逗号分隔.</P>' +
  '<P>2.中横线表示连续的箱号,逗号表示多个不连续的箱号.</P>' +
  '<P>3.如果一个单包含多种格式的FBA唛头号/箱号,可以混入.如是混入,则每行填写的第一个箱号必须是完整的FBA唛头号/箱号</P>' +
  '<P>普通样例1:  FBA16J3U000001,3-10,11,13</P><P>普通样例2:  1-3,4-10,11,12,13</P>' +
  '<P>混入样例(包含2种格式的箱号):' +
  '<P>第一行录入: FBA16J3U000001,3-10,11,13</P><P>第二行录入: FBA1001U000001,2-4,6,FBA16J3U000002</P>'
t.fba.inputSerialNoPlaceholder = '填写序号，以","逗号分隔（如:1,2 表示第1,2行）,若连续多行可用"-"中横号分隔（如:1-3,表示1至3行）,可以混用（如：1,3,4-8,表示第1行和3至8行）'
t.fba.quickFill = '快捷填充'
t.fba.boxInfo = '箱信息'
t.fba.autoAppend = '自动追加'
t.fba.addBoxNo = '追加箱号'
t.fba.resetBoxNo = '重置箱号'
t.fba.waybillNo = '运单号'
t.fba.printSystemShippingMark = '打印系统箱唛'
t.fba.length = '长'
t.fba.width = '宽'
t.fba.height = '高'
t.fba.weight = '重量'
t.fba.warehouseCode = '亚马逊'
t.fba.fbaWarehouse = 'FBA仓库'
t.fba.amazonWarehouse = '亚马逊仓库'
t.fba.baseInfoInputTips = '请把基础信息和收件人信息填写完整!并录入箱信息.'
t.fba.boxIsEmpty = '请录入箱信息再进行入库'
t.fba.boxInfoInputTips = '请把箱货信息填写完整'
t.fba.printSysBoxMark = '打印系统箱唛'
t.fba.orderByMode = '排序模式'
t.fba.trackingNo = '运单号'
t.fba.boxNoPrefix = '箱号前缀'
t.fba.forecast = '预报'
t.fba.batchPrint = '打印尾程面单'
t.fba.batchOrderImport = '批量导入订单'
t.fba.cancelForecast = '取消预报'
t.fba.deletedCancel = '撤销'
t.fba.vat = 'VAT号'
t.fba.completeDeleted = '彻底删除'
t.fba.attachment = '附件'
t.fba.fbaWarehouseCheckBoxTips = '以亚马逊仓库作为收件地址'
t.fba.customerOrderNoOrInWarehouseNo = '客户单号/入库单号'
t.fba.customerName = '客户'
t.fba.import_amazon_module = '亚马逊模板'
t.fba.draft = '暂存草稿'
t.fba.forecast = '预报'
t.fba.header = {}
t.fba.header.copy = 'FBA订单 -- 复制'
t.fba.header.update = 'FBA订单 -- 修改'
t.fba.header.consignee = '收件人信息'
t.fba.header.boxCargoInfo = '箱货信息'

/* 供应商费用映射明细 BdFeeTypeProviderMatchingItem */
t.bdFeeTypeProviderMatchingItem = {}
t.bdFeeTypeProviderMatchingItem.id = 'ID'
t.bdFeeTypeProviderMatchingItem.matchingId = '映射主单ID'
t.bdFeeTypeProviderMatchingItem.feeTypeId = '费用项'
t.bdFeeTypeProviderMatchingItem.providerFeeTypeName = '供应商费用项名称'
t.bdFeeTypeProviderMatchingItem.remark = '备注'

/* 费用项明细映射 BdFeeTypeProviderMatching */
t.bdFeeTypeProviderMatching = {}
t.bdFeeTypeProviderMatching.id = 'ID'
t.bdFeeTypeProviderMatching.providerId = '供应商'
t.bdFeeTypeProviderMatching.feeCategory = '费用项类别'
t.bdFeeTypeProviderMatching.logisticsChannelCode = '物流渠道'
t.bdFeeTypeProviderMatching.remark = '备注'

/* 提成方案明细 PsCommissionPlanPrice */
t.psCommissionPlanPrice = {}
t.psCommissionPlanPrice.title = '提成明细'
t.psCommissionPlanPrice.id = 'ID'
t.psCommissionPlanPrice.planId = '提成方案'
t.psCommissionPlanPrice.planElementType = '提成要素'
t.psCommissionPlanPrice.beginValue = '起始值'
t.psCommissionPlanPrice.endValue = '结束值'
t.psCommissionPlanPrice.culcMethod = '计算方法'
t.psCommissionPlanPrice.unitValue = '单位值'
t.psCommissionPlanPrice.beginPrice = '起价'
t.psCommissionPlanPrice.price = '单价'
t.psCommissionPlanPrice.formula = '计算公式'

/* 提成费用单 BaCommissionBizOrder */
t.baCommissionBizOrder = {}
t.baCommissionBizOrder.id = 'ID'
t.baCommissionBizOrder.month = '月份'
t.baCommissionBizOrder.settlementObjectType = '结算对象类型'
t.baCommissionBizOrder.settlementObjectId = '结算对象编码'
t.baCommissionBizOrder.salesmanId = '业务员'
t.baCommissionBizOrder.settlementObjectName = '结算对象名称'
t.baCommissionBizOrder.billsId = '计费单号'
t.baCommissionBizOrder.optDate = '业务发生时间'
t.baCommissionBizOrder.remark = '备注'
t.baCommissionBizOrder.logisticsProductCode = '运输方式'
t.baCommissionBizOrder.planElementType = '提成要素'
t.baCommissionBizOrder.planElementValue = '计费元素值'
t.baCommissionBizOrder.currency = '币种'
t.baCommissionBizOrder.receivableSum = '应收金额'
t.baCommissionBizOrder.accountingPeriod = '会计期间'
t.baCommissionBizOrder.settleBillId = '对账单号'
t.baCommissionBizOrder.auditor = '审核人'
t.baCommissionBizOrder.auditDate = '审核时间'

/* 提成任务单 BaCommissionTask */
t.baCommissionTask = {}
t.baCommissionTask.id = 'ID'
t.baCommissionTask.inWaybillId = '入库运单ID'
t.baCommissionTask.outWaybillId = '出库运单ID'
t.baCommissionTask.outDate = '出库时间'
t.baCommissionTask.month = '月份'
t.baCommissionTask.settlementObjectType = '结算对象类型'
t.baCommissionTask.settlementObjectId = '结算对象编码'
t.baCommissionTask.logisticsProductCode = '运输方式'
t.baCommissionTask.logisticsChannelCode = '渠道代码'
t.baCommissionTask.customerId = '客户编号'

/* 提成费用明细 BaCommissionBizFee */
t.baCommissionBizFee = {}
t.baCommissionBizFee.id = 'ID'
t.baCommissionBizFee.commissionBizOrderId = '费用单号'
t.baCommissionBizFee.billsId = '计费单号'
t.baCommissionBizFee.createType = '费用产生方式'
t.baCommissionBizFee.settleBillId = '对账单号'
t.baCommissionBizFee.feeTypeId = '费用项'
t.baCommissionBizFee.currency = '币种'
t.baCommissionBizFee.receivableSum = '应收金额'
t.baCommissionBizFee.commissionSum = '提成金额'
t.baCommissionBizFee.formula = '提成公式'
t.baCommissionBizFee.billingDate = '计费时间'
t.baCommissionBizFee.auditor = '审核人'
t.baCommissionBizFee.auditDate = '审核时间'
t.baCommissionBizFee.remark = '备注'

/* 提成配置 BdCommissionSetting */
t.bdCommissionSetting = {}
t.bdCommissionSetting.id = 'ID'
t.bdCommissionSetting.planElementType = '提成要素'
t.bdCommissionSetting.statisticsDate = '统计提成日'
t.bdCommissionSetting.monthOffset = '统计数据月范围'
t.bdCommissionSetting.effectDate = '生效时间'
t.bdCommissionSetting.sendCommission = '支持多次提成'
t.bdCommissionSetting.localCurrency = '本位币种'

/* 提成运单明细 BaCommissionBillItem */
t.baCommissionBillItem = {}
t.baCommissionBillItem.id = 'ID'
t.baCommissionBillItem.month = '月份'
t.baCommissionBillItem.settlementObjectType = '结算对象类型'
t.baCommissionBillItem.settlementObjectId = '结算对象编码'
t.baCommissionBillItem.billsId = '计费单ID'
t.baCommissionBillItem.outDate = '入库时间'
t.baCommissionBillItem.customerId = '客户编号'
t.baCommissionBillItem.customerName = '客户对象名称'
t.baCommissionBillItem.customerOrderNo = '客户单号'
t.baCommissionBillItem.waybillNo = '运单号'
t.baCommissionBillItem.deliveryNo = '派送单号'
t.baCommissionBillItem.logisticsProductCode = '物流产品'
t.baCommissionBillItem.logisticsChannelCode = '渠道代码'
t.baCommissionBillItem.planElementType = '提成要素'
t.baCommissionBillItem.balanceWeight = '结算重(KG)'
t.baCommissionBillItem.weight = '实重(KG)'
t.baCommissionBillItem.vol = '体积'
t.baCommissionBillItem.waybillQty = '票数'
t.baCommissionBillItem.packageQty = '件数'
t.baCommissionBillItem.receivableSum = '应收金额'
t.baCommissionBillItem.payableSum = '应付金额'
t.baCommissionBillItem.grossprofitSum = '毛利金额'
t.baCommissionBillItem.auditor = '审核人'
t.baCommissionBillItem.auditDate = '审核时间'

/* 提成计费单 BaCommissionBills */
t.baCommissionBills = {}
t.baCommissionBills.id = 'ID'
t.baCommissionBills.month = '月份'
t.baCommissionBills.settlementObjectType = '结算对象类型'
t.baCommissionBills.settlementObjectId = '结算对象编码'
t.baCommissionBills.salesmanId = '业务员'
t.baCommissionBills.settlementObjectName = '结算对象名称'
t.baCommissionBills.optDate = '业务发生时间'
t.baCommissionBills.logisticsProductCode = '物流产品'
t.baCommissionBills.planElementType = '提成要素'
t.baCommissionBills.balanceWeight = '结算重(KG)'
t.baCommissionBills.weight = '实重(KG)'
t.baCommissionBills.vol = '体积()'
t.baCommissionBills.waybillQty = '票数'
t.baCommissionBills.packageQty = '件数'
t.baCommissionBills.receivableSum = '应收金额'
t.baCommissionBills.payableSum = '应付金额'
t.baCommissionBills.grossprofitSum = '毛利金额'
t.baCommissionBills.remark = '备注'

/* 提成方案 PsCommissionPlan */
t.psCommissionPlan = {}
t.psCommissionPlan.id = 'ID'
t.psCommissionPlan.planElementType = '提成要素'
t.psCommissionPlan.objectType = '对象类型'
t.psCommissionPlan.salemanId = '业务员'
t.psCommissionPlan.objectName = '对象名称'
t.psCommissionPlan.currency = '币种'
t.psCommissionPlan.logisticsProductCode = '物流产品'
t.psCommissionPlan.effectDate = '生效时间'
t.psCommissionPlan.remark = '备注'
t.psCommissionPlan.auditor = '审核人'
t.psCommissionPlan.auditDate = '审核时间'

t.prompt = {}
t.prompt.title = '提示'
t.prompt.info = '确定进行[ {handle} ]操作?'
t.prompt.infoMore = '确定进行[ {handle} ]操作? {moreinfo}'
t.prompt.success = '操作成功'
t.prompt.failed = '操作失败'
t.prompt.uploadEmpty = '请上传付款水单'
t.prompt.auditBatch = '请选择审核项'
t.prompt.deleteBatch = '请选择删除项'
t.prompt.abandonBatch = '请选择弃审项'
t.prompt.finishBatch = '请选择完成项'
t.prompt.warehouse = '请先选择一个仓库'
t.prompt.actionStatusBatch = '请选择操作项'
t.prompt.exportBatch = '请选择导出项'
t.prompt.export = '请选择导出模板'
t.prompt.download = '下载'
t.prompt.single = '只能选择一条数据操作'
t.prompt.batchPrint = '请选择批量打印数据'
t.prompt.bankAccount = '请选择银行账户'

t.psExpressCustomerSpotQuotation = {}
t.psExpressCustomerSpotQuotation.id = 'ID'
t.psExpressCustomerSpotQuotation.customerId = '客户'
t.psExpressCustomerSpotQuotation.customerName = '客户名称'
t.psExpressCustomerSpotQuotation.logisticsProductCode = '物流产品'
t.psExpressCustomerSpotQuotation.type = '类型'
t.psExpressCustomerSpotQuotation.price = '单价/金额'
t.psExpressCustomerSpotQuotation.discount = '折扣'
t.psExpressCustomerSpotQuotation.effectDate = '生效时间'
t.psExpressCustomerSpotQuotation.deadDate = '失效时间'
t.psExpressCustomerSpotQuotation.remark = '备注'
t.psExpressCustomerSpotQuotation.noCustomerError = '右侧的客户信息未选择,请勾选需要设置即期价的客户'
t.psExpressCustomerSpotQuotation.selectCustomerTip = '选择需要设置即期价的客户'
t.psExpressCustomerSpotQuotation.selectedCustomerTip = '已选择客户'

t.upload.fileNameFormat = '文件名只能中文，字母，数字，中划线和下划线'

/* 申报明细 CoOrderDeclare */
t.coOrderDeclare = {}
t.coOrderDeclare.id = 'ID'
t.coOrderDeclare.orderId = '订单编号'
t.coOrderDeclare.chineseName = '中文品名'
t.coOrderDeclare.englishName = '英文品名'
t.coOrderDeclare.declareUnit = '申报单位'
t.coOrderDeclare.packageCustomerNo = '箱号/FBA唛头'
t.coOrderDeclare.quantity = '数量'
t.coOrderDeclare.unitNetWeight = '单位净重（KG）'
t.coOrderDeclare.unitDeclarePrice = '申报单价'
t.coOrderDeclare.brand = '品牌'
t.coOrderDeclare.goodsBarcode = '货物条码'
t.coOrderDeclare.sku = 'SKU'
t.coOrderDeclare.hsCode = '海关编码'
t.coOrderDeclare.productModel = '产品型号'
t.coOrderDeclare.material = '材质'
t.coOrderDeclare.purpose = '用途'
t.coOrderDeclare.origin = '原产地'
t.coOrderDeclare.pickingRemark = '配货备注'
t.coOrderDeclare.productUrl = '商品URL'

/* FBA的Reference CoOrderFbaReference */
t.coOrderFbaReference = {}
t.coOrderFbaReference.orderId = '订单编号'
t.coOrderFbaReference.fbaNo = 'FBA编号'
t.coOrderFbaReference.referenceId = 'Reference ID'
t.coOrderFbaReference.packageQty = '箱数'
t.coOrderFbaReference.id = 'ID'

/* 客户发货量统计 RpInbillsStatistics */
t.rpInbillsStatistics = {}
t.rpInbillsStatistics.id = 'ID'
t.rpInbillsStatistics.salesmanId = '业务员'
t.rpInbillsStatistics.customerId = '客户编码'
t.rpInbillsStatistics.customerName = '客户名称'
t.rpInbillsStatistics.logisticsProductCode = '运输方式'
t.rpInbillsStatistics.weight = '实重(KG)'
t.rpInbillsStatistics.vol = '体积(M³)'
t.rpInbillsStatistics.waybillQty = '票数'
t.rpInbillsStatistics.packageQty = '件数'
t.rpInbillsStatistics.season = '季'
t.rpInbillsStatistics.month = '月'
t.rpInbillsStatistics.week = '周'
t.rpInbillsStatistics.day = '日'
t.rpInbillsStatistics.startStatisticsDate = '开始统计时间'
t.rpInbillsStatistics.endStatisticsDate = '结束统计时间'
t.rpInbillsStatistics.period = '周期'
t.rpInbillsStatistics.statisticsDate = '发货时间'
t.rpInbillsStatistics.dimension = '统计维度'
t.rpInbillsStatistics.remark = '备注'
// 头部信息
t.header.operateLog = '操作日志'
t.header.remark = '备注信息'
t.header.baseInfo = '基础信息'
t.header.recipientInfo = '收件人信息'
t.header.senderInfo = '发件人信息'
t.header.boxCargoInfo = '箱货信息'
t.header.waybill = '运单信息'
t.header.customer = '客户信息'
t.header.exception = '异常信息'
t.header.operationLog = '操作日志'
t.header.shipper = '发件人'
t.header.consignee = '收件人'
t.header.workOrder = '工单信息'
t.header.feedbackRecord = '反馈记录'
t.header.setEmergencyLevel = '设置紧急程度'
t.header.customerEvaluation = '客户评价'
t.header.answerService = '回答客服'
/* 订单信息 CoOrder */
t.coOrder.id = '订单编号'
t.coOrder.customerOrderNo = '客户单号'
t.coOrder.customerBoxNo = '箱号/FBA唛头'
t.coOrder.update = '订单--修改'
t.coOrder.copy = '订单--复制'
t.coOrder.forecastMoney = '预估费用'
t.coOrder.shopName = '店铺'
t.coOrder.customerOrderNoOrInWarehouseNo = '客户单号/入库单号'
t.coOrder.waybillNo = '运单号'
t.coOrder.waybillNoPlaceHold = '物流产品设置为手工输入才需要填写该单号'
t.coOrder.trackingNo = '运单号'
t.coOrder.deliveryNo = '派送单号'
t.coOrder.deliveryNoPlaceHold = '只有线上物流才需要填写该单号'
t.coOrder.logisticsProductCode = '物流产品'
t.coOrder.taxPayMode = '税费模式'
t.coOrder.taxPayAccount = '税费支付人账号'
t.coOrder.customsMethod = '报关方式'
t.coOrder.forecastWeight = '预报重量'
t.coOrder.parcelType = '包裹类型'
t.coOrder.goodsCategory = '货物类别'
t.coOrder.electric = '是否带电'
t.coOrder.magnetized = '是否带磁'
t.coOrder.liquid = '是否液体'
t.coOrder.powder = '是否粉末'
t.coOrder.remote = '是否偏远'
t.coOrder.insuredAmount = '保险金额'
t.coOrder.insuredCurrency = '保险币种'
t.coOrder.codAmount = 'COD金额'
t.coOrder.codCurrency = 'COD币种'
t.coOrder.declareCurrency = '申报币种'
t.coOrder.subCustomerOrderNo = '子客户单号'
t.coOrder.consigneeCountry = '收货国家'
t.coOrder.customerRemark = '客户备注'
t.coOrder.serviceId = '客服'
t.coOrder.salesmanId = '业务员'
t.coOrder.customerId = '客户'
t.coOrder.customerName = '客户名称'
t.coOrder.customerSimpleCode = '客户简码'
t.coOrder.franchiseeId = '加盟商编号'
t.coOrder.warehouseId = '仓库编号'
t.coOrder.platformType = '来源平台'
t.coOrder.salesUrl = '销售URL'
t.coOrder.packageQty = '包裹件数'
t.coOrder.print = '是否打印'
t.coOrder.status = '订单状态'
t.coOrder.createDate = '创建时间'
t.coOrder.orderByMode = '排序模式'
t.coOrder.fbaWarehouseCheckBoxTips = '以亚马逊仓库作为收件地址'
t.coOrder.fillInSenderInfo = '填写发件人信息'
t.coOrder.amazonWarehouse = '亚马逊仓库'
t.coOrderConsignee.vat = 'VAT税号'
t.coOrderConsignee.detailAddress = '地址'
t.coOrderConsignee.recipient = '姓名'
t.addDeclareCargo = '增加申报信息'
t.addFee = '增加费用信息'
t.addOperatingOrder = '增加作业单'
t.batchAddOperatingOrder = '增加作业单(批量)'
t.addMawbOrder = '增加干线主单'
t.addHawbOrder = '增加干线分单'
t.addPackageOrder = '增加订单包裹'
/* 预约提货单 WsComCollectOrder */
t.wsComCollectOrder = {}
t.wsComCollectOrder.id = 'ID'
t.wsComCollectOrder.companyOrderNo = '业务单号'
t.wsComCollectOrder.logisticsProductCode = '物流产品'
t.wsComCollectOrder.planCollectDate = '预约提货时间'
t.wsComCollectOrder.packageQty = '包裹件数'
t.wsComCollectOrder.containerQty = '容器数'
t.wsComCollectOrder.weight = '毛重(KG)'
t.wsComCollectOrder.volume = '体积(M³)'
t.wsComCollectOrder.salesmanId = '业务员'
t.wsComCollectOrder.collectDriver = '提货司机'
t.wsComCollectOrder.collectLicensePlateNumber = '车牌号'
t.wsComCollectOrder.companyMemo = '公司备注'
t.wsComCollectOrder.consigneeCountry = '派送国家'
t.wsComCollectOrder.consigneePostcode = '派送邮编'
t.wsComCollectOrder.customerId = '客户'
t.wsComCollectOrder.customerName = '客户名称'
t.wsComCollectOrder.customerCode = '客户代号'
t.wsComCollectOrder.customerAddress = '提货地址'
t.wsComCollectOrder.customerMemo = '客户备注'
t.wsComCollectOrder.collectedDate = '提货完成时间'
t.wsComCollectOrder.warehouseId = '交货仓库'
t.wsComCollectOrder.finish = '完成提货'
t.wsComCollectOrder.trucking = '派车'
t.wsComCollectOrder.baseInfo = '基础信息'
t.wsComCollectOrder.customerCargoInfo = '货信息'
t.wsComCollectOrder.companyInfo = '公司信息'

/* 费用加收 PsFeeAddition */
t.psFeeAddition = {}
t.psFeeAddition.id = 'ID'
t.psFeeAddition.customerScope = '客户范围'
t.psFeeAddition.objectId = '客户'
t.psFeeAddition.objectName = '客户名称'
t.psFeeAddition.logisticsProductCode = '物流产品'
t.psFeeAddition.logisticsChannelCode = '物流渠道'
t.psFeeAddition.discount = '折扣'
t.psFeeAddition.ratio = '加收比例'
t.psFeeAddition.fixedValue = '加收固定金额'
t.psFeeAddition.remark = '备注'
t.psFeeAddition.auditor = '审核人'
t.psFeeAddition.auditDate = '审核时间'
t.psFeeAddition.logisticsProductCodePlaceholder = '产品渠道二选一，清空渠道后可选'
t.psFeeAddition.logisticsChannelCodePlaceholder = '产品渠道二选一，清空产品后可选'

/* excel导出自定义模板明细 BdExcelExportTemplateDetail */
t.bdExcelExportTemplateDetail = {}
t.bdExcelExportTemplateDetail.id = 'ID'
t.bdExcelExportTemplateDetail.templateId = '模版编号'
t.bdExcelExportTemplateDetail.exportObjectName = '导出对象名称'
t.bdExcelExportTemplateDetail.fieldName = '字段名'
t.bdExcelExportTemplateDetail.titleName = '表头名'
t.bdExcelExportTemplateDetail.columnNo = '列序号'
t.bdExcelExportTemplateDetail.type = '类型,数据字典/基础数据/普通数据...'
t.bdExcelExportTemplateDetail.serviceName = '服务名'
t.bdExcelExportTemplateDetail.fixValue = '固定内容'

/* 自定义导出模版 BdExcelExportTemplate */
t.bdExcelExportTemplate = {}
t.bdExcelExportTemplate.id = 'ID'
t.bdExcelExportTemplate.systemTemplate = '是否系统模板,0否/1是'
t.bdExcelExportTemplate.type = '模板归属'
t.bdExcelExportTemplate.name = '模板名称'
t.bdExcelExportTemplate.exportMainObjectName = '导出模块'
t.bdExcelExportTemplate.description = '描述'
t.bdExcelExportTemplate.url = 'URL路径'
t.bdExcelExportTemplate.objectId = '客户ID,为系统模板时，客户ID为0'
t.bdExcelExportTemplate.detailOneLine = '申报明细单行显示'
t.bdExcelExportTemplate.startLine = '头部预留行数'
t.bdExcelExportTemplate.addCustomer = '新增客户端模板'

/* 系统设置表 SysSetting */
t.sysSetting = {}
t.sysSetting.id = 'ID'
t.sysSetting.systemLogo = '系统logo'
t.sysSetting.systemLogoUploadEmpty = '请上传系统logo'
t.sysSetting.systemName = '系统名称'
t.sysSetting.companyName = '公司名称'
t.sysSetting.companyAbbr = '公司简称'
t.sysSetting.qrCodeImg = '公司公众号二维码图片'

/* 询价结果 BaSortRateLog */
t.baSortRateLog = {}
t.baSortRateLog.id = 'ID'
t.baSortRateLog.orderId = '订单ID'
t.baSortRateLog.channelRouterId = '渠道链路'
t.baSortRateLog.logisticsChannelCode = '物流渠道'
t.baSortRateLog.providerChannelCode = 'API渠道代码'
t.baSortRateLog.type = '报价来源'
t.baSortRateLog.currency = '币种'
t.baSortRateLog.payableSum = '金额'
t.baSortRateLog.balanceWeight = '结算重'
t.baSortRateLog.balanceWeightUnit = '结算重单位'
t.baSortRateLog.zone = '分区'
t.baSortRateLog.message = '错误信息'
t.baSortRateLog.createDate = '询价时间'

/* 尾程派送渠道供应商服务代码 BdLogisticsChannelProviderCode */
t.bdLogisticsChannelProviderCode = {}
t.bdLogisticsChannelProviderCode.id = '渠道编号'
t.bdLogisticsChannelProviderCode.providerChannelCode = 'API渠道代码'
t.bdLogisticsChannelProviderCode.providerChannelName = 'API渠道名称'
t.bdLogisticsChannelProviderCode.providerCodeSetting = '供应商服务设置'

/* 代理换单子表 WsComReplaceSubDeliveryNo */
t.wsComReplaceSubDeliveryNo = {}
t.wsComReplaceSubDeliveryNo.id = 'ID'
t.wsComReplaceSubDeliveryNo.subWaybillId = '子运单ID'
t.wsComReplaceSubDeliveryNo.orderPackageId = '订单包裹ID'
t.wsComReplaceSubDeliveryNo.oldDeliveryNo = '原单号'
t.wsComReplaceSubDeliveryNo.newDeliveryNo = '新派送单号'
t.wsComReplaceSubDeliveryNo.importType = '导入类型'
t.wsComReplaceSubDeliveryNo.noType = '导入单号类型'
t.wsComReplaceSubDeliveryNo.firstEleToMasterNoTips = '以第一个新派送单号做为新主派送单号'
t.wsComReplaceSubDeliveryNo.downloadImportTemplate = '下载导入模版'

/* 代理换单主表 WsComReplaceDeliveryNo */
t.wsComReplaceDeliveryNo = {}
t.wsComReplaceDeliveryNo.id = 'ID'
t.wsComReplaceDeliveryNo.waybillId = '运单ID'
t.wsComReplaceDeliveryNo.waybillNo = '运单号'
t.wsComReplaceDeliveryNo.waybillInfo = '运单信息'
t.wsComReplaceDeliveryNo.orderId = '订单ID'
t.wsComReplaceDeliveryNo.oldDeliveryNo = '原派送单号'
t.wsComReplaceDeliveryNo.oldMasterDeliveryNo = '原主派送单号'
t.wsComReplaceDeliveryNo.newDeliveryNo = '新派送单号'
t.wsComReplaceDeliveryNo.newMasterDeliveryNo = '新主派送单号'
t.wsComReplaceDeliveryNo.batchNo = '批次号'
t.wsComReplaceDeliveryNo.auditor = '审核人'
t.wsComReplaceDeliveryNo.auditDate = '审核时间'
t.wsComReplaceDeliveryNo.swapSubDeliveryNos = '子单换单'
t.wsComReplaceDeliveryNo.audit = '审核'
t.wsComReplaceDeliveryNo.auditByBatchNo = '按批次号审核'
t.wsComReplaceDeliveryNo.empty = '清空'
t.wsComReplaceDeliveryNo.downloadImportTemplate = '下载导入模版'

/* 应付费用单审核任务表 BaPayableBizAuditTask */
t.baPayableBizAuditTask = {}
t.baPayableBizAuditTask.id = 'ID'
t.baPayableBizAuditTask.bizOrderId = '费用单编号'
t.baPayableBizAuditTask.nextDate = '下次执行时间'
t.baPayableBizAuditTask.execTimes = '执行次数'
t.baPayableBizAuditTask.settlementObjectId = '结算对象'

/* 应收费用单审核任务表 BaReceivableBizAuditTask */
t.baReceivableBizAuditTask = {}
t.baReceivableBizAuditTask.id = 'ID'
t.baReceivableBizAuditTask.bizOrderId = '费用单编号'
t.baReceivableBizAuditTask.nextDate = '下次执行时间'
t.baReceivableBizAuditTask.execTimes = '执行次数'
t.baReceivableBizAuditTask.settlementObjectId = '结算对象'
t.baReceivableBizAuditTask.type = '类型'
t.baReceivableBizAuditTask.execute = '立即执行'
t.baReceivableBizAuditTask.customerVoucherNo = '客户单号'

/* 干线推送表 WsComMawbPush */
t.wsComMawbPush = {}
t.wsComMawbPush.id = 'ID'
t.wsComMawbPush.mawbId = '推送主单ID'
t.wsComMawbPush.mawbNo = '提单号'
t.wsComMawbPush.providerId = '供应商'
t.wsComMawbPush.logisticsChannelCode = '物流渠道'
t.wsComMawbPush.providerFileUrl = '服务商文件URL'
t.wsComMawbPush.remark = '备注'
t.wsComMawbPush.weightD = '重量'
t.wsComMawbPush.volD = '体积'
t.wsComMawbPush.packageQty = '件数'
t.wsComMawbPush.status = '推送状态'
t.wsComMawbPush.pushStatus = '打板状态'

/* 干线推送明细表 WsComMawbSubPush */
t.wsComMawbSubPush = {}
t.wsComMawbSubPush.id = 'ID'
t.wsComMawbSubPush.mawbId = '推送主单ID'
t.wsComMawbSubPush.bagId = '袋子ID'
t.wsComMawbSubPush.bagNo = '容器号'
t.wsComMawbSubPush.providerId = '供应商'
t.wsComMawbSubPush.logisticsChannelCode = '物流渠道'
t.wsComMawbSubPush.vol = '体积'
t.wsComMawbSubPush.weight = '实重'
t.wsComMawbSubPush.packageQty = '包裹件数'
t.wsComMawbSubPush.remark = '备注'

/* 干线推送包裹表 WsComMawbPackagePush */
t.wsComMawbPackagePush = {}
t.wsComMawbPackagePush.id = 'ID'
t.wsComMawbPackagePush.subId = '推送明细ID'
t.wsComMawbPackagePush.bagNo = '容器号'
t.wsComMawbPackagePush.deliveryNo = '派送单号'
t.wsComMawbPackagePush.mawbId = '提单ID'
t.wsComMawbPackagePush.mawbNo = '提单号'
t.wsComMawbPackagePush.customerNo = '公司单号'
t.wsComMawbPackagePush.hawbId = '分提单ID'
t.wsComMawbPackagePush.hawbNo = '分提单号'
t.wsComMawbPackagePush.providerId = '供应商'
t.wsComMawbPackagePush.logisticsChannelCode = '物流渠道'
t.wsComMawbPackagePush.vol = '体积'
t.wsComMawbPackagePush.weight = '实重'
t.wsComMawbPackagePush.length = '长'
t.wsComMawbPackagePush.width = '宽'
t.wsComMawbPackagePush.height = '高'
t.wsComMawbPackagePush.remark = '备注'

t.express = {}
t.express.switchConciseMode = '切换简洁录入模式'
t.express.switchFullMode = '切换完整录入模式'
t.express.forecastAndInWarehouse = '预报并入库'
t.express.customerBoxNo = '客户箱号'
t.express.boxNoLenPrefix = '箱号共'
t.express.boxNoLenSuffix = '位数'
t.express.generateBoxNo = '生成箱号'
t.express.addAnRow = '新增一行'
t.express.editForm = '编辑表单'
t.express.addConsigneeInfo = '追加收发件人信息'
t.express.addDeclareInfo = '补充申报信息'

/* 干线推送打板表 WsComMawbPushStaContain */
t.wsComMawbPushStaContain = {}
t.wsComMawbPushStaContain.id = 'ID'
t.wsComMawbPushStaContain.mawbNo = '提单号'
t.wsComMawbPushStaContain.hawbId = '干线分单ID'
t.wsComMawbPushStaContain.uldBatchNo = '打板批次号'
t.wsComMawbPushStaContain.airlineCode = '航空公司'
t.wsComMawbPushStaContain.uldNo = '打板编号'
t.wsComMawbPushStaContain.uldType = '打板型号'
t.wsComMawbPushStaContain.weight = '打板后重量'
t.wsComMawbPushStaContain.opCode = '处理状态,0-处理成功'
t.wsComMawbPushStaContain.opLocation = '实际操作地点'
t.wsComMawbPushStaContain.opTime = '作业时间'
t.wsComMawbPushStaContain.cn38CodeList = 'cn38code列表'
t.wsComMawbPushStaContain.providerFileUrl = '服务商文件URL'
t.wsComMawbPushStaContain.opRemark = '备注'

/* 干线推送打板批次表 WsComMawbPushStaBatch */
t.wsComMawbPushStaBatch = {}
t.wsComMawbPushStaBatch.id = 'ID'
t.wsComMawbPushStaBatch.mawbId = '干线主单ID'
t.wsComMawbPushStaBatch.uldBatchNo = '打板批次号'
t.wsComMawbPushStaBatch.airlineCode = '航空公司IATA代号'
t.wsComMawbPushStaBatch.opRemark = '备注'

t.bdAttachments = {}
t.bdAttachments.name = '文件名'
t.bdAttachments.file = '文件'
t.bdAttachments.attachmentUrl = '附件地址'

t.customerJumpToLogin = {}
t.customerJumpToLogin.originIP = '绑定来源IP地址'
t.customerJumpToLogin.originIPTips = '绑定来源IP地址,若有多个以逗号分隔，如：127.0.0.1,*********,*********'
t.customerJumpToLogin.omsUrl = '客户端请求地址'
t.customerJumpToLogin.title = '使用客户账号新开页面进行登陆'
t.customerJumpToLogin.omsUrlTips = 'http://127.0.0.1'

/* 轨迹自主推送明细表 TksTrackAutonomousPushDetail */
t.tksTrackAutonomousPushDetail = {}
t.tksTrackAutonomousPushDetail.id = 'ID'
t.tksTrackAutonomousPushDetail.serialNo = '顺序'
t.tksTrackAutonomousPushDetail.deliveryNo = '派送单号'
t.tksTrackAutonomousPushDetail.customerOrderNo = '客户单号'
t.tksTrackAutonomousPushDetail.trackCode = '系统轨迹代码'
t.tksTrackAutonomousPushDetail.apiTrackCode = 'api轨迹代码'
t.tksTrackAutonomousPushDetail.trackingContentEn = '轨迹英文描述'
t.tksTrackAutonomousPushDetail.trackingContentCn = '轨迹中文描述'
t.tksTrackAutonomousPushDetail.trackingCountry = '轨迹发生国'
t.tksTrackAutonomousPushDetail.trackingLocation = '轨迹发生地'
t.tksTrackAutonomousPushDetail.trackingTime = '轨迹发生时间'
t.tksTrackAutonomousPushDetail.timeZone = '当前轨迹的时区'
t.tksTrackAutonomousPushDetail.eventProvince = '当前轨迹的省州'
t.tksTrackAutonomousPushDetail.eventCity = '当前轨迹的城市'
t.tksTrackAutonomousPushDetail.zipCode = '当前轨迹的邮编'
t.tksTrackAutonomousPushDetail.flightNo = '当前轨迹的航班号'
t.tksTrackAutonomousPushDetail.isInternalTrack = '系统内部轨迹'
t.tksTrackAutonomousPushDetail.retryCount = '重试次数'

/* 轨迹自主推送 TksTrackAutonomousPush */
t.tksTrackAutonomousPush = {}
t.tksTrackAutonomousPush.id = 'ID主键'
t.tksTrackAutonomousPush.headerApiInfo = 'API信息'
t.tksTrackAutonomousPush.headertrackcodeMapConfigInfo = '轨迹节点映射'
t.tksTrackAutonomousPush.systemTrackNode = '系统轨迹节点'
t.tksTrackAutonomousPush.apiTrackNode = 'API轨迹节点'
t.tksTrackAutonomousPush.apiTypeValue = 'API类型'
t.tksTrackAutonomousPush.apiTypeDetailConfig = 'API类型配置信息'
t.tksTrackAutonomousPush.trackcodeMapConfig = '轨迹代码映射'
t.tksTrackAutonomousPush.autonomousPushDetailQty = '明细记录总数'
t.tksTrackAutonomousPush.detailQty = '系统内部轨迹总数'
t.tksTrackAutonomousPush.apiTypeValueNull = '请先选择API类型'
t.tksTrackAutonomousPush.maintenanceDetails = '维护明细'
t.tksTrackAutonomousPush.pushDetails = '推送'
t.tksTrackAutonomousPush.batchPush = '批量推送'
t.tksTrackAutonomousPush.finish = '完结'
t.tksTrackAutonomousPush.reopen = '重新打开'
t.tksTrackAutonomousPush.batchFinish = '批量完结'
t.tksTrackAutonomousPush.finishTips = '完结后将无法继续推送轨迹！确定继续吗？'
t.tksTrackAutonomousPush.deliveryNo = '派送单号'
t.tksTrackAutonomousPush.customerId = '推送客户'
t.tksTrackAutonomousPush.customerOrderNo = '客户单号'
t.tksTrackAutonomousPush.waybillNo = '运单号'
t.tksTrackAutonomousPush.postalTrackingNo = '邮政单号'
t.tksTrackAutonomousPush.logisticsChannelCode = '物流渠道'
t.tksTrackAutonomousPush.logisticsProductCode = '物流产品'
t.tksTrackAutonomousPush.apiParamSource = 'API参数来源'
t.tksTrackAutonomousPush.provider = '推送供应商'
t.tksTrackAutonomousPush.customer = '推送客户'
t.tksTrackAutonomousPush.newestTrackCode = '最新已推送轨迹代码'
t.tksTrackAutonomousPush.newestTrackTime = '最新已推送轨迹时间'
t.tksTrackAutonomousPush.newestTrackContent = '最新已推送轨迹内容'
t.tksTrackAutonomousPush.newestTrackLocation = '最新已推送轨迹地点'
t.tksTrackAutonomousPush.pushPlatform = '推送平台'
t.tksTrackAutonomousPush.newestTrackCodePlaceholder = '请先选择推送平台'
t.tksTrackAutonomousPush.pushCount = '推送次数'
t.tksTrackAutonomousPush.detailFailedCount = '明细推送失败数量'
t.tksTrackAutonomousPush.detailSuccessCount = '明细推送成功数量'
t.tksTrackAutonomousPush.detailWaitCount = '明细等待推送数量'
t.tksTrackAutonomousPush.pathType = '轨迹类型'
t.tksTrackAutonomousPush.destinationCountry = '轨迹发生国'
t.tksTrackAutonomousPush.timeFilter = '轨迹时间过滤'
t.tksTrackAutonomousPush.status = '状态'
t.tksTrackAutonomousPush.pushStatus = '明细状态'

/* 供应商轨迹匹配 BdOuterTrackMatching */
t.bdOuterTrackMatching = {}
t.bdOuterTrackMatching.id = 'ID'
t.bdOuterTrackMatching.outerType = '轨迹来源类型'
t.bdOuterTrackMatching.matchingType = '匹配类型'
t.bdOuterTrackMatching.trackingCode = '轨迹代码'
t.bdOuterTrackMatching.outerTrackingCode = '供应商轨迹代码'
t.bdOuterTrackMatching.outerTrackingContent = '供应商轨迹描述'
t.bdOuterTrackMatching.apiValue = 'API接口类型'
t.bdOuterTrackMatching.providerId = '供应商'
t.bdOuterTrackMatching.logisticsChannelCode = '物流渠道'
t.bdOuterTrackMatching.remark = '备注'

/* 轨迹API日志单号 BdTrackApiLogNo */
t.bdTrackApiLogNo = {}
t.bdTrackApiLogNo.id = 'ID'
t.bdTrackApiLogNo.trackLogId = '日志表编号'
t.bdTrackApiLogNo.waybillNo = '运单号'
t.bdTrackApiLogNo.deliveryNo = '派送单号'

/* 轨迹API日志 BdTrackApiLog */
t.bdTrackApiLog = {}
t.bdTrackApiLog.id = 'ID'
t.bdTrackApiLog.url = '请求url'
t.bdTrackApiLog.elapse = '耗时(秒)'
t.bdTrackApiLog.requestOrResponse = '请求或者响应'
t.bdTrackApiLog.message = '消息'
t.bdTrackApiLog.type = '类型'
t.bdTrackApiLog.head = '请求头'
t.bdTrackApiLog.logId = '请求id'

/* 干线包裹推送客户表 WsComMawbPackagePushCustomer */
t.wsComMawbPackagePushCustomer = {}
t.wsComMawbPackagePushCustomer.id = 'ID'
t.wsComMawbPackagePushCustomer.mawbId = '推送单ID'
t.wsComMawbPackagePushCustomer.outPackageId = '出库包裹ID'
t.wsComMawbPackagePushCustomer.packageNo = '箱号'
t.wsComMawbPackagePushCustomer.customerOrderNo = '客户单号'
t.wsComMawbPackagePushCustomer.waybillNo = '运单号'
t.wsComMawbPackagePushCustomer.deliveryNo = '派送单号'
t.wsComMawbPackagePushCustomer.bagNo = '容器号'
t.wsComMawbPackagePushCustomer.batchNo = '批次号'
t.wsComMawbPackagePushCustomer.customerId = '客户'
t.wsComMawbPackagePushCustomer.pushType = '推送类型'
t.wsComMawbPackagePushCustomer.status = '推送状态'
t.wsComMawbPackagePushCustomer.message = '错误消息'
t.wsComMawbPackagePushCustomer.build = '生成包裹推送'
t.wsComMawbPackagePushCustomer.unbind = '解绑'
t.wsComMawbPackagePushCustomer.bind = '绑定'

/* 干线推送客户表 WsComMawbPushCustomer */
t.wsComMawbPushCustomer = {}
t.wsComMawbPushCustomer.id = 'ID'
t.wsComMawbPushCustomer.mawbId = '推送主单ID'
t.wsComMawbPushCustomer.mawbNo = '提单号'
t.wsComMawbPushCustomer.customerId = '客户'
t.wsComMawbPushCustomer.customerName = '客户名称'
t.wsComMawbPushCustomer.status = '推送状态'
t.wsComMawbPushCustomer.packagePushStatus = '包裹推送状态'
t.wsComMawbPushCustomer.packageQty = '包裹数'
t.wsComMawbPushCustomer.successPackageQty = '成功包裹数'
t.wsComMawbPushCustomer.failPackageQty = '失败包裹数'
t.wsComMawbPushCustomer.message = '错误消息'
t.wsComMawbPushCustomer.viewPackage = '查看包裹'

/* 拉单回调错误日志 FoFetchOrderCallbackErrorLog */
t.foFetchOrderCallbackErrorLog = {}
t.foFetchOrderCallbackErrorLog.id = 'ID'
t.foFetchOrderCallbackErrorLog.callbackId = '回调ID'
t.foFetchOrderCallbackErrorLog.lastExecuteTime = '上次执行时间'
t.foFetchOrderCallbackErrorLog.nextExecuteTime = '下次执行时间'
t.foFetchOrderCallbackErrorLog.url = '请求url'
t.foFetchOrderCallbackErrorLog.head = '请求头'
t.foFetchOrderCallbackErrorLog.requestOrResponse = '请求或者响应'
t.foFetchOrderCallbackErrorLog.message = '消息'

/* 拉单表 FoFetchOrder */
t.foFetchOrder = {}
t.foFetchOrder.id = 'ID'
t.foFetchOrder.customerId = '客户ID'
t.foFetchOrder.dispatchFetchOrderId = '调度任务ID'
t.foFetchOrder.customerNo = '客户单号'
t.foFetchOrder.platformNo = '平台单号'
t.foFetchOrder.status = '生成订单状态'
t.foFetchOrder.saleNo = '交易单号'
t.foFetchOrder.selfOrderId = '生成的订单ID'
t.foFetchOrder.needCallback = '需要回调'
t.foFetchOrder.callbackStatus = '回调状态'
t.foFetchOrder.shopName = '店铺名'
t.foFetchOrder.convertErrMsg = '错误信息'
t.foFetchOrder.warehouseId = '仓库ID'

/* 拉单错误日志 FoDispatchFetchOrderErrorLog */
t.foDispatchFetchOrderErrorLog = {}
t.foDispatchFetchOrderErrorLog.id = 'ID'
t.foDispatchFetchOrderErrorLog.dispatchFetchOrderId = '调度任务ID'
t.foDispatchFetchOrderErrorLog.lastEndPoint = '上次数据结束点'
t.foDispatchFetchOrderErrorLog.lastExecuteTime = '上次执行时间'
t.foDispatchFetchOrderErrorLog.nextExecuteTime = '下次执行时间'
t.foDispatchFetchOrderErrorLog.url = '请求url'
t.foDispatchFetchOrderErrorLog.head = '请求头'
t.foDispatchFetchOrderErrorLog.requestOrResponse = '请求或者响应'
t.foDispatchFetchOrderErrorLog.message = '消息'

/* 拉单调度表 FoDispatchFetchOrder */
t.foDispatchFetchOrder = {}
t.foDispatchFetchOrder.id = '调度任务ID'
t.foDispatchFetchOrder.customerId = '客户'
t.foDispatchFetchOrder.priorityLevel = '优先级'
t.foDispatchFetchOrder.sourceTypeId = '订单来源类型'
t.foDispatchFetchOrder.sourceApiParams = '订单来源API参数'
t.foDispatchFetchOrder.eachFetchQty = '每次拉单数量'
t.foDispatchFetchOrder.lastEndPoint = '上次数据结束点'
t.foDispatchFetchOrder.lastExecuteTime = '上次执行时间'
t.foDispatchFetchOrder.nextExecuteTime = '下次执行时间'
t.foDispatchFetchOrder.executeTimeInterval = '执行间隔(ms)'
t.foDispatchFetchOrder.shopName = '店铺'
t.foDispatchFetchOrder.warehouseId = '仓库'

/* 拉单日志 FoFetchOrderLog */
t.foFetchOrderLog = {}
t.foFetchOrderLog.id = 'ID'
t.foFetchOrderLog.dispatchFetchOrderId = '拉单调度ID'
t.foFetchOrderLog.coFetchOrderId = '拉单ID'
t.foFetchOrderLog.url = '请求url'
t.foFetchOrderLog.head = '请求头'
t.foFetchOrderLog.requestOrResponse = '请求或者响应'
t.foFetchOrderLog.message = '消息'

/* 拉单回调表 FoFetchOrderCallback */
t.foFetchOrderCallback = {}
t.foFetchOrderCallback.id = 'ID'
t.foFetchOrderCallback.customerId = '客户ID'
t.foFetchOrderCallback.dispatchFetchOrderId = '拉单调度ID'
t.foFetchOrderCallback.customerNo = '客户单号'
t.foFetchOrderCallback.deliveryNo = '派送单号'
t.foFetchOrderCallback.shopName = '店铺名'
t.foFetchOrderCallback.warehouseId = '仓库ID'

t.bdReceivableBillExportSetting = {}
t.bdReceivableBillExportSetting.id = 'ID'
t.bdReceivableBillExportSetting.desc = '对账单汇总sheet页信息'
t.bdReceivableBillExportSetting.updateInfo = '编辑汇总页信息'
t.bdReceivableBillExportSetting.logoUrl = 'logoURL'
t.bdReceivableBillExportSetting.titleName = '公司抬头'
t.bdReceivableBillExportSetting.descriptionOne = '描述1'
t.bdReceivableBillExportSetting.descriptionTwo = '描述2'
t.bdReceivableBillExportSetting.descriptionOneColor = '描述1字体颜色'
t.bdReceivableBillExportSetting.descriptionTwoColor = '描述2字体颜色'
t.bdReceivableBillExportSetting.retainDecimalPlaces = '保留小数位数'
t.bdReceivableBillExportSetting.carryMethod = '进位方式'

/* 应收对账导出设置明细 BdReceivableBillExportSettingDetail */
t.bdReceivableBillExportSettingDetail = {}
t.bdReceivableBillExportSettingDetail.id = 'ID'
t.bdReceivableBillExportSettingDetail.desc = '明细sheet页表头'
t.bdReceivableBillExportSettingDetail.updateDetail = '编辑明细表头'
t.bdReceivableBillExportSettingDetail.fieldName = '字段名'
t.bdReceivableBillExportSettingDetail.titleName = '表头名'
t.bdReceivableBillExportSettingDetail.columnNo = '列序号'

/* 换标单 TlsChangeLabelOrder */
t.tlsChangeLabelOrder = {}
t.tlsChangeLabelOrder.id = 'ID'
t.tlsChangeLabelOrder.targetLogisticsProviderType = '新服务商类型'
t.tlsChangeLabelOrder.batchNo = '换标批次号'
t.tlsChangeLabelOrder.sourceDeliveryNo = '原单号'
t.tlsChangeLabelOrder.targetDeliveryNo = '新单号'
t.tlsChangeLabelOrder.labelFileName = '面单文件名'
t.tlsChangeLabelOrder.targetLabelUrl = '新面单URL'
t.tlsChangeLabelOrder.importChangeLabelData = '换标订单导入'
t.tlsChangeLabelOrder.targetLabelUrlIsEmpty = '面单地址为空'
t.tlsChangeLabelOrder.pleaseInputSourceNo = '请扫描或者输入原单号'

/* 应收重量核对 BaReceivableWeightCheck */
t.baReceivableWeightCheck = {}
t.baReceivableWeightCheck.id = '重量核对单号'
t.baReceivableWeightCheck.settlementObjectId = '结算对象编码'
t.baReceivableWeightCheck.settlementObjectName = '结算对象名称'
t.baReceivableWeightCheck.positiveError = '正误差'
t.baReceivableWeightCheck.negativeError = '负误差'
t.baReceivableWeightCheck.qty = '票数'
t.baReceivableWeightCheck.remark = '备注'
t.baReceivableWeightCheck.closed = '完结人'
t.baReceivableWeightCheck.closeDate = '完结时间'
t.baReceivableWeightCheck.weightType = '重量类型'
t.baReceivableWeightCheck.noType = '匹配单号类型'
t.baReceivableWeightCheck.finish = '完结并同步重计费'
t.baReceivableWeightCheck.weightErrorAndNegative = '重量误差范围 (负)'

/* 应收重量核对明细 BaReceivableWeightCheckDetail */
t.baReceivableWeightCheckDetail = {}
t.baReceivableWeightCheckDetail.id = 'ID'
t.baReceivableWeightCheckDetail.weightCheckId = '应收重量核对单号'
t.baReceivableWeightCheckDetail.errorLevel = '差异级别'
t.baReceivableWeightCheckDetail.businessId = '业务单号'
t.baReceivableWeightCheckDetail.waybillNo = '运单号'
t.baReceivableWeightCheckDetail.weightUnit = '重量单位'
t.baReceivableWeightCheckDetail.weight = '重量'
t.baReceivableWeightCheckDetail.lengthUnit = '长度单位'
t.baReceivableWeightCheckDetail.length = '长度'
t.baReceivableWeightCheckDetail.width = '宽度'
t.baReceivableWeightCheckDetail.height = '高度'
t.baReceivableWeightCheckDetail.checkRemark = '核对备注'

/* 拉单单号规则表 FoFetchNoRule */
t.foFetchNoRule = {}
t.foFetchNoRule.id = 'ID'
t.foFetchNoRule.sourceTypeId = '订单来源类型'
t.foFetchNoRule.config = '规则配置'
t.foFetchNoRule.platformSystemParamName = '平台系统参数'
t.foFetchNoRule.platformSystemParamNameExplain = '平台系统参数说明'
t.foFetchNoRule.systemParamName = '本系统参数'
t.foFetchNoRule.systemParamNameForMultiple = '本系统参数，支持多选'
t.foFetchNoRule.platformProducts = '平台产品'
t.foFetchNoRule.pleaseSelectTheOrderSourceTypeFirst = '请先选择订单来源类型'
t.foFetchNoRule.pleaseSelectTheOrderSourceTypeFirstForMultiple = '请先选择订单来源类型,支持多选'

/* 轨迹分组表 TksTrackingGroup */
t.tksTrackingGroup = {}
t.tksTrackingGroup.id = 'ID'
t.tksTrackingGroup.groupName = '分组名称'
t.tksTrackingGroup.node = '轨迹段'
t.tksTrackingGroup.sequence = '序号'
t.tksTrackingGroup.systemDefault = '系统默认'
t.tksTrackingGroup.nodePlaceholder = '轨迹段，支持多选'
t.tksTrackingGroup.codeList = '轨迹节点'
t.tksTrackingGroup.codeListPlaceholder = '轨迹节点，支持多选'
t.tksTrackingGroup.codeListPlaceholderTips = '请先选择轨迹段，支持多选'

/* 用户授权的客户 SysUserCustomerAuth */
t.sysUserCustomerAuth = {}
t.sysUserCustomerAuth.id = ''
t.sysUserCustomerAuth.sysUserId = '用户'
t.sysUserCustomerAuth.customerId = '客户'

/* 内部流程日志 BdInternalFlowLogs */
t.bdInternalFlowLogs = {}
t.bdInternalFlowLogs.id = 'id'
t.bdInternalFlowLogs.type = '类型'
t.bdInternalFlowLogs.batchId = '批次号'
t.bdInternalFlowLogs.waybillNo = '运单号'
t.bdInternalFlowLogs.customerOrderNo = '客户单号'
t.bdInternalFlowLogs.trackingNo = '跟踪号'
t.bdInternalFlowLogs.deliveryNo = '派送单号'
t.bdInternalFlowLogs.postalTrackingNo = '邮政单号'
t.bdInternalFlowLogs.logisticsProductCode = '物流产品'
t.bdInternalFlowLogs.logisticsChannelCode = '物流渠道'
t.bdInternalFlowLogs.executeMs = '执行毫秒数'
t.bdInternalFlowLogs.sendData = '传送数据'
t.bdInternalFlowLogs.failMessage = '失败消息'

/* 指定单号库 BdSpecifiedTrackingNo */
t.bdSpecifiedTrackingNo = {}
t.bdSpecifiedTrackingNo.id = 'ID'
t.bdSpecifiedTrackingNo.logisticsChannelCode = '物流渠道'
t.bdSpecifiedTrackingNo.sourceNo = '源单号'
t.bdSpecifiedTrackingNo.trackingNo = '派送单号'

/* 批量换标任务单 TlsBatchChangeLabelTask */
t.tlsBatchChangeLabelTask = {}
t.tlsBatchChangeLabelTask.id = 'ID'
t.tlsBatchChangeLabelTask.batchNo = '批次号'
t.tlsBatchChangeLabelTask.waybillId = '运单ID'
t.tlsBatchChangeLabelTask.oldDeliveryNo = '原派送单号'
t.tlsBatchChangeLabelTask.newDeliveryNo = '新派送单号'
t.tlsBatchChangeLabelTask.printTimes = '打印次数'
t.tlsBatchChangeLabelTask.warehouseId = '仓库ID'

/* 单号库轨迹跟踪 TlsNoLibTrajectory */
t.tlsNoLibTrajectory = {}
t.tlsNoLibTrajectory.id = 'ID'
t.tlsNoLibTrajectory.logisticsChannelCode = '渠道代码'
t.tlsNoLibTrajectory.deliveryNo = '派送单号'
t.tlsNoLibTrajectory.trackTimes = '跟踪次数'
t.tlsNoLibTrajectory.importDate = '单号导入时间'
t.tlsNoLibTrajectory.used = '单号已使用'
t.tlsNoLibTrajectory.usedDate = '单号使用时间'
t.tlsNoLibTrajectory.nextTrackingTime = '下次跟踪时间'
t.tlsNoLibTrajectory.syncTrackingNo = '获取单号库单号'
t.tlsNoLibTrajectory.clearInvalidTrackingNo = '清空无效单号'
t.tlsNoLibTrajectory.selectChannel = '选择渠道'

/* 运单轨迹跟踪 TlsDeliveryTrajectory */
t.tlsDeliveryTrajectory = {}
t.tlsDeliveryTrajectory.id = 'ID'
t.tlsDeliveryTrajectory.customerId = '客户ID'
t.tlsDeliveryTrajectory.customerName = '客户'
t.tlsDeliveryTrajectory.deliveryBatchNo = '送仓批次号'
t.tlsDeliveryTrajectory.logisticsChannelCode = '渠道'
t.tlsDeliveryTrajectory.deliveryNo = '派送单号'
t.tlsDeliveryTrajectory.orderDate = '下单时间'
t.tlsDeliveryTrajectory.trackTimes = '跟踪次数'
t.tlsDeliveryTrajectory.transStatus = '运输状态'
t.tlsDeliveryTrajectory.arrivePostDate = '送达邮局时间'
t.tlsDeliveryTrajectory.currentTrajectoryNodeDate = '最新轨迹时间'
t.tlsDeliveryTrajectory.currentTrajectoryAddress = '最新轨迹地址'
t.tlsDeliveryTrajectory.currentTrajectoryDesc = '最新轨迹描述'
t.tlsDeliveryTrajectory.beforeTrackDate = '上次轨迹跟踪时间'
t.tlsDeliveryTrajectory.nextTrackDate = '下次轨迹跟踪时间'
t.tlsDeliveryTrajectory.import = '导入派送单号'
t.tlsDeliveryTrajectory.register = '注册单号'
t.tlsDeliveryTrajectory.updateTransStatus = '更新运输状态'

/* Label换发件地址工单 TlsLabelChangeConsignorOrder */
t.tlsLabelChangeConsignorOrder = {}
t.tlsLabelChangeConsignorOrder.id = 'ID'
t.tlsLabelChangeConsignorOrder.logisticsChannelCode = '渠道'
t.tlsLabelChangeConsignorOrder.customerOrderNo = '客户单号'
t.tlsLabelChangeConsignorOrder.waybillNo = '运单号'
t.tlsLabelChangeConsignorOrder.deliveryNo = '派送单号'
t.tlsLabelChangeConsignorOrder.newReturnAddressId = '新发件地址'
t.tlsLabelChangeConsignorOrder.printTimes = '打印次数'
t.tlsLabelChangeConsignorOrder.customerId = '客户'
t.tlsLabelChangeConsignorOrder.customerName = '客户名称'
t.tlsLabelChangeConsignorOrder.warehouseId = '仓库'

/* 推送轨迹时间过滤设置 TksTrackAutonomousPushTimeFilter */
t.tksTrackAutonomousPushTimeFilter = {}
t.tksTrackAutonomousPushTimeFilter.id = 'ID'
t.tksTrackAutonomousPushTimeFilter.objectType = '对象类型'
t.tksTrackAutonomousPushTimeFilter.objectId = '推送对象'
t.tksTrackAutonomousPushTimeFilter.objectName = '推送对象'
t.tksTrackAutonomousPushTimeFilter.trackFilterTimeNode = '过滤时间节点'

/* 客户-注册 BdCustomerRegister */
t.bdCustomerRegister = {}
t.bdCustomerRegister.id = 'ID'
t.bdCustomerRegister.name = '公司名称（或个人姓名）'
t.bdCustomerRegister.phone = '手机'
t.bdCustomerRegister.email = '电子邮件'
t.bdCustomerRegister.address = '联系地址'

/* 派送单号分拣批次 TlsDeliveryNoSortBatch */
t.tlsDeliveryNoSortBatch = {}
t.tlsDeliveryNoSortBatch.id = 'ID'
t.tlsDeliveryNoSortBatch.batchNo = '批次号'
t.tlsDeliveryNoSortBatch.inSystemQty = '系统内单数'
t.tlsDeliveryNoSortBatch.outSystemQty = '系统外单数'
t.tlsDeliveryNoSortBatch.weightRangeOneQty = '系统内单号重量段一单数'
t.tlsDeliveryNoSortBatch.weightRangeTwoQty = '系统内单号重量段二单数'
t.tlsDeliveryNoSortBatch.warehouseId = '仓库'

/* 派送单号分拣单 TlsDeliveryNoSortOrder */
t.tlsDeliveryNoSortOrder = {}
t.tlsDeliveryNoSortOrder.id = 'ID'
t.tlsDeliveryNoSortOrder.batchId = '批次ID'
t.tlsDeliveryNoSortOrder.batchNo = '批次号'
t.tlsDeliveryNoSortOrder.logisticsChannelCode = '渠道'
t.tlsDeliveryNoSortOrder.customerOrderNo = '客户单号'
t.tlsDeliveryNoSortOrder.waybillNo = '运单号'
t.tlsDeliveryNoSortOrder.deliveryNo = '派送单号'
t.tlsDeliveryNoSortOrder.inSystem = '系统单号'
t.tlsDeliveryNoSortOrder.customerId = '客户'
t.tlsDeliveryNoSortOrder.customerName = '客户名称'

/* 渠道预上网轨迹明细 BdLogisticsChannelInboundPath */
t.bdLogisticsChannelInboundPath = {}
t.bdLogisticsChannelInboundPath.id = 'ID'
t.bdLogisticsChannelInboundPath.logisticsChannelId = '尾程派送渠道编号'
t.bdLogisticsChannelInboundPath.trackCode = '内部轨迹代码'
t.bdLogisticsChannelInboundPath.apiTrackCode = '供应商轨迹代码'
t.bdLogisticsChannelInboundPath.trackingContent = '供应商轨迹描述'
t.bdLogisticsChannelInboundPath.trackingLocation = '发生地'
t.bdLogisticsChannelInboundPath.trackingDelayTimeStart = '前一轨迹时间差[开始](小时)'
t.bdLogisticsChannelInboundPath.trackingDelayTimeEnd = '前一轨迹迹时间差[结束](小时)'
t.bdLogisticsChannelInboundPath.timeZone = '时区'
t.bdLogisticsChannelInboundPath.eventCountry = '国家'
t.bdLogisticsChannelInboundPath.eventProvince = '省州'
t.bdLogisticsChannelInboundPath.eventCity = '城市'
t.bdLogisticsChannelInboundPath.zipCode = '邮编'

/* 渠道面单修正 BdLogisticsChannelLabelChange */
t.bdLogisticsChannelLabelChange = {}
t.bdLogisticsChannelLabelChange.id = 'ID'
t.bdLogisticsChannelLabelChange.channelId = '渠道编码'
t.bdLogisticsChannelLabelChange.areaStartX = '起始x坐标(mm/px)'
t.bdLogisticsChannelLabelChange.areaStartY = '起始y坐标(mm/px)'
t.bdLogisticsChannelLabelChange.areaWidth = '区域宽(mm/px)'
t.bdLogisticsChannelLabelChange.areaHeight = '区域高(mm/px)'
t.bdLogisticsChannelLabelChange.fontSize = '字体大小'
t.bdLogisticsChannelLabelChange.rotation = '旋转角度'
t.bdLogisticsChannelLabelChange.type = '用途'
t.bdLogisticsChannelLabelChange.fullValueType = '填充值类型'
t.bdLogisticsChannelLabelChange.fullValue = '填充值'
t.bdLogisticsChannelLabelChange.testLabel = '预览实际效果'
t.bdLogisticsChannelLabelChange.testLabelByGridView = '网格线预览'

/* 轨迹平台承运商匹配 TksPlatformCarrierMatch */
t.tksPlatformCarrierMatch = {}
t.tksPlatformCarrierMatch.id = 'ID'
t.tksPlatformCarrierMatch.type = '匹配类型'
t.tksPlatformCarrierMatch.platformCode = '轨迹平台'
t.tksPlatformCarrierMatch.logisticsChannelCode = '物流渠道'
t.tksPlatformCarrierMatch.carrierCode = '平台承运商代码'
t.tksPlatformCarrierMatch.carrierName = '平台承运商名称'
t.tksPlatformCarrierMatch.regularExpression = '正则表达式'

/* 客户发货时效统计 RpTrackingStatistics */
t.rpTrackingStatistics = {}
t.rpTrackingStatistics.id = 'ID'
t.rpTrackingStatistics.customerId = '客户'
t.rpTrackingStatistics.logisticsChannelCode = '物流产品'
t.rpTrackingStatistics.outTime = '入库日期'
t.rpTrackingStatistics.qtyOut = '发货量'
t.rpTrackingStatistics.qtyNotAirline = '未交航'
t.rpTrackingStatistics.qtyAirline = '已交航'
t.rpTrackingStatistics.qtyCustomsClearance = '已清关'
t.rpTrackingStatistics.qtyDelivered = '已签收'
t.rpTrackingStatistics.qtyException = '异常件'
t.rpTrackingStatistics.hourInOut = '出库时效'
t.rpTrackingStatistics.hourInCnonline = '中邮上网时效'
t.rpTrackingStatistics.hourInLmonline = '末端上网时效'
t.rpTrackingStatistics.hourInDelivered = '签收时效'
t.rpTrackingStatistics.rateCnonline = '中邮上网率'
t.rpTrackingStatistics.rateLmonline = '末端上网率'
t.rpTrackingStatistics.rateDelivered = '签收率'

/* 批次明细 WsComBatchItem */
t.wsComBatchItem = {}
t.wsComBatchItem.id = 'ID'
t.wsComBatchItem.hadInBatch = '已添加的单号'
t.wsComBatchItem.batchId = '批次ID'
t.wsComBatchItem.batchNo = '批次号'
t.wsComBatchItem.logisticsProductCode = '物流产品'
t.wsComBatchItem.logisticsChannelCode = '物流渠道'
t.wsComBatchItem.customerOrderNo = '客户单号'
t.wsComBatchItem.waybillNo = '运单号'
t.wsComBatchItem.deliveryNo = '派送单号'
t.wsComBatchItem.customerId = '客户'
t.wsComBatchItem.customerName = '客户名称'

/* 批次单 WsComBatchOrder */
t.wsComBatchOrder = {}
t.wsComBatchOrder.id = 'ID'
t.wsComBatchOrder.providerId = '供应商'
t.wsComBatchOrder.customerId = '客户'
t.wsComBatchOrder.customerName = '客户'
t.wsComBatchOrder.originPostcode = '始发地邮编'
t.wsComBatchOrder.logisticsChannelCode = '物流渠道'
t.wsComBatchOrder.batchNo = '批次号'
t.wsComBatchOrder.parcelQty = '包裹数'
t.wsComBatchOrder.batchFileUrl = '批次/Scan Form文件'
t.wsComBatchOrder.errorMessage = '错误信息'
t.wsComBatchOrder.addItem = '添加订单'
t.wsComBatchOrder.delivery = '交递'
t.wsComBatchOrder.addItemType = '更新方式'

export default t
