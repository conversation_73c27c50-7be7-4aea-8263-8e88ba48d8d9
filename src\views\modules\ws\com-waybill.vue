<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-tabs class="flex_tab no_shadow" type="border-card" v-model="masterActiveName" @tab-click="masterTabsClick" :stretch="false">
        <el-tab-pane v-for="(masterTabItem, masterTabIndex) in typeArr" :key="masterTabIndex" :name="masterTabIndex">
          <div slot="label" style="min-width: 4em; text-align: center">
            <span style="font-weight: bolder;" v-if="masterTabItem.permission">{{ typeName(masterTabItem.value) || '&nbsp;' }}</span>
          </div>
        </el-tab-pane>
        <el-card class="search_box margin_bottom10" shadow="never">
          <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()"
                   label-width="90px">
            <el-row :gutter="10" type="flex">
              <el-col :span="6">
                <three-no-input ref="threeNoInput" @setNoSize="setNoSize"
                                :customerOrderNo.sync="dataForm.customerOrderNos"
                                :waybillNo.sync="dataForm.waybillNos"
                                :deliveryNo.sync="dataForm.deliveryNos"
                                :postalTrackingNo.sync="dataForm.postalTrackingNos"
                                :packageCustomerNos.sync="dataForm.packageCustomerNos"
                                :subDeliveryNos.sync="dataForm.subDeliveryNos"
                                :outBatchNos.sync="dataForm.outBatchNos"
                                :hideItem="[7, 8, 9, 10, 11]"
                                :autosize="threeNoInputAutoSize" :noSize="5000"/>
                <div>
                <span v-if="noSize > 0" style="padding-left: 5px;padding-top: 5px;">
                  搜索( <span style="color: #0ea55d">{{noSize}}</span> )条
                </span>
                  <span v-if="notInScopeNoList.length > 0" style="padding-left: 5px;padding-top: 5px;">
                  <el-button type="text" style="margin-left: 5px;font-size: 12px;color: #ebb563" icon="el-icon-warning" @click="showNotInScopeNosHandle">未匹配单号(<span style="color: #8a979e">{{notInScopeNoList.length}}</span>)条(点击查看)</el-button>
                </span>
                </div>
              </el-col>
              <el-col :span="16">
                <el-row :gutter="10">
                  <el-col :span="9">
                    <el-form-item :label="$t('wsComWaybill.logisticsProductCode')" prop="logisticsProductCode">
                      <el-select v-model="dataForm.logisticsProductCode" filterable clearable>
                        <el-option v-for="item in baseData.logisticsProductByParamsList" :key="item.code"
                                   :label="item.name" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('wsComWaybill.objectName')" prop="objectName">
                      <el-select v-model="dataForm.objectId" clearable
                                 :placeholder="$t('twoCharToSelectForCustomer')"
                                 :loading="loading" filterable remote reserve-keyword
                                 :remote-method="getCustomerByCodeOrName">
                        <el-option v-for="item in customerList" :key="item.code" :label="item.name"
                                   :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item :label="$t('wsComWaybill.status')" prop="status">
                      <el-select v-model="dataForm.status" clearable filterable
                                 :placeholder="$t('wsComWaybill.status')">
                        <el-option v-for="item in dict.statusList" :key="item.dictValue"
                                   :label="item.dictName"
                                   :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                  <el-col :span="9">
                    <el-form-item :label="$t('wsComWaybill.logisticsChannelCode')" prop="logisticsChannelCode">
                      <el-select v-model="dataForm.logisticsChannelCode" filterable clearable>
                        <el-option v-for="item in baseData.logisticsChannelByParamsList" :key="item.code"
                                   :label="item.name" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="15">
                    <el-form-item :label="$t('wsComWaybill.createDate')" prop="createDateArray" label-width='125px'>
                      <span slot="label">
                        <el-tooltip content="若选择入库时间，则只能查到入库过的数据">
                          <el-select style="width: 100px;"
                                     :class="dataForm.dateType === 'IN_TIME' ? 'custom-select' : ''"
                                     v-model="dataForm.dateType" filterable @change="dateTypeChangedHandle()">
                            <el-option :key="1" :label="$t('system.createDate')" value="CREATE_DATE"></el-option>
                            <el-option :key="3" :label="$t('tksTrackingWaybill.inTime')" value="IN_TIME"></el-option>
                          </el-select>
                        </el-tooltip>
                      </span>
                      <el-date-picker v-model="createDateArray" value-format="yyyy-MM-dd HH:mm:ss"
                                      type="datetimerange" :start-placeholder="$t('startTime')"
                                      :default-time="['00:00:00', '23:59:59']"
                                      :end-placeholder="$t('endTime')" style="width: 100%">
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10" v-show="!searchBoxShow">
                  <el-col :span="9">
                    <el-form-item :label="$t('wsComWaybill.providerCode')" prop="providerCode">
                      <el-select v-model="dataForm.providerCode" filterable clearable :placeholder="$t('wsComWaybill.providerCode')">
                        <el-option v-for="(item, index) in baseData.providerList" :key="index" :label="item.name" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item :label="$t('wsComWaybill.consigneeCountry')" prop="consigneeCountry">
                      <el-select v-model="dataForm.consigneeCountry" filterable placeholder="" clearable>
                        <el-option v-for="item in baseData.countryList" :key="item.code"
                                   :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('wsComWaybill.inRemark')" prop="inRemark">
                      <el-input v-model="dataForm.inRemark" clearable :placeholder="$t('wsComWaybill.inRemark')" />
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row :gutter="10" v-show="!searchBoxShow">
                  <el-col :span="9">
                    <el-form-item :label="$t('wsComWaybill.warehouseId')" prop="warehouseId">
                      <el-select v-model="dataForm.warehouseId" filterable clearable>
                        <el-option v-for="item in baseData.warehouseList" :key="item.id"
                                   :label="item.warehouseName"
                                   :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item :label="$t('wsComWaybill.arrearage')" prop="warehouseId">
                      <el-select filterable v-model="dataForm.arrearage"
                                 :placeholder="$t('wsComWaybill.arrearage')" clearable>
                        <el-option v-for="item in yesOrNo" :key="item.dictValue" :label="item.dictName"
                                   :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="$t('wsComWaybill.exceptionFlag')" prop="warehouseId">
                      <el-select filterable v-model="dataForm.exceptionFlag"
                                 :placeholder="$t('wsComWaybill.exceptionFlag')" clearable>
                        <el-option v-for="item in yesOrNo" :key="item.dictValue" :label="item.dictName"
                                   :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <div class="search_box_btn">
                <el-button type="primary" @click="searchHandle(1)" icon="el-icon-search">查询</el-button>
                <el-button @click.native="resetHandle()" icon="el-icon-refresh-right">重置</el-button>
                <!-- <el-button type="primary" v-if="$hasPermission('ws:comwaybill:export')" :disabled="this.dataList.length===0" @click="exportHandle()">{{ $t('export') }}</el-button> -->
                <el-button type="text" @click="searchBoxShowFn">{{ searchBoxShow ? '展开' : '收起' }}<i
                  :class="searchBoxShow ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></el-button>
              </div>
            </el-row>
          </el-form>
        </el-card>

        <el-tabs class="flex_tab no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
          <el-tab-pane v-for="(item, key) in statusArr" :key="key" :name="key">
            <div slot="label" style="min-width: 4em; text-align: center">{{ statusName(item.value) || '&nbsp;' }}
              <el-badge v-if="isBadge(item)" :value="item.num >0 ? item.num:''" :max="99999" class="item" type="primary">
              </el-badge>
            </div>
            <el-row class="optBtn_panel">
              <el-col :span="18" class="optBtn_leftFixed">
                <el-button size="mini" type="primary" icon="el-icon-download" v-if="$hasPermission('ws:comwaybill:printLastChannel') && item.value === 12" plain @click="batchDownloadLabelHandle()">
                  {{ $t('wsComWaybill.downloadLastChannel') }}
                </el-button>
                <el-button :loading="forecastLoading" v-if=" item.value === 11" size="mini" type="primary" plain @click="forecast()">
                  {{ $t('wsComWaybill.forecast') }}
                </el-button>
                <el-button v-if=" item.value === 11" size="mini" type="primary" plain @click="batchSortRate()">
                  {{ $t('wsComWaybill.batchSortRate') }}
                </el-button>
                <el-button v-if="item.value !== 10" size="mini" type="primary" icon="el-icon-receiving" plain @click="openQuickInarehouseDialog()">
                  {{ $t('wsComWaybill.quickInWarehouse') }}
                </el-button>
                <el-button v-if="$hasPermission('ws:comwaybill:mergeOrder') &&  item.value === 11 && (dataForm.logisticsType === 11  || dataForm.logisticsType === 12)" class="text-right"
                           size="mini" type="primary" plain @click="mergeOrder()">
                  {{ $t('fba.mergeOrder') }}
                </el-button>
                <el-dropdown class="dropdown margin_left10" v-show="item.value === 12" >
                  <el-button type="primary" icon="el-icon-printer" plain size="mini" >
                    打印操作<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <el-tooltip :disabled="$hasPermission('ws:comwaybill:printLastChannel')" content="未授权" placement="right" effect="light">
                        <el-link :underline="false" icon="el-icon-s-ticket" :disabled="!($hasPermission('ws:comwaybill:printLastChannel') && item.value === 12)" element-loading-spinner="el-icon-loading" v-loading="dataListLoading" @click="printLastChannel()">{{ $t('wsComWaybill.printLastChannel') }}</el-link>
                      </el-tooltip>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-tooltip :disabled="$hasPermission('ws:comwaybill:printLastChannel')" content="未授权" placement="right" effect="light">
                        <el-link  :underline="false" icon="el-icon-toilet-paper" @click="printLabelBarcode()" :disabled="!($hasPermission('ws:comwaybill:printLastChannel') && item.value === 12)"  element-loading-spinner="el-icon-loading" v-loading="dataListLoading">{{ $t('wsComWaybill.printLabelBarcode') }}</el-link>
                      </el-tooltip>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-tooltip :disabled="$hasPermission('ws:comwaybill:printProofOfPostage')" content="未授权" placement="right" effect="light">
                        <el-link  :underline="false" icon="el-icon-notebook-2" @click="printProofOfPostage()" :disabled="!($hasPermission('ws:comwaybill:printProofOfPostage') && item.value === 12)"  element-loading-spinner="el-icon-loading" v-loading="dataListLoading">{{ $t('wsComWaybill.printProofOfPostage') }}</el-link>
                      </el-tooltip>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-tooltip :disabled="$hasPermission('ws:comwaybill:printProofOfSignFor')" content="未授权" placement="right" effect="light">
                        <el-link  :underline="false" icon="el-icon-s-check" @click="printProofOfSignFor()" :disabled="!($hasPermission('ws:comwaybill:printProofOfSignFor') && item.value === 12)"  element-loading-spinner="el-icon-loading" v-loading="dataListLoading">{{ $t('wsComWaybill.printProofOfSignFor') }}</el-link>
                      </el-tooltip>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <el-dropdown class="dropdown margin_left10" v-if="item.value !== 10" >
                  <el-button type="warning" plain size="mini" icon="el-icon-top-left">
                    撤销操作<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:batchIntercept')">
                      <el-link type="warning" icon="el-icon-video-pause" :underline="false" @click="batchInterceptHandle()">{{ $t('wsComWaybill.batchIntercept') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:cancelForecast')" >
                      <el-link type="danger" icon="el-icon-close" :underline="false" @click="cancelForecast()">{{ $t('wsComWaybill.cancelForecast') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:cancelInbound')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="cancelWarehouseDialog('in')">{{ cancelOperation['in'].title }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:cancelReturnInStock')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="cancelWarehouseDialog('returnIn')">{{ cancelOperation['returnIn'].title }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:cancelReturnOutStock')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="cancelWarehouseDialog('returnOut')">{{ cancelOperation['returnOut'].title }}</el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- 退货操作 -->
                <el-dropdown class="dropdown margin_left10" v-if="item.value !== 10">
                  <el-button type="warning" plain size="mini" icon="el-icon-warning-outline">
                    异常操作<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:changeLogisticsProduct')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="changeLogisticsProduct()">{{ $t('wsComWaybill.changeProductLogistics') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:changeLogisticsChannel')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="openChangeLogisticsChannelDialog()">{{ $t('wsComWaybill.changeLogisticsChannel') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:changeLogisticsChannel')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="resetChannelProviderHandle()">{{ $t('wsComWaybill.resetChannelProvider') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:changeWarehouse')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="openChangeWarehouseDialog()">{{ $t('wsComWaybill.changeWarehouse') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:changeInboundTime')">
                      <el-link type="warning" icon="el-icon-s-release" :underline="false" @click="openChangeInboundTimeDialog()">{{ $t('wsComWaybill.changeInboundTime') }}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:exceptionMark')">
                      <el-link type="warning" icon="el-icon-lock" :underline="false" @click="openExceptionMarkDialog()">标记异常</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:returnInWarehouse')">
                      <el-link type="warning" icon="el-icon-right" :underline="false" @click="openReturnWarehouseDialog('in')">退货入库</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:returnOutWarehouse')">
                      <el-link type="warning" icon="el-icon-back" :underline="false" @click="openReturnWarehouseDialog('out')">退货出库</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-if="$hasPermission('ws:comwaybill:reforecast')">
                      <el-link type="danger" icon="el-icon-refresh-left" :underline="false" @click="batchReforcastConfirm()">{{ $t('wsComWaybill.reforecast') }}</el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- <el-button type="primary" v-if="$hasPermission('ws:comwaybill:export')" :disabled="this.dataList.length===0" @click="exportHandle()">{{ $t('export') }}</el-button> -->
              </el-col>

              <el-col :span="6" class="text-right">
                <el-button size="mini" type="primary" icon="el-icon-upload2" v-if="$hasPermission('ws:comwaybill:export')" plain @click="importPdf()">导入面单</el-button>
<!--                <el-button size="mini" type="primary" icon="el-icon-download"  v-if="dataList.length > 0 && $hasPermission('ws:comwaybill:export')" plain @click="exportHandle()">导出所有</el-button>-->
                <el-dropdown class="dropdown margin_left10" v-show="$hasPermission('ws:comwaybill:export')" >
                  <el-button type="primary" icon="el-icon-upload2" plain size="mini" >
                    导出<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <el-tooltip :disabled="$hasPermission('ws:comwaybill:export')" placement="right" effect="light">
                        <el-link :underline="false" icon="el-icon-check" :disabled="!dataListSelections || dataListSelections.length === 0" @click="exportHandle(dataListSelections.map(item => item.id).join())">导出选中</el-link>
                      </el-tooltip>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-tooltip :disabled="$hasPermission('ws:comwaybill:export')" placement="right" effect="light">
                        <el-link :underline="false" icon="el-icon-upload2" :disabled="!$hasPermission('ws:comwaybill:export')" @click="exportHandle()">导出所有</el-link>
                      </el-tooltip>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </el-col>
            </el-row>
          </el-tab-pane>

          <div class="flex_table" ref="tableElm" v-domResize="redraw">
            <ux-grid ref="tableData" v-loading="dataListLoading" :max-height="tableHeight" size="mini" :widthResize="true" :border="false"
                     :checkboxConfig="{checkMethod: selectable, highlight: false}" :show-overflow="true"
                     :row-class-name="tableRowClassName" @selection-change="_dataListSelectionChangeHandle">
              <!-- 动态显示表格 -->
              <ux-table-column type="checkbox" width='50' fixed="left" ></ux-table-column>
              <ux-table-column v-for="(item, index) in tableColumnsArr" :key="item.id"
                               :field="item.prop"  :title="item.label" :resizable="true" :border="false"
                               header-align="center" :align="item.align" :min-width="item.width"
                               :sortable="item.prop === 'createDate' || item.prop === 'logisticsProductCode' || item.prop === 'status'">
                <template slot="header" slot-scope="scope">
                  <div class="header-wrapper">
                    <span>{{ scope.column.title }}</span>
                    <el-tooltip  placement="top" :content="'点击复制该列所有' + scope.column.title" effect='light'>
                      <el-link v-show="[1,2,3,8].includes(index)" :underline="false" type="info" class='icon-copy' icon="el-icon-copy-document" @click="clipAllNo($event, item.prop)"></el-link>
                    </el-tooltip>
                  </div>
                </template>
                <template v-slot="scope">
                  <span>
                    <div v-if="index === 0 && scope.row.subWsComWaybillList && scope.row.subWsComWaybillList.length > 0 " @click="expandSubWaybill(scope.row, scope.rowIndex, scope)" class="caret-icon-style">
                      <i :class="scope.row.expand ? 'el-icon-caret-bottom':'el-icon-caret-right'" ></i>
                    </div>
                    <div v-else-if="index === 0 && scope.row.mergeType === 2" class="circle-icon-style">
                      <i class="circle"></i>
                    </div>
                    <span v-if="item.prop === 'customerRemark' || item.prop === 'inRemark'">
                      <span style="color:red;">{{ formatterFn(scope, item.prop) }}</span>
                    </span>
                    <span v-else-if="item.prop === 'customerOrderNo'" :class="getDisplayColor(scope.row)">
                      <el-tooltip :content="getCustomerNoToolsTip(scope.row)" effect="light" placement="top" v-if="scope.row.arrearage===1 || scope.row.exceptionFlag===1">
                        <li v-if="scope.row.mergeType === 2"><span v-text="scope.row.customerOrderNo"></span></li>
                        <span v-else v-text="scope.row.customerOrderNo"></span>
                      </el-tooltip>
                      <li v-else-if="scope.row.mergeType === 2"><span v-text="scope.row.customerOrderNo"></span></li>
                      <span v-else v-text="scope.row.customerOrderNo"></span>
                    </span>
                    <div v-else-if="item.prop === 'weightD'">
                      <span v-if="scope.row.status === 0">--</span>
                      <span v-else>{{ scope.row.weightD}}</span>
                    </div>
                    <div v-else-if="item.prop === 'volumeD'">
                      <span>{{ scope.row.volumeD.toFixed(3) }}</span>
                    </div>
                    <div v-else-if="item.prop === 'asynNoStatus'">
                        <template v-if='scope.row.asynNoStatus'>
                          <span v-if="scope.row.asynNoStatus.status === 30" class="warning">{{scope.row.asynNoStatus.msg}}</span>
                          <span v-else-if="scope.row.asynNoStatus.status === 0" class="info">{{scope.row.asynNoStatus.msg}}</span>
                          <span v-else-if="scope.row.asynNoStatus.status === 10" class="primary">{{scope.row.asynNoStatus.msg}}</span>
                          <span v-else-if="scope.row.asynNoStatus.status === 20" class="success">{{scope.row.asynNoStatus.msg}}</span>
                          <span v-else></span>
                        </template>
                    </div>
                    <span v-else>
                      <span>{{ formatterFn(scope, item.prop) }}</span>
                    </span>
                  </span>
                </template>
              </ux-table-column>
              <div style="z-index: 100;">
                <ux-table-column :label="$t('handle')" fixed="right"  header-align="center" align="center"
                                 min-width="166">
                  <template slot="header" slot-scope="scope">
                    <span>{{ $t('handle') }}</span>
                    <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                      <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting"
                               @click="settingCol(tableColumns)"></el-link>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <div style="overflow: visible;white-space: break-spaces;line-height: initial; ">
                      <el-link :underline="false" v-if="$hasPermission('ws:comwaybill:view')"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
                      <!-- 快递发票打印 -->
                      <el-link v-if="(scope.row.deliveryNode === 15 || scope.row.deliveryNode === 20)" :underline="false" @click="printInvoice(scope.row)"> 打印发票 </el-link>
                      <!--  重新入仓-->
                      <popconfirm i18nOperateValue="wsComWaybill.inWarehouseAfterCancel"
                                  @clickHandle="inWarehouseAfterCancel(scope.row.id)"
                                  :condition="$hasPermission('ws:comwaybill:inWarehouseAfterCancel') && scope.row.status === 10 && scope.row.deliveryNode === 10"></popconfirm>
                      <!--  强制入仓-->
                      <popconfirm i18nOperateValue="wsComWaybill.forcedInbound"
                                  @clickHandle="forcedInbound(scope.row.id)"
                                  :condition="$hasPermission('ws:comwaybill:forcedInbound') && scope.row.status === 13"></popconfirm>
                      <!--     强制出仓-->
                      <popconfirm i18nOperateValue="wsComWaybill.forcedOutbound"
                                  tipsContent="wsComWaybill.forcedOutboundTips"
                                  @clickHandle="forcedOutbound(scope.row.id)"
                                  :condition="$hasPermission('ws:comwaybill:forcedOutbound') && (scope.row.status === 23 && scope.row.mergeType !== 1)"></popconfirm>
                      <!--     取消合并-->
                      <popconfirm i18nOperateValue="wsComWaybill.cancelMerge"
                                  @clickHandle="cancelMerge(scope.row)"
                                  :condition="$hasPermission('ws:comwaybill:cancelMerge')
                                    && scope.row.mergeType === 2 && scope.row.status === 15
                                    && activeName === 'notGetTailOrderNo'"></popconfirm>
                      <!--   要求原单，未入仓，包裹数量大于1，未获取尾程    -->
                      <popconfirm i18nOperateValue="wsComWaybill.splitOrder"
                                  @clickHandle="splitWaybill(scope.row)"
                                  :condition="$hasPermission('ws:comwaybill:splitWaybill')
                                    && scope.row.status <= 10
                                    && scope.row.deliveryNode === 10
                                    && scope.row.packageQty > 1
                                    && scope.row.mergeType === 0"></popconfirm>
                      <el-link v-if="$hasPermission('ws:comwaybill:sortRate') && scope.row.status <= 15 && scope.row.logisticsType !== 10 && scope.row.deliveryNode === 10 && scope.row.mergeType !== 2"  :underline="false"
                               @click="sortRate(scope.row)" >{{ $t('wsComWaybill.sortRate') }}</el-link>
                      <el-dropdown size="mini" :hide-on-click="false"  v-if="($hasPermission('ws:comwaybill:export') || $hasPermission('ws:comwaybill:modifyDeclare') || $hasPermission('ws:comwaybill:modifyPackageInfo') || $hasPermission('ws:comwaybill:modifyOrderBaseInfo') || $hasPermission('ws:comwaybill:modifyConsigneeAddress') || $hasPermission('ws:comwaybill:importToForecast'))">
                        <a class="el-link el-link--default">
                            <span class="el-dropdown-link el-link--inner">
                              <i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                        </a>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item  v-if="$hasPermission('ws:comwaybill:modifyOrderBaseInfo')">
                            <el-link :underline="false" @click="modifyOrderBaseInfo(scope.row)">{{ $t('modifyOrderBaseInfo') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item  v-if="$hasPermission('ws:comwaybill:modifyConsigneeAddress')">
                            <el-link :underline="false" @click="modifyConsigneeAddress(scope.row)">{{ $t('modifyConsigneeAddress') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item  v-if="$hasPermission('ws:comwaybill:modifyPackageInfo') && scope.row.status <= 15">
                            <el-link :underline="false" @click="modifyPackageInfo(scope.row)">{{ $t('wsComWaybill.modifyPackageInfo') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item  v-if="$hasPermission('ws:comwaybill:modifyDeclare')">
                            <el-link :underline="false" @click="modifyDeclareInfo(scope.row)">{{ $t('wsComWaybill.modifyDeclareInfo') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item  v-if="$hasPermission('ws:comwaybill:modifyDeclare')">
                            <el-link :underline="false" @click="modifyDeclareInfoBatch(scope.row)">{{ $t('wsComWaybill.modifyDeclareInfoBatch') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="$hasPermission('ws:comwaybill:modifyPackageInfo') && scope.row.logisticsType === 12">
                            <el-link :underline="false" @click="inputReference(scope.row.orderId)">{{ $t('fba.inputReference') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item>
                            <el-link :underline="false" @click="openChangeInRemarkDialog(scope.row)">{{ $t('wsComWaybill.modifyInRemark') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="$hasPermission('ws:comwaybill:importToForecast')">
                            <el-link v-if="scope.row.status === 15" :underline="false"   @click="importToForecast(scope.row)">{{ $t('importToForecast') }}</el-link>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="$hasPermission('ws:comwaybill:export')">
                            <el-link :underline="false"  @click="exportHandle(scope.row.id)">{{ $t('export') }}</el-link>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>
                    </template>
                </ux-table-column>
              </div>
            </ux-grid>
          </div>
          <el-row class="optBtn_panel" >
              <el-col :md="10" style="padding-top:10px;">
                <span class="label">【预报】</span>
                <span class="label">重量:</span> <span class="text">{{ totalForecastWeight }}</span>
                <span class="label">体积重:</span> <span class="text">{{ totalForecastVolumeWeight }}</span>
                <span v-if="dataListSelections && dataListSelections.length > 0" class="label" style="font-weight:bold; color: red; margin-right: 30px;">已选 {{ dataListSelections.length }} 票</span>
                <span v-else-if="dataList && dataList.length > 0" class="label" style="font-weight:bold; color: blue; margin-right: 30px;">本页共 {{ dataList.length }} 票</span>
                <br/>
                <span class="label">【入库】</span>
                <span class="label">实重:</span> <span class="text">{{ totalWeight }}</span>
                <span class="label">体积重:</span> <span class="text">{{ totalVolumeWeight }}</span>
                <span class="label">方:</span> <span class="text">{{ totalVolume }}</span>
                <span class="label">{{$t('wsComWaybill.inedQty')}}:</span> <span class="text"><span style="color:green;">{{ totalInedQty}}</span> / {{ dataList.length }}</span>
              </el-col>
              <el-col :md="14" class="text-right">
                <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="limit"
                         :total="total" layout="total, sizes, prev, pager, next, jumper"
                         @size-change="pageSizeChangeSearchHandle" @current-change="pageCurrentChangeSearchHandle"></el-pagination>
              </el-col>
          </el-row>
        </el-tabs>
      </el-tabs>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <!--共用拦截组件-->
    <waybill-intercept-dialog ref="commonWaybillInterceptDialog" @commonWaybillInterceptDialogClose='batchInterceptHandleClose' @commonWaybillInterceptDialogConfirm="batchInterceptHandleConfirm"></waybill-intercept-dialog>
    <!--合并运单-->
    <com-waybill-merge-dialog v-if="comWaybillMergeDialogVisible" ref="comWaybillMergeDialog" @mergeAfterHandle="mergeAfterHandle"></com-waybill-merge-dialog>
    <!--拆单-->
    <com-waybill-split-dialog v-if="comWaybillSplitDialogVisible" ref="comWaybillSplitDialog" @splitWaybillAfterHandle="splitWaybillAfterHandle"></com-waybill-split-dialog>
    <!--修改订单基本信息-->
    <com-waybill-modify-order-base-info-dialog ref="comWaybillModifyOrderBaseInfoDialog" @modifyOrderBaseInfoAfterHandle="modifyOrderBaseInfoAfterHandle"></com-waybill-modify-order-base-info-dialog>
    <!--修改收件人信息-->
    <com-waybill-modifyConsigneeAddress-dialog ref="comWaybillModifyConsigneeAddressDialog" @modifyConsigneeAddressAfterHandle="modifyConsigneeAddressAfterHandle"></com-waybill-modifyConsigneeAddress-dialog>
    <!--修改申报信息-->
    <com-waybill-modifyDeclareInfo-dialog ref="comWaybillModifyDeclareInfoDialog" @modifyConsigneeAddressAfterHandle="waybillModifyDeclareAfterHandle" ></com-waybill-modifyDeclareInfo-dialog>
    <!--批量修改申报信息-->
    <com-waybill-modifyDeclareInfoBatch-dialog ref="comWaybillModifyDeclareInfoBatchDialog" @modifyConsigneeAddressAfterHandle="waybillModifyDeclareAfterHandle" ></com-waybill-modifyDeclareInfoBatch-dialog>
    <!--修改箱信息-->
    <com-waybill-modifyPackageInfo-dialog ref="comWaybillModifyPackageInfoDialog" @modifyPackageInfoAfterHandle="modifyPackageInfoAfterHandle"></com-waybill-modifyPackageInfo-dialog>
    <!--修改FBA箱信息-->
    <com-waybill-modifyFbaPackageInfo-dialog ref="comWaybillModifyFbaPackageInfoDialog" @modifyPackageInfoAfterHandle="modifyPackageInfoAfterHandle"></com-waybill-modifyFbaPackageInfo-dialog>
    <!--比价-->
    <com-waybill-sort-rate-dialog ref="comWaybillSortRateDialog" @sortRateAfterHandle="modifyPackageInfoAfterHandle"></com-waybill-sort-rate-dialog>
    <!--批量比价-->
    <com-waybill-batch-sort-rate-dialog ref="ComWaybillBatchSortRateDialog" @sortRateAfterHandle="modifyPackageInfoAfterHandle"></com-waybill-batch-sort-rate-dialog>
    <!--手工导入尾程预报-->
    <com-waybill-importToForecast-dialog ref="comWaybillImportToForecastDialog" @importToForecastAfterHandle="importToForecastAfterHandle"></com-waybill-importToForecast-dialog>
    <exportDetail v-if="exportVisible" ref="exportDetail" @backView="backView"/>
    <!-- 批量变更物流产品 -->
    <com-waybill-change-logistics-product-dialog v-if="changeLogisticsProductDialogVisible" ref="comWaybillChangeLogisticsProductDialog" @changeLogisticsProductAfterHandle="changeLogisticsProductAfterHandle"></com-waybill-change-logistics-product-dialog>
    <!-- 批量变更渠道 -->
    <com-waybill-change-logistics-channel-dialog ref="comWaybillChangeLogisticsChannelDialog" title="变更渠道" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></com-waybill-change-logistics-channel-dialog>
    <!-- 批量变更仓库 -->
    <com-waybill-change-warehouse-dialog ref="comWaybillChangeWarehouseDialog" title="变更仓库" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></com-waybill-change-warehouse-dialog>
    <!-- 快速入库  -->
    <progress-dialog ref="progressDialog" :title="$t('wsComWaybill.quickInWarehouse')" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></progress-dialog>
    <el-dialog :visible.sync="quickInWarehouseVisible" :title="$t('wsComWaybill.quickInWarehouse')" :show-close="false"  :close-on-press-escape="false" width="330px">
      <el-date-picker v-model="inWarehouseForm.inTime" type="datetime" :picker-options="{disabledDate: (time => time.getTime() > Date.now())}" value-format="yyyy-MM-dd HH:mm:ss" placeholder="入库时间，不填表示当前时间为入库时间" style="width: 290px;"></el-date-picker>
      <!-- <el-input style="margin-top: 20px;" type="textarea" v-model="inWarehouseForm.inRemark" placeholder="入库备注" :autosize="{ minRows: 2, maxRows: 5}"></el-input> -->
          <div style="font-size: 12px; margin-top: 10px;">
            <div style="font-weight: bold;">操作提示：</div>
            <div>1. 入库重量和尺寸以预报信息为准</div>
            <div>2. 若不指定入库时间，则当前时间作为入库时间</div>
            <div>3. 入库时间尽量不要早于预报时间，否则轨迹时间可能让人产生误解</div>
          </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="quickInWarehouseVisible = false">取 消</el-button>
        <el-button type="primary" @click="quickInWarehouseHandle()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 撤销操作：撤销入库、撤销出库、撤销退回入库、撤销退回出库 -->
    <progress-dialog ref="cancelProgressDialog" :title="cancelTitle" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></progress-dialog>
    <!-- 重置渠道供应商 -->
    <progress-dialog ref="resetChannelProviderProgressDialog" title="重置渠道供应商" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></progress-dialog>
    <!-- 标记异常 -->
    <com-waybill-exception-mark-dialog ref="ComWaybillExceptionMarkDialog" title="标记异常" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></com-waybill-exception-mark-dialog>
    <!-- 变更入库时间 -->
    <com-waybill-change-inbound-time-dialog ref="comWaybillChangeInboundTimeDialog" title="变更入库时间" no="waybillNo" noLabel="运单号" @callback="queryPageByParam"></com-waybill-change-inbound-time-dialog>
    <!-- 修改入库备注   -->
    <com-waybill-change-in-remark-dialog ref="comWaybillChangeInRemarkDialog" @callback="queryPageByParam"></com-waybill-change-in-remark-dialog>
    <!-- 批量退货出库 -->
    <com-waybill-return-out-dialog v-if="returnWarehouseDialogVisible" ref="comWaybillReturnWarehouseDialog" @callback="queryPageByParam"></com-waybill-return-out-dialog>
    <!-- 下载尾程面单 -->
    <labelDownload v-if="labelDownloadVisible" ref="labelDownload" @backView="backView"/>

    <!-- 预报 -->
    <el-dialog :title="$t('wsComWaybill.forecast')" :close-on-press-escape="false" :show-close="false" :visible.sync="forecastDialogVisible" width="40%" style="min-width: 500px; " :before-close="forecastHandleClose">
      <el-form :model="forecastDialogDataForm" ref="forecastDialogDataForm" label-width="108px">
        <el-row :gutter="20" type="flex" style="padding-top: 10px">
          <el-col :span="20">
            <el-form-item>
              <el-radio-group v-model="forecastDialogDataForm.refNoSource">
                <el-radio v-for="refNoSource in dict.refNoSourceList" :label="refNoSource.dictValue" :disabled="refNoSource.dictValue===11" :key="refNoSource.dictValue" @change="changeRefNoHandel">{{refNoSource.dictName}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" type="flex" style="padding-top: 10px" v-show="changeRefNo">
          <el-col :span="20">
            <el-form-item :label="$t('bdLogisticsChannel.pushApiNoType')" prop="pushApiNoType" label-width="120px">
              <el-select v-model="forecastDialogDataForm.pushApiNoType"  :placeholder="$t('select')">
                <el-option v-for="item in dict.pushApiNoTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="forecastLoading" @click="forecastHandleClose">取 消</el-button>
        <el-button type="primary" @click="batchForcastConfirm()">确 定</el-button>
      </span>
    </el-dialog>
    <not-in-scope-no v-if="notInScopeNosVisible" ref="notInScopeNos" @backView="notInScopeNosVisible=false"></not-in-scope-no>
    <label-upload-dialog ref="labelUploadDialog" :uploadFileUrl="uploadFileUrl" :excelSizeLimit="300" @successHandle="successHandle"></label-upload-dialog>
    <inputReferenceDialog ref='inputReferenceDialog'  @closeReferenceInfo='closeReferenceInfo' @inputReferenceEmit='inputReferenceEmit' v-if='inputReferenceDialogShow' ></inputReferenceDialog>

  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeNativeName, formatterShowName, formatterUser } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import notInScopeNo from '@/components/not-in-scope-no-detail'
import ExportDetail from '../bd/excel-export-dialog'
import ViewDetail from '@/components/ws/com-waybill-view-detail'
import threeNoInput from '@/components/multiple-no-input'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { getBeforeDay, getNowDate } from '@/utils/tools'
import WaybillInterceptDialog from '@/components/ws/waybill-intercept-dialog'
import comMixins from '@/mixins/comMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import LabelDownload from '@/components/co/label-download-dialog'
import ComWaybillMergeDialog from '@/components/ws/com-waybill-merge-dialog'
import ComWaybillSplitDialog from '@/components/ws/com-waybill-split-dialog'
import ComWaybillModifyConsigneeAddressDialog from '@/components/ws/com-waybill-modifyConsigneeAddress-dialog'
import ComWaybillModifyOrderBaseInfoDialog from '@/components/ws/com-waybill-modifyOrderBaseInfo-dialog'
import ComWaybillImportToForecastDialog from '@/components/ws/com-waybill-importToForecast-dialog'
import ComWaybillModifyDeclareInfoDialog from '@/components/ws/com-waybill-modifyDeclareInfo-dialog'
import ComWaybillModifyDeclareInfoBatchDialog from '@/components/ws/com-waybill-modifyDeclareInfoBatch-dialog'
import ComWaybillModifyPackageInfoDialog from '@/components/ws/com-waybill-modifyPackageInfo-dialog'
import ComWaybillModifyFbaPackageInfoDialog from '@/components/ws/com-waybill-modifyFbaPackageInfo-dialog'
import inputReferenceDialog from '../fba/input-reference-dialog'
import ComWaybillSortRateDialog from '@/components/ws/com-waybill-sortRate-dialog'
import ComWaybillBatchSortRateDialog from '@/components/ws/com-waybill-batch-sortRate-dialog'
import ComWaybillChangeLogisticsProductDialog from '@/components/ws/com-waybill-change-logistics_product'
import ComWaybillChangeLogisticsChannelDialog from '@/components/ws/com-waybill-change-logistics-channel'
import ComWaybillChangeWarehouseDialog from '@/components/ws/com-waybill-change-warehouse'
import ComWaybillReturnOutDialog from '@/components/ws/com-waybill-return-out-dialog'
import ProgressDialog from '@/components/progress-dialog'
import ComWaybillExceptionMarkDialog from '@/components/ws/com-waybill-exception-mark'
import ComWaybillChangeInboundTimeDialog from '@/components/ws/com-waybill-change-inbound-time'
import ComWaybillChangeInRemarkDialog from '@/components/ws/com-waybill-change-inremark'
import LabelUploadDialog from './label-upload-dialog'
import { printFn, printFn4Post } from '@/utils/print'
import clip from '@/utils/clipboard'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      // 上一个展开行索引
      preExpandRowObj: null,
      tableColumns: [
        { width: '0', prop: 'orderId', label: this.$t('wsComWaybill.orderId'), align: 'center', isShow: false, disabled: true },
        { width: '110', prop: 'objectName', label: this.$t('wsComWaybill.objectName'), align: 'center', isShow: true, disabled: false },
        { width: '120', prop: 'customerOrderNo', label: this.$t('wsComWaybill.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { width: '125', prop: 'waybillNo', label: this.$t('wsComWaybill.waybillNo'), align: 'center', isShow: true, disabled: false },
        { width: '135', prop: 'deliveryNo', label: this.$t('wsComWaybill.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { width: '100', prop: 'status', sortable: 'true', label: this.$t('wsComWaybill.status'), align: 'center', isShow: true, disabled: false },
        { width: '120', prop: 'logisticsProductCode', sortable: 'true', label: this.$t('wsComWaybill.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { width: '136', prop: 'asynNoStatus', label: this.$t('wsComWaybill.asynNoStatus'), align: 'center', isShow: true, disabled: false },
        { width: '105', prop: 'createDate', sortable: 'true', label: this.$t('wsComWaybill.createDate'), align: 'center', isShow: true, disabled: false },
        { width: '105', prop: 'inTime', sortable: 'true', label: this.$t('wsComWaybill.inWarehouseDate'), align: 'center', isShow: true, disabled: false },
        { width: '130', prop: 'postalTrackingNo', label: this.$t('wsComWaybill.postalTrackingNo'), align: 'center', isShow: true, disabled: false },
        { width: '120', prop: 'logisticsChannelCode', label: this.$t('wsComWaybill.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { width: '60', prop: 'packageQty', sortable: 'true', label: this.$t('wsComWaybill.packageQty'), align: 'center', isShow: true, disabled: false },
        { width: '90', prop: 'forecastWeightD', sortable: 'true', label: this.$t('wsComWaybill.forecastWeight'), align: 'right', isShow: true, disabled: false },
        { width: '90', prop: 'forecastVolumeWeightD', sortable: 'true', label: this.$t('wsComWaybill.forecastVolumeWeight'), align: 'right', isShow: true, disabled: false },
        { width: '90', prop: 'weightD', sortable: 'true', label: this.$t('wsComWaybill.weight'), align: 'right', isShow: true, disabled: false },
        { width: '90', prop: 'volumeWeightD', sortable: 'true', label: this.$t('wsComWaybill.volumeWeight'), align: 'right', isShow: true, disabled: false },
        { width: '90', prop: 'volumeD', sortable: 'true', label: this.$t('wsComWaybill.volume'), align: 'right', isShow: true, disabled: false },
        { width: '120', prop: 'providerCode', sortable: 'true', label: this.$t('wsComWaybill.providerCode'), align: 'center', isShow: true, disabled: false },
        { width: '80', prop: 'customsMethod', sortable: 'true', label: this.$t('coOrder.customsMethod'), align: 'center', isShow: true, disabled: false },
        { width: '100', prop: 'customerRemark', label: this.$t('wsComWaybill.customerRemark'), align: 'center', isShow: true, disabled: false },
        { width: '100', prop: 'inRemark', label: this.$t('wsComWaybill.inRemark'), align: 'center', isShow: true, disabled: false },
        { width: '80', prop: 'logisticsType', label: this.$t('wsComWaybill.orderLogisticsType'), align: 'center', isShow: false, disabled: false },
        { width: '80', prop: 'mergeType', sortable: 'true', label: this.$t('wsComWaybill.mergeType'), align: 'center', isShow: true, disabled: false },
        { width: '80', prop: 'consigneeCountry', label: this.$t('wsComWaybill.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { width: '150', prop: 'serviceId', label: this.$t('wsComWaybill.serviceId'), align: 'center', isShow: true, disabled: false },
        { width: '150', prop: 'warehouseId', label: this.$t('wsComWaybill.warehouseId'), align: 'center', isShow: true, disabled: false },
        { width: '150', prop: 'exceptionFlag', label: this.$t('wsComWaybill.exceptionFlag'), align: 'center', isShow: false, disabled: false },
        { width: '150', prop: 'arrearage', label: this.$t('wsComWaybill.arrearage'), align: 'center', isShow: false, disabled: false },
        { width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ws/comwaybill/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      totalWeight: 0,
      totalForecastWeight: 0,
      totalVolumeWeight: 0,
      totalVolume: 0,
      totalForecastVolumeWeight: 0,
      totalInedQty: 0,
      dataForm: {
        customerOrderNos: '',
        waybillNos: '',
        postalTrackingNos: '',
        bagNo: '',
        outBatchNos: '',
        packageCustomerNos: '',
        subDeliveryNos: '',
        deliveryNos: '',
        logisticsProductCode: '',
        logisticsChannelCode: '',
        objectName: '',
        warehouseId: '',
        logisticsType: 10,
        deliveryNode: 10,
        logisticsTypeList: '',
        deliveryNodeList: '',
        status: '',
        createDateFrom: '',
        createDateTo: '',
        inTimeFrom: '',
        inTimeTo: '',
        dateType: localStorage.getItem(this.$store.state.user.id + '-ws-com-waybill-dateType') || 'CREATE_DATE',
        consigneeCountry: '',
        waybillQuery: '1',
        dataIsolationType: 'warehouse'
      },
      // 快速入库
      inWarehouseForm: {
        waybillId: '',
        inTime: '',
        inRemark: ''
      },
      // 数据字典
      dict: {
        statusList: [],
        asynStatusList: [],
        waybillMergeTypeList: [],
        objectTypeList: [],
        pushApiNoTypeList: [],
        refNoSourceList: [],
        customsMethodList: [],
        orderLogisticsTypeList: []
      },
      customerList: [],
      createDateArray: [getBeforeDay(7), getNowDate()],
      inTimeArray: ['', ''],
      activeName: 'notGetTailOrderNo',
      masterActiveName: 'smallBag',
      tableName: 'ws-wscomwaybill',
      baseData: {
        logisticsProductByParamsList: [],
        logisticsChannelByParamsList: [],
        warehouseList: [],
        countryList: [],
        providerList: [],
        userList: []
      },
      loading: false,
      yesOrNo: [],
      notInScopeNoList: [],
      notInScopeNosVisible: false,
      changeRefNo: false,
      forecastLoading: false,
      cancelForecastLoading: false,
      exportVisible: false,
      firstRouteTo: true,
      batchInterceptDialogVisible: false,
      comWaybillMergeDialogVisible: false,
      comWaybillSplitDialogVisible: false,
      changeLogisticsProductDialogVisible: false,
      changeLogisticsChannelDialogVisible: false,
      quickInWarehouseVisible: false,
      returnWarehouseDialogVisible: false,
      uploadVisible: false,
      uploadFileUrl: '/co/order/uploadLabel',
      labelDownloadVisible: false,
      inputReferenceDialogShow: false,
      orderId: '',
      referenceList: [],
      // 撤销操作
      cancelType: 'none',
      cancelOperation: {
        none: { title: '', url: '' },
        in: { title: '撤销入库', url: '/ws/cominwarehousejob/cancelInbound' },
        // out: { title: '撤销出库', url: '' },
        returnIn: { title: '撤销退货入库', url: '/ws/comreturninbatch/cancelReturnInStock' },
        returnOut: { title: '撤销退货出库', url: '/ws/comreturnoutbatch/cancelReturnStock' }
      },
      batchInterceptDataForm: {
        opeareteDescription: '',
        wsComWaybillDTOList: null
      },
      forecastDialogVisible: false,
      forecastDialogDataForm: {
        ids: [],
        pushApiNoType: 10,
        refNoSource: 10
      },
      typeArr: {
        'smallBag': {
          value: 10,
          permission: this.$hasPermission('ws:comwaybill:smallBagTab')
        },
        'express': {
          value: 11,
          permission: this.$hasPermission('ws:comwaybill:expressTab')
        },
        'fba': {
          value: 12,
          permission: this.$hasPermission('ws:comwaybill:fbaTab')
        }
      },
      statusArr: {
        // 10=所有 11=未预报 12=已预报
        'all': {
          value: 10,
          num: 0
        },
        'notGetTailOrderNo': {
          value: 11,
          num: 0
        },
        'gotTailOrderNo': {
          value: 12,
          num: 0
        }
      }
    }
  },
  watch: {
    createDateArray: {
      handler (value, oldName) {
        if (value !== undefined && value !== '' && value !== null) {
          this.dataForm.createDateFrom = timestampFormat(value[0])
          this.dataForm.createDateTo = timestampFormat(value[1])
        } else {
          this.createDateArray = [getBeforeDay(7), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    },
    inTimeArray: {
      handler (value, oldName) {
        if (value !== undefined && value !== '' && value !== null) {
          this.dataForm.inTimeFrom = timestampFormat(value[0])
          this.dataForm.inTimeTo = timestampFormat(value[1])
        } else {
          this.inTimeArray = [getBeforeDay(16), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  created () {
    // 获取基础数据
    this.preExpandRowObj = null
    new Promise(this.masterTabPermissionCtrl).then(() => {
      this.getDict()
      this.getBaseData()
    })
    this.$nextTick(() => {
      this.dateTypeChangedHandle()
    })
  },
  activated () {
    console.log('this.firstRouteTo', this.firstRouteTo)
    if ((this.$route.query.status === '0' || this.$route.query.status === '15') && this.$route.query.activeName) {
      if (!this.firstRouteTo) {
        return
      }
      this.firstRouteTo = false
      this.$nextTick(() => {
        this.$set(this.dataForm, 'status', Number(this.$route.query.status))
        this.activeName = this.$route.query.activeName
        if (this.activeName === 'all') {
          this.dataForm.deliveryNode = ''
          this.dataForm.deliveryNodeList = ''
        }
        this.createDateArray = [getBeforeDay(7), getNowDate()]
        this.searchHandle()
      })
    } else {
      this.searchHandle()
    }
  },
  methods: {
    clip,
    clipAllNo (event, prop) {
      console.log('复制列的prop', prop)
      let noArr
      let split = prop.split('.')
      if (split.length > 1) {
        noArr = this.dataList.map(item => item[split[0]][split[1]]).filter((value, index, array) => {
          return (value !== '') && (value !== null) && (value !== undefined) && (array.indexOf(value) === index)
        })
      } else {
        noArr = this.dataList.map(item => item[prop]).filter((value, index, array) => {
          return (value !== '') && (value !== null) && (value !== undefined) && (array.indexOf(value) === index)
        })
      }
      this.clip(noArr.toString(), event)
    },
    // 不建议使用双向绑定的 data 属性（vue 监听会大数据会短暂的卡顿）, 要使用reloadData加载表格数据
    $getDataListCallback () {
      this.$refs.tableData.reloadData(this.dataList)
      this.showTotalInfo(this.dataList)
      // if (this.dataForm.dateType === 'IN_TIME') {
      //   this.$message({ showClose: true, message: '您选择了入库时间，只能筛选入库过的数据', type: 'warning' })
      // }
    },
    // 显示汇总信息
    showTotalInfo (dataList) {
      let totalWeight = 0
      let totalVolumeWeight = 0
      let totalVolume = 0
      let totalForecastWeight = 0
      let totalForecastVolumeWeight = 0
      let totalInedQty = 0
      dataList.forEach(item => {
        totalWeight += item.weightD
        totalVolumeWeight += item.volumeWeightD
        totalVolume += item.volumeD
        totalForecastWeight += item.forecastWeightD
        totalForecastVolumeWeight += item.forecastVolumeWeightD
        if (item.status !== 0) {
          totalInedQty += 1
        }
      })
      this.totalWeight = totalWeight.toFixed(3)
      this.totalVolumeWeight = totalVolumeWeight.toFixed(3)
      this.totalVolume = totalVolume.toFixed(3)
      this.totalForecastWeight = totalForecastWeight.toFixed(3)
      this.totalForecastVolumeWeight = totalForecastVolumeWeight.toFixed(3)
      this.totalInedQty = totalInedQty
    },
    _dataListSelectionChangeHandle (val) {
      this.dataListSelectionChangeHandle(val)
      this.showTotalInfo(this.dataListSelections.length > 0 ? this.dataListSelections : this.dataList)
    },
    // 添加插入行,从而伪造展开行的效果
    expandSubWaybill (row, index, scopeObj) {
      let insertRecords = this.$refs.tableData.getInsertRecords()
      if (insertRecords && insertRecords.length > 0) {
        this.$refs.tableData.remove(insertRecords)
      }
      this.$set(scopeObj.row, 'expand', !scopeObj.row.expand)
      // 往表格插入临时数据（不支持树结构），从指定位置插入一行或多行；第二个参数：指定位置的对象、null从第一行插入、-1 从最后插入
      if (typeof row.expand === 'boolean' && row.expand) {
        let insertBeforeRowObj = scopeObj.visibleData[index + 1]
        insertBeforeRowObj = insertBeforeRowObj === undefined ? -1 : insertBeforeRowObj
        console.log('insertBeforeRowObj', insertBeforeRowObj)
        this.$refs.tableData.insertRow(row.subWsComWaybillList, insertBeforeRowObj)
      }
      // 非当前行的折叠回去
      if (this.preExpandRowObj && this.preExpandRowObj.id !== row.id) {
        this.$set(this.preExpandRowObj, 'expand', false)
      }
      this.preExpandRowObj = row
    },
    /**
     * 设置异常或者欠费时显示的颜色
     * @param row
     */
    getDisplayColor (row) {
      let color = ''
      if (row.exceptionFlag === 1 || row.arrearage === 1) {
        color = 'danger'
      }
      return color
    },
    /**
     * 获取客户单号的弹出框提示语
     * @param row
     * @return {string}
     */
    getCustomerNoToolsTip (row) {
      let tip = ''
      if (row.arrearage === 1) {
        tip = '欠费'
      }
      if (row.exceptionFlag === 1) {
        tip = '异常'
      }
      if (row.exceptionFlag === 1 && row.arrearage === 1) {
        tip = '欠费并异常'
      }
      return tip
    },
    // 对FBA，小包，快递 TAB进行权限控制
    masterTabPermissionCtrl (resolve, reject) {
      this.getDefaultMasterTabVal().then((defaultMasterTabVal) => {
        defaultMasterTabVal = Number(defaultMasterTabVal)
        let enableTypeValArr = []
        for (let key in this.typeArr) {
          if (!this.typeArr[key].permission) {
            delete this.typeArr[key]
          } else {
            enableTypeValArr.push(this.typeArr[key].value)
          }
        }
        if (enableTypeValArr.length > 0) {
          if (enableTypeValArr.includes(defaultMasterTabVal)) {
            this.setMasterTabDefaultValue(defaultMasterTabVal)
          } else {
            this.setMasterTabDefaultValue(enableTypeValArr[0])
          }
        } else {
          this.setMasterTabDefaultValue(null)
        }
        resolve()
      })
    },
    async getDefaultMasterTabVal () {
      let val = await this.getDefaultMasterTab()
      return val
    },
    getDefaultMasterTab () {
      return new Promise((resolve, reject) => {
        this.$http.get(`/sys/params/getValueByCode/WAYBILL_SEARCH_DEFAULT_TAB`).then(({ data: res }) => {
          if (res.code !== 0) {
            resolve(this.$message.error(res.msg))
          }
          resolve(res.data)
        }).catch(() => {
          resolve(null)
        })
      })
    },
    setMasterTabDefaultValue (typeVal) {
      console.log('setMasterTabDefaultValue typeVal', typeVal)
      if (this.$hasPermission('ws:comwaybill:smallBagTab') && typeVal === 10) {
        this.dataForm.logisticsType = 10
        this.masterActiveName = 'smallBag'
        this.queryPageByParam()
      } else if (this.$hasPermission('ws:comwaybill:expressTab') && typeVal === 11) {
        this.dataForm.logisticsType = 11
        this.masterActiveName = 'express'
        this.queryPageByParam()
      } else if (this.$hasPermission('ws:comwaybill:fbaTab') && typeVal === 12) {
        this.dataForm.logisticsType = 12
        this.masterActiveName = 'fba'
        this.queryPageByParam()
      } else {
        return this.$message.error('未获得页面授权')
      }
    },
    async getDict () {
      this.dict.statusList = await this.getDictTypeList('WsComWaybillStatus')
      this.dict.asynStatusList = await this.getDictTypeList('scheduleStatus')
      this.dict.waybillMergeTypeList = await this.getDictTypeList('waybillMergeType')
      // 仓库运单对象类型
      this.dict.objectTypeList = await this.getDictTypeList('wsComWaybillObjectType')
      this.dict.orderLogisticsTypeList = await this.getDictTypeList('orderLogisticsType')
      this.dict.pushApiNoTypeList = await this.getDictTypeList('ChannelPushApiNoType') // 推送API单号类型
      this.dict.refNoSourceList = await this.getDictTypeList('RefNoSource') // 参考号来源
      this.dict.customsMethodList = await this.getDictTypeList('OrderCustomsMethod') // 报关方式
      // 是否
      this.yesOrNo = await this.getDictTypeList('yesOrNo')
    },
    async getBaseData () {
      this.baseData.providerList = await baseData(baseDataApi.providerList, { status: 11 }).catch(() => {})
      this.baseData.logisticsProductByParamsList = await baseData(baseDataApi.listAllByCurrent).catch(() => {})
      this.baseData.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByParamsList, { autoFilter: true }).catch(() => {})
      this.baseData.warehouseList = await baseData(baseDataApi.warehouseInfoList).catch(() => {})
      this.baseData.countryList = await baseData(baseDataApi.countryList).catch(() => {})
      // 用户信息
      this.baseData.userList = await baseData(baseDataApi.allUserList).catch(() => {})
    },
    pageSizeChangeSearchHandle (val) {
      this.page = 1
      this.limit = val
      this.searchHandle()
    },
    // 分页, 当前页
    pageCurrentChangeSearchHandle (val) {
      this.page = val
      console.log('pageCurrentChangeSearchHandle')
      this.searchHandle()
    },
    // 查询前预提交单号到后台
    searchHandle (page) {
      if (page) {
        this.page = page
      }
      let initFlag = this.$refs.threeNoInput.setValue()
      if (!initFlag) {
        return
      }
      // 查询
      // 查询条件预缓存到后台
      let params = {
        order: this.order,
        orderField: this.orderField,
        page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
        limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
        ...this.dataForm
      }
      this.$http.post('/common/cacheQueryParams', params).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        let tempDataForm = { ...this.dataForm }
        this.dataForm = { 'queryId': res.data }
        // 查询
        this.getDataList()
        this.dataForm = { ...tempDataForm }
      }).catch(() => {})
      // 单号不匹配
      this.findNotInScopeNos()
    },
    resetHandle () {
      this.createDateArray = [getBeforeDay(7), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    },
    tableRowClassName ({ row, rowIndex }) {
      if (row.mergeType === 2 && (this.activeName === 'notGetTailOrderNo' || this.activeName === 'gotTailOrderNo')) {
        return 'table-light-blue-row'
      }
      return ''
    },
    selectable ({ row }) {
      if (row.mergeType === 2) {
        return false
      }
      // if (row.mergeType === 2 && (this.activeName === 'notGetTailOrderNo' || this.activeName === 'gotTailOrderNo')) {
      //   return false
      // }
      // // 未入库不能预报
      // if (this.activeName === 'notGetTailOrderNo' && row.status !== 15) {
      //   return false
      // }
      return true
    },
    getKey (row) {
      return row.id
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsProductCode':
          value = formatterCodeNativeName(scope.row.logisticsProductCode, this.baseData.logisticsProductByParamsList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeNativeName(scope.row.logisticsChannelCode, this.baseData.logisticsChannelByParamsList)
          break
        case 'consigneeCountry':
          value = formatterCodeNativeName(scope.row.consigneeCountry, this.baseData.countryList)
          break
        case 'providerCode':
          value = formatterCodeNativeName(scope.row.providerCode, this.baseData.providerList)
          break
        case 'serviceId':
          value = formatterUser(scope.row.serviceId, this.baseData.userList)
          break
        case 'warehouseId':
          value = formatterShowName(scope.row.warehouseId, this.baseData.warehouseList, 'warehouseName')
          break
        case 'forecastWeightD':
          value = scope.row.forecastWeightD
          break
        case 'objectType':
          value = formatterType(scope.row.objectType, this.dict.objectTypeList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.dict.statusList)
          break
        case 'logisticsType':
          value = formatterType(scope.row.logisticsType, this.dict.orderLogisticsTypeList)
          break
        case 'mergeType':
          value = formatterType(scope.row.mergeType, this.dict.waybillMergeTypeList)
          break
        case 'customsMethod':
          value = formatterType(scope.row.customsMethod, this.dict.customsMethodList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    showColor (status) {
      let styleCss = ''
      if (status === '30') {
        styleCss = 'color: red'
      } else if (status === '10' || status === '0') {
        styleCss = 'color: gray'
      }
      return styleCss
    },
    // 格式化其他状态列显示
    formatterFnByOtherStatus (status, prop) {
      let value
      switch (prop) {
        case 'asynNoStatus':
          value = formatterType(status, this.dict.asynStatusList)
          break
        default:
          value = ''
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    isBadge (item) {
      let ret = true
      if (item.value === 70) {
        ret = false
        return ret
      }
      return ret
    },
    statusName (val) {
      let name = ''
      if (val === 10) {
        name = '所有'
      }
      if (val === 11) {
        name = '未获取尾程派送单号'
      }
      if (val === 12) {
        name = '已获取尾程派送单号'
      }
      return name
    },
    typeName (val) {
      let name = ''
      if (val === 10) {
        name = '小包'
      } else if (val === 11) {
        name = '快递'
      } else {
        name = 'FBA'
      }
      return name
    },
    // 下载面单
    batchDownloadLabelHandle () {
      let data = this.dataListSelections
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.batchPrint'),
          type: 'warning',
          duration: 1000
        })
      }
      this.labelDownloadVisible = true
      this.$nextTick(() => {
        this.$refs.downloadFileButtonLoading = false
        this.$refs.labelDownload.dataList = data
        this.$refs.labelDownload.init()
      })
    },
    // 打印发票
    printInvoice (data) {
      // if (data.status !== 11 && data.status !== 12 && data.status !== 13) {
      //   return this.$message.warning('请选择已预报或者已入库或者已出库数据')
      // } else {
      // printFn('/co/order/customer/invoice?orderId=' + data.orderId)
      // }
      printFn('/co/order/customer/invoice?ids=' + [data.orderId])
    },

    batchInterceptHandle () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.warning(this.$t('select'))
      }
      this.$nextTick(() => {
        this.$refs.commonWaybillInterceptDialog.init()
      })
    },
    /**
     * 撤销后入仓
     **/
    inWarehouseAfterCancel (id) {
      this.$http.post('/ws/comwaybill/inWarehouseAfterCancel', 'id=' + id).then(({ data: res }) => {
        // console.log('res', res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.queryPageByParam()
          }
        })
      }).catch(() => {})
    },
    forcedInbound (id) {
      this.$http.post('/ws/cominwarehousejob/forcedInbound', 'id=' + id).then(({ data: res }) => {
        // console.log('res', res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            // console.log('onClose')
            this.queryPageByParam()
          }
        })
      }).catch(() => {})
    },
    forcedOutbound (id) {
      this.$http.post('/ws/cominwarehousejob/forcedOutbound', 'id=' + id).then(({ data: res }) => {
        // console.log('res', res)
        if (res.code !== 0) {
          return this.$message({
            dangerouslyUseHTMLString: true,
            message: res.msg,
            type: 'error',
            duration: 2500
          })
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            // console.log('onClose')
            this.queryPageByParam()
          }
        })
      }).catch(() => {})
    },
    forecastBackup () {
      this.forecastLoading = true
      if (!this.dataListSelections.length) {
        this.forecastLoading = false
        return this.$message.warning(this.$t('select'))
      }
      let idArray = this.dataListSelections.map(item => item.id)
      this.$http.post('/ws/cominwarehousejob/forecast', idArray).then(({ data: res }) => {
        this.forecastLoading = false
        if (res.code !== 0) {
          return this.$message({
            dangerouslyUseHTMLString: true,
            message: res.msg,
            type: 'error',
            duration: 2500
          })
        }
        return this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.queryPageByParam()
          }
        })
      }).catch(() => {
      })
    },
    forecast () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      this.forecastDialogDataForm.ids = this.dataListSelections.map(item => item.id)
      this.forecastDialogVisible = true
    },
    forecastHandleClose () {
      this.$refs.forecastDialogDataForm.resetFields()
      this.forecastDialogVisible = false
    },
    forecastHandleConfirm () {
      this.forecastLoading = true
      this.$http.post('/ws/cominwarehousejob/forecast2LastChannel', this.forecastDialogDataForm).then(({ data: res }) => {
        this.forecastLoading = false
        if (res.code !== 0) {
          return this.$message({
            dangerouslyUseHTMLString: true,
            message: res.msg,
            type: 'error',
            duration: 2500
          })
        }
        this.forecastHandleClose()
        return this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.queryPageByParam()
          }
        })
      }).catch(() => {
      })
    },
    // 尾程预报（并发请求）
    batchForcastConfirm () {
      this.forecastDialogVisible = false
      this.$refs.progressDialog.refreshTitle = '尾程预报'
      this.$refs.progressDialog.init(this.dataListSelections.length)
      this.$nextTick(() => {
        // 10个并发请求
        this.$refs.progressDialog.concurrencyRequest(this.dataListSelections, 10, this.forcastRequest)
      })
    },
    // 单笔尾程预报请求
    forcastRequest (row) {
      console.log('row', row)
      let reqData = {
        ids: [row.id],
        pushApiNoType: this.forecastDialogDataForm.pushApiNoType,
        refNoSource: this.forecastDialogDataForm.refNoSource
      }
      this.$http.post('/ws/cominwarehousejob/forecast2LastChannel', reqData).then(({ data: res }) => {
        if (res.code !== 0) {
          // 请求失败通知
          this.$refs.progressDialog.fail(row.waybillNo, res.msg)
        } else {
          // 请求成功通知
          this.$refs.progressDialog.success()
        }
      }).catch(() => {
        // 请求失败通知
        this.$refs.progressDialog.fail(row.waybillNo, '请求超时')
      })
    },
    // 异常重新预报（并发请求）
    batchReforcastConfirm () {
      this.reforecastDialogVisible = false
      this.$refs.progressDialog.refreshTitle = '取消并重新预报'
      this.$refs.progressDialog.init(this.dataListSelections.length)
      this.$nextTick(() => {
        // 10个并发请求
        this.$refs.progressDialog.concurrencyRequest(this.dataListSelections, 10, this.reforcastRequest)
      })
    },
    // 单笔异常重新预报请求
    reforcastRequest (row) {
      console.log('row', row)
      this.$http.post('/ws/comwaybill/reforecast', row.orderId, { headers: { 'Content-Type': 'application/json;charset=UTF-8' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          // 请求失败通知
          this.$refs.progressDialog.fail(row.waybillNo, res.msg)
        } else {
          // 请求成功通知
          this.$refs.progressDialog.success()
        }
      }).catch(() => {
        // 请求失败通知
        this.$refs.progressDialog.fail(row.waybillNo, '请求超时')
      })
    },
    changeRefNoHandel () {
      // 如果换参考号预报被选中时, 则API单号来源为 订单ID
      if (this.forecastDialogDataForm.refNoSource === 11) {
        this.forecastDialogDataForm.pushApiNoType = 10
        this.changeRefNo = true
      } else {
        this.changeRefNo = false
      }
    },
    /**
     *
     * 取消预报
     * @return {ElMessageComponent}
     */
    cancelForecast () {
      this.$confirm('确定要【取消尾程预报】么？该操作会取消尾程物流商的订单，将无法恢复，请慎重操作！', this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.cancelForecastLoading = true
        if (!this.dataListSelections.length) {
          this.cancelForecastLoading = false
          return this.$message.warning(this.$t('select'))
        }
        let idArray = this.dataListSelections.map(item => item.id)
        this.$http.post('/ws/cominwarehousejob/cancelForecast', idArray).then(({ data: res }) => {
          this.cancelForecastLoading = false
          if (res.code !== 0) {
            return this.$message({
              dangerouslyUseHTMLString: true,
              message: res.msg,
              type: 'error',
              duration: 2500
            })
          }
          return this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.queryPageByParam()
            }
          })
        }).catch(() => {
        })
      })
    },
    printLabelBarcode () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      let orderIdArr = this.dataListSelections.map(item => item.id)
      let param = { 'ids': orderIdArr }
      printFn4Post('/ws/comwaybill/printLabelBarcode', param)
    },
    printLastChannel () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      let orderIdArr = this.dataListSelections.map(item => item.orderId)
      printFn('/co/order/channel/label?orderByMode=11&nos=' + orderIdArr)
    },
    printProofOfPostage () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      let orderIdArr = this.dataListSelections.map(item => item.orderId)
      printFn('/co/order/printProofOfPostage?ids=' + orderIdArr)
    },
    printProofOfSignFor () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      let orderIdArr = this.dataListSelections.map(item => item.orderId)
      printFn('/co/order/printProofOfSignFor?ids=' + orderIdArr)
    },
    mergeOrder () {
      this.comWaybillMergeDialogVisible = true
      this.$nextTick(() => {
        this.$refs.comWaybillMergeDialog.init()
      })
    },
    // 导出
    exportHandle (ids) {
      this.exportVisible = true
      this.$nextTick(() => {
        let masterDTOName = 'WsComWaybillDTO'
        this.$refs.exportDetail.dataForm.masterDTOName = masterDTOName
        if (ids) {
          this.$refs.exportDetail.queryDataForm.ids = ids
        } else {
          this.$refs.exportDetail.queryDataForm = this.dataForm
        }
        this.$refs.exportDetail.init()
      })
    },
    splitWaybill (row) {
      this.comWaybillSplitDialogVisible = true
      this.$nextTick(() => {
        this.$refs.comWaybillSplitDialog.dataForm.waybillId = row.id
        this.$refs.comWaybillSplitDialog.dataForm.newCustomerOrderNo = row.customerOrderNo + '-'
        this.$refs.comWaybillSplitDialog.init()
      })
    },
    cancelMerge (row) {
      this.$http.put(`/ws/comwaybill/cancelMerge/${row.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.queryPageByParam()
          }
        })
      }).catch(() => {})
    },
    modifyConsigneeAddress (row) {
      this.$refs.comWaybillModifyConsigneeAddressDialog.dataForm.id = row.orderId
      this.$refs.comWaybillModifyConsigneeAddressDialog.waybillId = row.id
      this.$refs.comWaybillModifyConsigneeAddressDialog.dataForm.orderLogisticsType = row.logisticsType
      this.$refs.comWaybillModifyConsigneeAddressDialog.init()
    },
    modifyOrderBaseInfo (row) {
      this.$refs.comWaybillModifyOrderBaseInfoDialog.dataForm.id = row.orderId
      this.$refs.comWaybillModifyOrderBaseInfoDialog.waybillId = row.id
      this.$refs.comWaybillModifyOrderBaseInfoDialog.dataForm.orderLogisticsType = row.logisticsType
      this.$refs.comWaybillModifyOrderBaseInfoDialog.init()
    },
    // 手工导入尾程单号预报
    importToForecast (row) {
      this.$refs.comWaybillImportToForecastDialog.dataForm.waybillId = row.id
      this.$refs.comWaybillImportToForecastDialog.waybillDataForm.waybillNo = row.waybillNo
      this.$refs.comWaybillImportToForecastDialog.waybillDataForm.logisticsProductCode = row.logisticsProductCode
      this.$refs.comWaybillImportToForecastDialog.waybillDataForm.customerOrderNo = row.customerOrderNo
      this.$refs.comWaybillImportToForecastDialog.waybillDataForm.customerName = row.objectName
      this.$refs.comWaybillImportToForecastDialog.init()
    },
    modifyDeclareInfo (row) {
      // todo  分页弹出
      this.$refs.comWaybillModifyDeclareInfoDialog.init(row.orderId, row.id)
    },
    modifyDeclareInfoBatch (row) {
      // todo  分页弹出
      this.$refs.comWaybillModifyDeclareInfoBatchDialog.init(row.orderId, row.id)
    },
    modifyPackageInfo (row) {
      if (row.logisticsType === 12) {
        this.$refs.comWaybillModifyFbaPackageInfoDialog.init(row.orderId, row.id)
      } else {
        this.$refs.comWaybillModifyPackageInfoDialog.init(row.id)
      }
    },
    sortRate (row) {
      this.$refs.comWaybillSortRateDialog.init(row.orderId)
    },
    batchSortRate () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      let idArray = this.dataListSelections.map(item => item.orderId)
      this.$refs.ComWaybillBatchSortRateDialog.init(idArray)
    },
    // 变更物流产品
    changeLogisticsProduct () {
      let data = this.dataListSelections
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('select'),
          type: 'warning',
          duration: 1000
        })
      }
      let logisticsTypeSet = new Set()
      let waybillStatusSet = new Set()
      for (let i = 0; i < data.length; i++) {
        // if (data[i].status !== 0) {
        //   this.$refs.tableData.clearSelection()
        //   return this.$message.warning('请选择记录后操作')
        // }
        logisticsTypeSet.add(data[i].logisticsType)
        waybillStatusSet.add(data[i].waybillStatus)
        if (logisticsTypeSet.size > 1) {
          return this.$message.warning('请选择同一种订单物流类型')
        }
      }
      if ([...waybillStatusSet].some(item => item !== 105 && item !== 25 && item > 15)) {
        return this.$message.warning('运单正在操作过程中，不能更换物流产品')
      }
      let _self = this
      // 校验运单状态--已预报-0、已揽收-5、已签收-10、已入库-15和退回入库-105才可以操作
      this.checkStatus4ChangeLogisticsProduct(data, function () {
        let waybillNos = []
        for (let i = 0; i < data.length; i++) {
          waybillNos.push(data[i].waybillNo)
        }
        _self.changeLogisticsProductDialogVisible = true
        _self.$nextTick(() => {
          // 数组去重
          _self.$refs.comWaybillChangeLogisticsProductDialog.dataForm.waybillNos = Array.from(new Set(waybillNos))
          _self.$refs.comWaybillChangeLogisticsProductDialog.logisticsType = _self.dataForm.logisticsType
          _self.$refs.comWaybillChangeLogisticsProductDialog.init()
        })
      })
    },
    // 校验运单状态--已预报-0、已揽收-5、已签收-10、已入库-15和退回入库-105才可以操作
    checkStatus4ChangeLogisticsProduct (data, callback) {
      let waybillNos = []
      data.forEach(d => {
        let waybillStatus = String(d.status)
        if (waybillStatus !== '0' && waybillStatus !== '5' && waybillStatus !== '10' && waybillStatus !== '15' && waybillStatus !== '25' && waybillStatus !== '105') {
          waybillNos.push(d.waybillNo)
        }
      })
      if (waybillNos.length > 0) {
        this.$refs.tableData.clearSelection()
        return this.$message.warning('运单号：【' + waybillNos.toString() + '】,只允许处理以下运单状态：已预报、已揽收、已签收、已入库、已出库和退回入库')
      }
      callback()
    },

    batchInterceptHandleClose () {
      this.$refs.tableData.clearSelection()
    },
    mergeAfterHandle () {
      this.queryPageByParam()
    },
    splitWaybillAfterHandle () {
      this.queryPageByParam()
    },
    modifyConsigneeAddressAfterHandle () {
      this.queryPageByParam()
    },
    modifyOrderBaseInfoAfterHandle () {
      this.queryPageByParam()
    },
    importToForecastAfterHandle () {
      this.queryPageByParam()
    },
    waybillModifyDeclareAfterHandle () {
      this.queryPageByParam()
    },
    modifyPackageInfoAfterHandle () {
      this.queryPageByParam()
    },
    changeLogisticsProductAfterHandle () {
      this.changeLogisticsProductDialogVisible = false
      this.queryPageByParam()
    },
    masterTabsClick (tab) {
      // console.log('tab', tab)
      switch (tab.name) {
        case 'smallBag':
          this.dataForm.logisticsType = 10
          this.searchHandle()
          break
        case 'express':
          this.dataForm.logisticsType = 11
          this.searchHandle()
          break
        case 'fba':
          this.dataForm.logisticsType = 12
          this.searchHandle()
          break
        default:
          this.dataForm.logisticsType = 10
          this.searchHandle()
      }
    },
    tabsClick (tab, event) {
      switch (tab.name) {
        case 'all':
          this.dataForm.deliveryNode = ''
          this.dataForm.deliveryNodeList = ''
          this.searchHandle()
          break
        case 'notGetTailOrderNo':
          this.dataForm.deliveryNode = 10
          this.dataForm.deliveryNodeList = ''
          this.searchHandle()
          break
        case 'gotTailOrderNo':
          this.dataForm.deliveryNode = ''
          this.dataForm.deliveryNodeList = '15,20'
          this.searchHandle()
          break
      }
    },
    batchInterceptHandleConfirm (data) {
      this.batchInterceptDataForm.wsComWaybillDTOList = this.dataListSelections
      this.batchInterceptDataForm.opeareteDescription = data.exceptionDescription
      this.$http.post('/ws/comwaybill/interceptWaybill', this.batchInterceptDataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.$nextTick(() => {
              this.$refs.commonWaybillInterceptDialog.commonWaybillInterceptDialogClose()
            })
            this.searchHandle()
          }
        })
      }).catch(() => {})
    },
    // 查看未匹配单号
    showNotInScopeNosHandle () {
      this.notInScopeNosVisible = true
      this.$nextTick(() => {
        this.$refs.notInScopeNos.noList = this.notInScopeNoList
        this.$refs.notInScopeNos.init()
      })
    },
    findNotInScopeNos () {
      this.$http.post('/ws/comwaybill/findNoInScopeNos', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.notInScopeNoList = res.data || []
      }).catch(() => {})
    },
    // 撤销入库/出库/退回入库/退回出库
    cancelWarehouseDialog (cancelType) {
      this.cancelType = cancelType
      this.$confirm(`确定要【${this.cancelOperation[cancelType].title}】么？请慎重操作！`, this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.cancelWarehouseHandle()
      })
    },
    // 并发撤销
    cancelWarehouseHandle () {
      this.$refs.cancelProgressDialog.init(this.dataListSelections.length)
      this.$nextTick(() => {
        // 10个并发请求
        this.$refs.cancelProgressDialog.concurrencyRequest(this.dataListSelections, 10, this.cancelWarehouseRequest)
      })
    },
    // 单笔撤销请求
    cancelWarehouseRequest (row) {
      let url = this.cancelOperation[this.cancelType].url
      this.$http.post(url, { waybillOrDeliveryNo: row.waybillNo, deliveryNo: row.waybillNo, waybillNoOrDeliveryNo: row.waybillNo }).then(({ data: res }) => {
        if (res.code !== 0) {
          // 请求失败通知
          this.$refs.cancelProgressDialog.fail(row.waybillNo, res.msg)
        } else {
          // 请求成功通知
          this.$refs.cancelProgressDialog.success()
        }
      }).catch(() => {
        // 请求失败通知
        this.$refs.cancelProgressDialog.fail(row.waybillNo, '请求超时')
      })
    },
    // 打开变更仓库弹出框
    openChangeWarehouseDialog () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.error('请先选择记录')
      }
      this.$nextTick(() => {
        this.$refs.comWaybillChangeWarehouseDialog.visible = true
        this.$refs.comWaybillChangeWarehouseDialog.init(this.dataListSelections.map(item => {
          return { 'waybillNo': item.waybillNo, 'waybillId': item.id }
        }))
      })
    },
    // 重置渠道供应商
    resetChannelProviderHandle () {
      this.$refs.resetChannelProviderProgressDialog.init(this.dataListSelections.length)
      this.$nextTick(() => {
        // 10个并发请求
        this.$refs.resetChannelProviderProgressDialog.concurrencyRequest(this.dataListSelections, 10, this.resetChannelProviderRequest)
      })
    },
    // 单笔重置渠道供应商请求
    resetChannelProviderRequest (row) {
      this.$http.post('/ws/comwaybill/resetChannelProvider', row.id, { headers: { 'Content-Type': 'application/json;charset=UTF-8' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          // 请求失败通知
          this.$refs.resetChannelProviderProgressDialog.fail(row.waybillNo, res.msg)
        } else {
          // 请求成功通知
          this.$refs.resetChannelProviderProgressDialog.success()
        }
      }).catch(() => {
        // 请求失败通知
        this.$refs.resetChannelProviderProgressDialog.fail(row.waybillNo, '请求超时')
      })
    },
    // 打开快速入库弹出框
    openQuickInarehouseDialog () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.error('请先选择记录')
      }
      this.quickInWarehouseVisible = true
      this.inWarehouseForm.waybillId = ''
      this.inWarehouseForm.inTime = ''
      this.inWarehouseForm.inRemark = ''
    },
    quickInWarehouseHandle () {
      // 入库时间若早于预报时间，提示
      let inTime = this.inWarehouseForm.inTime
      if (inTime) {
        let warningWaybillNoList = this.dataListSelections.filter(item => inTime < item.createDate).map(item => item.waybillNo)
        if (warningWaybillNoList && warningWaybillNoList.length > 0) {
          this.$confirm('入库时间早于预报时间，确定要按指定的入库时间来入库么？', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {
            this.batchQuickInWarehouse()
          }).catch(() => { })
        } else {
          this.batchQuickInWarehouse()
        }
      } else {
        this.batchQuickInWarehouse()
      }
    },
    // 快速入库（并发请求）
    batchQuickInWarehouse () {
      this.quickInWarehouseVisible = false
      this.$refs.progressDialog.refreshTitle = '快速入库'
      this.$refs.progressDialog.init(this.dataListSelections.length)
      this.$nextTick(() => {
        // 10个并发请求
        this.$refs.progressDialog.concurrencyRequest(this.dataListSelections, 10, this.quickInWarehouseRequest)
      })
    },
    // 单笔快速入库请求
    quickInWarehouseRequest (row) {
      console.log('row', row)
      let reqData = {
        waybillId: row.id,
        inTime: this.inWarehouseForm.inTime,
        inRemark: row.inRemark
      }
      this.$http.post('/ws/cominwarehousejob/quickInWarehouse', reqData).then(({ data: res }) => {
        if (res.code !== 0) {
          // 请求失败通知
          this.$refs.progressDialog.fail(row.waybillNo, res.msg)
        } else {
          // 请求成功通知
          this.$refs.progressDialog.success()
        }
      }).catch(() => {
        // 请求失败通知
        this.$refs.progressDialog.fail(row.waybillNo, '请求超时')
      })
    },
    // 打开变更渠道弹出框
    openChangeLogisticsChannelDialog () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.error('请先选择记录')
      }
      this.$nextTick(() => {
        this.$refs.comWaybillChangeLogisticsChannelDialog.visible = true
        this.$refs.comWaybillChangeLogisticsChannelDialog.init(this.dataListSelections.map(item => {
          return { 'waybillNo': item.waybillNo, 'waybillId': item.id }
        }))
      })
    },
    // 打开变更入库时间弹出框
    openChangeInboundTimeDialog () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.error('请先选择记录')
      }
      this.$nextTick(() => {
        this.$refs.comWaybillChangeInboundTimeDialog.visible = true
        this.$refs.comWaybillChangeInboundTimeDialog.init(this.dataListSelections.map(item => {
          return { 'waybillNo': item.waybillNo, 'waybillId': item.id }
        }))
      })
    },
    // 打开标记异常弹出框
    openExceptionMarkDialog () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.error('请先选择记录')
      }
      this.$nextTick(() => {
        this.$refs.ComWaybillExceptionMarkDialog.visible = true
        this.$refs.ComWaybillExceptionMarkDialog.init(this.dataListSelections.map(item => {
          return { 'waybillNo': item.waybillNo }
        }))
      })
    },
    importPdf () {
      this.uploadVisible = true
      this.$refs.labelUploadDialog.init()
    },
    successHandle (res) {
      this.$message.success({
        message: res.data,
        duration: 5000,
        dangerouslyUseHTMLString: true
      })
      this.$refs.labelUploadDialog.cancelFn()
      this.searchHandle()
    },
    // 打开批量退货出库弹出框
    openReturnWarehouseDialog (type) {
      if (this.dataListSelections.length <= 0) {
        return this.$message.error('请先选择记录')
      }
      this.returnWarehouseDialogVisible = true
      this.$nextTick(() => {
        this.$refs.comWaybillReturnWarehouseDialog.visible = true
        this.$refs.comWaybillReturnWarehouseDialog.init(this.dataListSelections.map(item => {
          return { 'waybillNo': item.waybillNo, 'deliveryNo': item.deliveryNo }
        }), type)
      })
    },
    // 打开修改入库备注弹出框
    openChangeInRemarkDialog (row) {
      this.$nextTick(() => {
        this.$refs.comWaybillChangeInRemarkDialog.visible = true
        this.$refs.comWaybillChangeInRemarkDialog.init(row)
      })
    },
    dateTypeChangedHandle () {
      let dateType = this.dataForm.dateType
      console.log('dateTypeChangedHandle', dateType)
      localStorage.setItem(this.$store.state.user.id + '-ws-com-waybill-dateType', dateType)
      if (dateType === 'CREATE_DATE') {
        if (this.dataForm.inTimeFrom && this.dataForm.inTimeTo) {
          this.createDateArray = [this.dataForm.inTimeFrom, this.dataForm.inTimeTo]
        } else {
          this.createDateArray = [getBeforeDay(16), getNowDate()]
        }
        // this.dataForm.createDateFrom = this.dataForm.inTimeFrom
        // this.dataForm.createDateTo = this.dataForm.inTimeTo
        this.dataForm.inTimeFrom = ''
        this.dataForm.inTimeTo = ''
      } else if (dateType === 'IN_TIME') {
        if (this.dataForm.createDateFrom && this.dataForm.createDateTo) {
          this.inTimeArray = [this.dataForm.createDateFrom, this.dataForm.createDateTo]
        } else {
          this.inTimeArray = [getBeforeDay(16), getNowDate()]
        }
        // this.dataForm.inTimeFrom = this.dataForm.createDateFrom
        // this.dataForm.inTimeTo = this.dataForm.createDateTo
        this.dataForm.createDateFrom = ''
        this.dataForm.createDateTo = ''
      }
    },
    inputReference (orderId) {
      this.$http.get(`/co/coorderfbareference/listByOrderId/${orderId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.referenceList = res.data
        this.inputReferenceDialogShow = true
        this.$nextTick(() => {
          this.orderId = orderId
          this.$refs.inputReferenceDialog.init(this.referenceList)
        })
      }).catch(() => {})
    },
    closeReferenceInfo () {
      this.inputReferenceDialogShow = false
    },
    inputReferenceEmit (referenceList) {
      console.log('inputReferenceEmit referenceList', referenceList)
      this.referenceList = referenceList
      this.inputReferenceDialogShow = false
      let reqData = {
        'id': this.orderId,
        'fbaReferenceList': this.referenceList
      }
      this.$http.post('/co/coorderfbareference/batchSave', reqData).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.orderId = ''
        this.referenceList = []
        this.inputReferenceDialogShow = false
        this.$message.success('操作成功')
      }).catch(() => {})
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeNativeName,
    formatterShowName,
    formatterUser
  },
  computed: {
    cancelTitle () {
      return this.cancelOperation[this.cancelType].title
    },
    batchInterceptDataRule () {
      return {
        opeareteDescription: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 2, maxRows: 6 }
      } else {
        return { minRows: 6, maxRows: 6 }
      }
    },
    getUploadFileUrl () {
      return `${this.$baseUrl}` + `${this.uploadFileUrl}`
    }
  },
  components: {
    // AddOrUpdate,
    ViewDetail,
    threeNoInput,
    WaybillInterceptDialog,
    ComWaybillMergeDialog,
    ComWaybillSplitDialog,
    ComWaybillModifyConsigneeAddressDialog,
    ComWaybillModifyOrderBaseInfoDialog,
    ComWaybillImportToForecastDialog,
    ComWaybillModifyDeclareInfoDialog,
    ComWaybillModifyDeclareInfoBatchDialog,
    ComWaybillModifyPackageInfoDialog,
    ComWaybillModifyFbaPackageInfoDialog,
    inputReferenceDialog,
    ComWaybillSortRateDialog,
    ComWaybillBatchSortRateDialog,
    ComWaybillChangeLogisticsProductDialog,
    ComWaybillChangeLogisticsChannelDialog,
    ComWaybillChangeWarehouseDialog,
    ComWaybillReturnOutDialog,
    ComWaybillChangeInboundTimeDialog,
    ComWaybillChangeInRemarkDialog,
    LabelDownload,
    LabelUploadDialog,
    tableSet,
    ExportDetail,
    ProgressDialog,
    ComWaybillExceptionMarkDialog,
    notInScopeNo
  }
}
</script>
<style lang="scss" scoped>
/* 待用 */
::v-deep .el-badge__content--primary {
  background-color: #0084ff !important;
}
/* 合运子单小蓝点 */
.circle{
  width: 4px;
  height: 4px;
  background-color: $--color-primary;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 50%;
  unicode-bidi: isolate;
  font-variant-numeric: tabular-nums;
  text-transform: none;
  text-indent: 0px;
  text-align: start;
  text-align-last: start;
}
/* 合运子单小蓝点布局样式 */
.caret-icon-style{
  position: absolute;
  color: #1890ff;
  cursor:pointer;
  font-size: small;
}
/* 合运主单蓝箭头布局样式 */
.circle-icon-style{
  position: absolute;
  color: #1890ff;
  font-size: small;
}
/* 处理 umy-ui ux-gird 第一列左侧点击滑动异常 */
::v-deep .elx-table .elx-table--body-wrapper.fixed-left--wrapper {
  overflow: hidden;
}
::v-deep .elx-grid .el-link {
  padding:0 4px !important;
}
.label {
  color: #555;
  margin-right: 0px;
}
.text {
  margin-right: 5px;
  font-weight: bold;
}
/* 使用 :deep() 穿透 Scoped CSS */
.custom-select :deep(.el-input__inner) {
  background-color: #fff;
  color: #303133;
  border-color: orangered;
}
</style>
