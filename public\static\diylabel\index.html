<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <title>基础 » DIY样式 » 添加尺寸样式</title>
    <meta name="Author" content="Team">
    <meta name="Copyright" content="">
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/bootstrap-rewrite.css" rel="stylesheet">
    <link href="css/jquery-ui-1.10.0.custom.css" rel="stylesheet">
    <link href="css/jquery.gritter.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <link href="css/print.css?v=V201709" rel="stylesheet">
    <script src="js/jquery.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery-ui-1.10.0.custom.min.js"></script>
    <script src="js/jquery.gritter.min.js"></script>
    <script type="text/javascript" src="js/validate/validate.wrapper.js"></script>
    <script src="./js/validate//jquery.metadata.js"></script>
    <script src="./js/validate//jquery.validate.js"></script>
    <script src="./js/validate//jquery.validate.methods.js"></script>
    <script src="./js/validate//jquery.validate.cn.js"></script>
    <script type="text/javascript" src="js/select2/select2.wrapper.js"></script>
    <link href="./js/select2//select2.css" rel="stylesheet" type="text/css">
    <script src="./js/select2//select2.js"></script>
    <script src="./js/select2//select2_locale_zh-CN.js"></script>
  <script type="text/javascript" src="js/js.cookie.min.js?v=V201709"></script>
    <script type="text/javascript" src="js/common.js?v=V201709"></script>
  <script>
    // 获取
    var getParentVule = window.parent.window.toChildValue();
    window['apiURL'] = getParentVule
  </script>
</head>
<body class="indexBody">
<div class="bar">
    基础 » DIY样式 » 添加尺寸样式 </div>
<div id="validateErrorContainer" class="validateErrorContainer">
    <div class="validateErrorTitle">以下信息填写有误,请重新填写</div>
    <ul></ul>
</div>
<div class="body">
    <form id="validateForm" action="bd_size!save.action" enctype="multipart/form-data" method="post">
        <!-- header -->
        <header class="custom-header">
            <div class="panel-body">
                <div class="form-horizontal form-bordered">

                    <div class="form-group m0">
                        <div class="col-sm-7">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="input-group input-group">
                                        <span class="loadtext text-muted">样式尺寸 : </span>
                                        <select name="size" id="size" class="select2 form-size">
                                            <option value="">--请选择--</option>
                                            <option value="10" selected>
                                                100*100
                                            </option>
                                            <option value="11">
                                                100*150
                                            </option>
                                            <option value="14">
                                                100*200
                                            </option>
                                            <option value="12">
                                                A4(10*10六分版)
                                            </option>
                                          <option value="13">
                                                A4(210*297)
                                            </option>
                                        </select>
                                        <span id="length" style="background: red;"></span>
                                        <input type="hidden" id="diypdfId" name="diypdfId" value="">
                                        <input type="hidden" id="id" name="id" value="">
                                        <input type="hidden" id="styleHtml" name="pdfHtml" value="">
                                    </div>
                                </div>
                            </div>
                            <div id="font-download" style="font-family: 'Microsoft YaHei';display: none">
                                <span class="loadtext text-muted">本机无微软雅黑字体，请联系客服下载安装</span>
                                <span>
<!--<a href="/xms/download/help/fonts.zip">点此下载</a>-->
</span>
                            </div>
                            <div id="code-download" style="font-family: 'Code 128';display: none">
                                <span class="loadtext text-muted">本机无code128字体，请下载安装</span>
                                <span>
<a href="/static/diylabel/download/code128.zip">点此下载</a>
</span>
                            </div>
                        </div>
                        <div class="col-sm-5 tar">
                            <button type="button" class="lo-btn lo-btn-primary lo-btn-m btn-save" id="save-data">
                                <i class="glyphicon glyphicon-floppy-save"></i> 保存模板
                            </button>
                            <div class="btn-group">
                                <button data-toggle="dropdown" class="lo-btn lo-btn-default dropdown-toggle lo-btn-m" type="button">
                                    <i class="glyphicon glyphicon-eye-open"></i><span class="text pr5"> 效果预览</span><span class="caret"></span>
                                </button>
                                <ul role="menu" class="dropdown-menu dropdown-menu-left">
                                    <li><a href="javascript:void(0);" class="btn-view"><i class="glyphicon glyphicon-camera mr5"></i>预览视图</a></li>
                                    <!--<li><a href="javascript:void(0);" class="btn-print-view"><i class="glyphicon glyphicon-print mr5"></i>打印预览</a></li>-->
                                </ul>
                            </div>
                            <button type="button" class="lo-btn lo-btn-danger lo-btn-m btn-reset">
                                <i class="glyphicon glyphicon-trash"></i> 清空画布
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </header></form>
    <!-- label type -->
    <aside class="label-type">
        <div class="panel-group" id="type-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#addressinfo" data-parent="#type-group" data-toggle="collapse" aria-expanded="false"> <span class="glyphicon glyphicon-menu-down"></span>地址信息</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="addressinfo" aria-expanded="false">
                    <div class="panel-body">
                        <div class="dragitem character ui-draggable" data-type="address" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-list-alt" aria-hidden="true"></i>发件人地址(格式1) </strong>
                            <span class="detail">
<span class="name"><hls id="shipperShipperName" class="shipperShipperName" name="labelStyle.shipperShipperName">Mr Zhang</hls><br></span>
<span class="street"><hls id="shipperShipperAddress" class="shipperShipperAddress" name="labelStyle.shipperShipperAddress">Qibao town Ming road no. 555 on the 8th floor 2 floor</hls></span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="address" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-list-alt" aria-hidden="true"></i>发件人地址(格式2) </strong>
                            <span class="detail">
<span class="name"><hls id="shipperShipperName" class="shipperShipperName" name="labelStyle.shipperShipperName">Mr Zhang</hls><br></span>
<span class="street"><hls id="shipperShipperAddress" class="shipperShipperAddress" name="labelStyle.shipperShipperAddress">Qibao town Ming road no. 555 on the 8th floor 2 floor</hls><br></span>
<span>
<span class="tel1"><hls id="shipperShipperPhone" class="shipperShipperPhone" name="labelStyle.shipperShipperPhone">021-88888888</hls></span>,
</span>
<span class="postcode"><hls id="shipperShipperPostcode" class="shipperShipperPostcode" name="labelStyle.shipperShipperPostcode">201101</hls></span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="address" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-list-alt" aria-hidden="true"></i>发件人地址(格式3) </strong>
                            <span class="detail">
<span class="name"><hls id="shipperShipperName" class="shipperShipperName" name="labelStyle.shipperShipperName">Mr Zhang</hls><br></span>
<span class="street"><hls id="shipperShipperAddress" class="shipperShipperAddress" name="labelStyle.shipperShipperAddress">Qibao town Ming road no. 555 on the 8th floor 2 floor</hls><br></span>
<span class="area">
<hls id="shipperShipperCity" class="shipperShipperCity" name="labelStyle.shipperShipperCity">Shenzhen</hls>
<hls id="shipperShipperProvince" class="shipperShipperProvince" name="labelStyle.shipperShipperProvince">GuangDong</hls><br>
</span>
<span class="country"><hls id="shipperShipperCountry" class="shipperShipperCountry" name="labelStyle.shipperShipperCountry">CN</hls><br></span>
<span class="country_en"><hls id="shipperShipperCountryCn" class="shipperShipperCountryCn" name="labelStyle.shipperShipperCountryCn">China</hls><br></span>
<span class="country_cn" style="display: none;">(<hls id="shipperShipperCountryCn" class="shipperShipperCountryCn" name="labelStyle.shipperShipperCountryCn">中国</hls>)<br></span>
<span class="postcode"><hls id="shipperShipperPostcode" class="shipperShipperPostcode" name="labelStyle.shipperShipperPostcode">201101</hls><br></span>
<span class="tel1"><hls id="shipperShipperPhone" class="shipperShipperPhone" name="labelStyle.shipperShipperPhone">021-88888888</hls><br></span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>退件单位 </strong>
                            <span class="detail"><hls id="shipperShipperName" class="shipperShipperName" name="labelStyle.shipperShipperName">深圳市互联通科技有限公司</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人公司名称 </strong>
                            <span class="detail"><hls id="shipperShipperCompany" class="shipperShipperCompany" name="labelStyle.shipperShipperCompany">INTERCONNECTEASY</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人姓名 </strong>
                            <span class="detail"><hls id="shipperShipperName" class="shipperShipperName" name="labelStyle.shipperShipperName">Mr Zhang</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人街道 </strong>
                            <span class="detail"><hls id="shipperShipperAddress" class="shipperShipperAddress" name="labelStyle.shipperShipperAddress">Qibao town Ming road no. 555 on the 8th floor 2 floor</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人国家 </strong>
                            <span class="detail"><hls id="shipperShipperCountryCn" class="shipperShipperCountryCn" name="labelStyle.shipperShipperCountryCn">China</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人国家英文缩写 </strong>
                            <span class="detail"><hls id="shipperShipperCountry" class="shipperShipperCountry" name="labelStyle.shipperShipperCountry">CN</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人国家中文 </strong>
                            <span class="detail"><hls id="shipperShipperCountryCn" class="shipperShipperCountryCn" name="labelStyle.shipperShipperCountryCn">中国</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人省份 </strong>
                            <span class="detail"><hls id="shipperShipperProvince" class="shipperShipperProvince" name="labelStyle.shipperShipperProvince">GuangDong</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人城市 </strong>
                            <span class="detail"><hls id="shipperShipperCity" class="shipperShipperCity" name="labelStyle.shipperShipperCity">Shenzhen</hls></span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人区 </strong>
                        <span class="detail"><hls id="shipperShipperDistrict" class="shipperShipperDistrict" name="labelStyle.shipperShipperDistrict">NanShan</hls></span>
                      </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人邮编 </strong>
                            <span class="detail"><hls id="shipperShipperPostcode" class="shipperShipperPostcode" name="labelStyle.shipperShipperPostcode">201101</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件人电话 </strong>
                            <span class="detail"><hls id="shipperShipperPhone" class="shipperShipperPhone" name="labelStyle.shipperShipperPhone">021-88888888</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="address" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-list-alt" aria-hidden="true"></i>收件人地址 </strong>
                            <span class="detail">
<span class="name"><hls id="consigneeConsigneeName" class="consigneeConsigneeName" name="labelStyle.consigneeConsigneeName">Andrew Peachey</hls><br></span>
<span class="street"><hls id="consigneeConsigneeAddress" class="consigneeConsigneeAddress" name="labelStyle.consigneeConsigneeAddress">12 Great Gill,Walmer Bridge</hls><br></span>
<span class="area">
<hls id="consigneeConsigneeCity" class="consigneeConsigneeCity" name="labelStyle.consigneeConsigneeCity">LOS ANGELES</hls>
<hls id="consigneeConsigneeProvince" class="consigneeConsigneeProvince" name="labelStyle.consigneeConsigneeProvince">CA</hls><br>
</span>
<span class="postcode"><hls id="consigneeConsigneePostcode" class="consigneeConsigneePostcode" name="labelStyle.consigneeConsigneePostcode">90038</hls><br></span>
<span class="tel1"><hls id="consigneeConsigneePhone" class="consigneeConsigneePhone" name="labelStyle.consigneeConsigneePhone">************</hls><br></span>
<span>
<span class="country">
<hls id="consigneeConsigneeCountry" class="consigneeConsigneeCountry" name="labelStyle.consigneeConsigneeCountry">US</hls>
<span class="country_en"><hls id="consigneeConsigneeCountryEn" class="consigneeConsigneeCountryEn" name="labelStyle.consigneeConsigneeCountryEn">UNITED STATES</hls></span>
<span class="country_cn" style="display: none;">(<hls id="consigneeConsigneeCountryCn" class="consigneeConsigneeCountryCn" name="labelStyle.consigneeConsigneeCountryCn">美国</hls>)</span><br>
</span>
</span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="address" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-list-alt" aria-hidden="true"></i>收件人地址（格式2） </strong>
                            <span class="detail">
<span class="name"><hls id="consigneeConsigneeName" class="consigneeConsigneeName" name="labelStyle.consigneeConsigneeName">Andrew Peachey</hls><br></span>
<span class="street"><hls id="consigneeConsigneeAddress" class="consigneeConsigneeAddress" name="labelStyle.consigneeConsigneeAddress">12 Great Gill,Walmer Bridge</hls><br></span>
<span class="area">
  <hls id="consigneeConsigneeCity" class="consigneeConsigneeCity" name="labelStyle.consigneeConsigneeCity">LOS ANGELES</hls>
  <hls id="consigneeConsigneeProvince" class="consigneeConsigneeProvince" name="labelStyle.consigneeConsigneeProvince">CA</hls><br>
</span>
<span class="country">
<hls id="consigneeConsigneeCountryEn" class="consigneeConsigneeCountryEn" name="labelStyle.consigneeConsigneeCountryEn">UNITED STATES</hls>
<span class="country_cn" style="display: none;">(<hls id="consigneeConsigneeCountryCn" class="consigneeConsigneeCountryCn" name="labelStyle.consigneeConsigneeCountryCn">美国</hls>)</span><br>
</span>
<span class="postcode"><hls id="consigneeConsigneePostcode" class="consigneeConsigneePostcode" name="labelStyle.consigneeConsigneePostcode">90038</hls><br></span>
<span class="tel1"><hls id="consigneeConsigneePhone" class="consigneeConsigneePhone" name="labelStyle.consigneeConsigneePhone">************</hls></span>
</span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="address" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-list-alt" aria-hidden="true"></i>FBA收件人地址</strong>
                        <span class="detail">
<span class="fbaWarehouseCode"><hls id="consigneeFbaWarehouseCode" class="consigneeFbaWarehouseCode" name="labelStyle.consigneeFbaWarehouseCode">LAS1</hls><br></span>
<span class="name"><hls id="consigneeConsigneeName" class="consigneeConsigneeName" name="labelStyle.consigneeConsigneeName">Andrew Peachey</hls><br></span>
<span class="street"><hls id="consigneeConsigneeAddress" class="consigneeConsigneeAddress" name="labelStyle.consigneeConsigneeAddress">12 Great Gill,Walmer Bridge</hls><br></span>
<span class="area">
<hls id="consigneeConsigneeCity" class="consigneeConsigneeCity" name="labelStyle.consigneeConsigneeCity">LOS ANGELES</hls>
<hls id="consigneeConsigneeProvince" class="consigneeConsigneeProvince" name="labelStyle.consigneeConsigneeProvince">CA</hls><br>
</span>
<span class="postcode"><hls id="consigneeConsigneePostcode" class="consigneeConsigneePostcode" name="labelStyle.consigneeConsigneePostcode">90038</hls><br></span>
<span class="tel1"><hls id="consigneeConsigneePhone" class="consigneeConsigneePhone" name="labelStyle.consigneeConsigneePhone">************</hls><br></span>
<span>
<span class="country">
<hls id="consigneeConsigneeCountry" class="consigneeConsigneeCountry" name="labelStyle.consigneeConsigneeCountry">US</hls>
<span class="country_en"><hls id="consigneeConsigneeCountryEn" class="consigneeConsigneeCountryEn" name="labelStyle.consigneeConsigneeCountryEn">UNITED STATES</hls></span>
<span class="country_cn" style="display: none;">(<hls id="consigneeConsigneeCountryCn" class="consigneeConsigneeCountryCn" name="labelStyle.consigneeConsigneeCountryCn">美国</hls>)</span><br>
</span>
</span>
</span>
                      </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人姓名 </strong>
                            <span class="detail"><hls id="consigneeConsigneeName" class="consigneeConsigneeName" name="labelStyle.consigneeConsigneeName">Andrew Peachey</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人街道 </strong>
                            <span class="detail"><hls id="consigneeConsigneeAddress" class="consigneeConsigneeAddress" name="labelStyle.consigneeConsigneeAddress">12 Great Gill,Walmer Bridge</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人国家(中) </strong>
                            <span class="detail"><hls id="consigneeConsigneeCountryCn" class="consigneeConsigneeCountryCn" name="labelStyle.consigneeConsigneeCountryCn">美国</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人国家(英) </strong>
                            <span class="detail"><hls id="consigneeConsigneeCountryEn" class="consigneeConsigneeCountryEn" name="labelStyle.consigneeConsigneeCountryEn">UNITED STATES</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人国家英文简称 </strong>
                            <span class="detail"><hls id="consigneeConsigneeCountry" class="consigneeConsigneeCountry" name="labelStyle.consigneeConsigneeCountry">US
</hls></span></div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人国家分区 </strong>
                            <span class="detail"><hls id="channelArea" class="channelArea" name="labelStyle.channelArea">1</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人省份、州 </strong>
                            <span class="detail"><hls id="consigneeConsigneeProvince" class="consigneeConsigneeProvince" name="labelStyle.consigneeConsigneeProvince">CA</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人城市 </strong>
                            <span class="detail"><hls id="consigneeConsigneeCity" class="consigneeConsigneeCity" name="labelStyle.consigneeConsigneeCity">LOS ANGELES</hls></span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人区</strong>
                        <span class="detail"><hls id="consigneeConsigneeDistrict" class="consigneeConsigneeDistrict" name="labelStyle.consigneeConsigneeDistrict">Industry City</hls></span>
                      </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人邮编 </strong>
                            <span class="detail"><hls id="consigneeConsigneePostcode" class="consigneeConsigneePostcode" name="labelStyle.consigneeConsigneePostcode">90038</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="170" data-default-height="57">
                            <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>收件人邮编条码 </strong>
                            <span class="detail">
<hls id="consigneePostCodeBarcode" class="consigneePostCodeBarcode" name="labelStyle.consigneePostCodeBarcode">
<div style="font-size:72px;"><div class="default-barcode">&lt;![CDATA[Ë90038ÃÎ]]&gt;</div></div>
</hls>
<span class="codeNumber"><hls id="rpostcodeBarcode" class="consigneeConsigneePostcode barcode-text" name="labelStyle.consigneeConsigneePostcode">90038</hls></span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人电话 </strong>
                            <span class="detail"><hls id="consigneeConsigneePhone" class="consigneeConsigneePhone" name="labelStyle.consigneeConsigneePhone">************</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人证件号 </strong>
                            <span class="detail"><hls id="consigneeConsigneeIdcard" class="consigneeConsigneeIdcard" name="labelStyle.consigneeConsigneeIdcard">00.000.000/0000-00</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>收件人税号 </strong>
                            <span class="detail"><hls id="consigneeConsigneeTaxNo" class="consigneeConsigneeTaxNo" name="labelStyle.consigneeConsigneeTaxNo">00.000.000/0000-00</hls></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#orderinfo" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                            <span class="glyphicon glyphicon-menu-down"></span>订单物流信息
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="orderinfo" aria-expanded="false">
                    <div class="panel-body">
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>订单号 </strong>
                            <span class="detail"><hls id="rfo" class="coOrderParamId" name="labelStyle.coOrderParamId">1000000024</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>客户单号 </strong>
                            <span class="detail"><hls id="cno" class="coOrderParamCustomerOrderNo" name="labelStyle.coOrderParamCustomerOrderNo">20170413001</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>运单号</strong>
                            <span class="detail"><hls id="wno" class="coOrderParamWaybillNo" name="labelStyle.coOrderParamWaybillNo">RS558431379CN</hls></span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>邮政单号</strong>
                        <span class="detail"><hls id="postalTrackingNo" class="coOrderParamPostalTrackingNo" name="labelStyle.coOrderParamPostalTrackingNo">A558431379312321Q</hls></span>
                      </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>派送单号 </strong>
                            <span class="detail"><hls id="returnNo" class="coOrderParamDeliveryNo" name="labelStyle.coOrderParamDeliveryNo">910029381231223011</hls></span>
                        </div>
                        <!-- <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>派送单号 </strong>
                          <span class="detail"><hls id="masterDeliveryNo" class="coOrderParamMasterDeliveryNo" name="labelStyle.coOrderParamMasterDeliveryNo">parent00001</hls></span>
                        </div> -->
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>子派送单号 </strong>
                          <span class="detail"><hls id="subDeliveryNo" class="coOrderParamSubDeliveryNo" name="labelStyle.coOrderParamSubDeliveryNo">sub00001</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="50">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>产品/渠道名称 </strong>
                            <span class="detail"><hls id="channelName" class="coOrderParamLogisticsProductName" name="labelStyle.coOrderParamLogisticsProductName">香港小包挂号</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="50">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>物流渠道名称 </strong>
                          <span class="detail"><hls id="channelName" class="coOrderParamLogisticsChannelName" name="labelStyle.coOrderParamLogisticsChannelName">香港小包渠道</hls></span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="50">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>物流渠道代码 </strong>
                        <span class="detail"><hls id="channelName" class="coOrderParamLogisticsChannelCode" name="labelStyle.coOrderParamLogisticsChannelCode">HK packet code</hls></span>
                      </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="50">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>挂号标识 </strong>
                            <span class="detail"><hls id="rflag" class="rflag" name="labelStyle.rflag">R</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="50">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>页码 </strong>
                            <span class="detail"><hls id="pageCode" class="pageCode" name="labelStyle.pageCode">1/1</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>COD金额 </strong>
                          <span class="detail"><hls id="codSum" class="coOrderParamCodAmountD" name="labelStyle.coOrderParamCodAmountD">21.21</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>COD币种 </strong>
                          <span class="detail"><hls id="codCurrency" class="coOrderParamCodCurrency" name="labelStyle.coOrderParamCodCurrency">USD</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>申报总值 </strong>
                            <span class="detail"><hls id="productSumD" class="productSumD" name="labelStyle.productSumD">5.6</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>实际重量(KG) </strong>
                            <span class="detail"><hls id="netWeight1" class="netWeight1" name="labelStyle.netWeight1">0.14</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>预估重量(G) </strong>
                            <span class="detail"><hls id="pweight" class="pweight" name="labelStyle.pweight">120</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>预估重量(KG) </strong>
                            <span class="detail"><hls id="pweight1" class="pweight1" name="labelStyle.pweight1">0.12</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>预报重量(KG) </strong>
                            <span class="detail"><hls id="coOrderParamForecastWeightD" class="coOrderParamForecastWeightD" name="labelStyle.coOrderParamForecastWeightD">0.12</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>材重(G) </strong>
                            <span class="detail"><hls id="vweight" class="vweight" name="labelStyle.vweight">100</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>材重(KG) </strong>
                            <span class="detail"><hls id="vweight1" class="vweight1" name="labelStyle.vweight1">0.1</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹件数 </strong>
                          <span class="detail"><hls id="coOrderParamPackageQty" class="coOrderParamPackageQty" name="labelStyle.coOrderParamPackageQty">1</hls></span>
                        </div>

                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>FBA仓库代码 </strong>
                          <span class="detail"><hls id="coOrderParamFbaWarehouseCode" class="coOrderParamFbaWarehouseCode" name="labelStyle.coOrderParamFbaWarehouseCode">1</hls></span>
                        </div>

                        <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="170" data-default-height="57">
                            <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>订单号条码 </strong>
                            <span class="detail">
<hls id="rfoBarcode" class="rfoBarcode" name="labelStyle.rfoBarcode">
<div style="font-size:72px;"><div class="default-barcode">1000000024</div></div>
</hls>
<span class="codeNumber"><hls id="rfo" class="coOrderParamId barcode-text" name="labelStyle.coOrderParamId">1000000024</hls></span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                            <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>客户单号条码 </strong>
                            <span class="detail">
<hls id="cnoBarcode" class="cnoBarcode coOrderParamCustomerOrderNoBarcode">
<div style="font-size:72px;"><div class="default-barcode">20170413001</div></div>
</hls>
<span class="codeNumber"><hls id="cnoB" class="cnoB coOrderParamCustomerOrderNo barcode-text" name="labelStyle.coOrderParamCustomerOrderNo">20170413001</hls></span>
</span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                            <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>运单号条码 </strong>
                            <span class="detail">
<hls id="wnoBarcode" class="wnoBarcode coOrderParamWaybillNoBarcode">
<div style="font-size:72px;"><div class="default-barcode">&lt;![CDATA[ËRSÇWt?oÉCN,Î]]&gt;</div></div>
</hls>
<span class="codeNumber"><hls id="wnoB" class="wnoB coOrderParamWaybillNo barcode-text" name="labelStyle.coOrderParamWaybillNo">RS558431379CN</hls></span>
</span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                        <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>邮政单号条码</strong>
                        <span class="detail">
<hls id="postalTrackingNoBarcode" class="postalTrackingNoBarcode coOrderParamPostalTrackingNoBarcode">
<div style="font-size:72px;"><div class="default-barcode">&lt;![CDATA[ËRSÇWt?oÉCN,Î]]&gt;</div></div>
</hls>
<span class="codeNumber"><hls id="ptoB" class="ptoB coOrderParamPostalTrackingNo barcode-text" name="labelStyle.coOrderParamPostalTrackingNo">RS558431379CN</hls></span>
</span>
                      </div>
                        <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                            <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>渠道商单号条码 </strong>
                            <span class="detail">
<hls id="returnNoBarcode" class="returnNoBarcode coOrderParamDeliveryNoBarcode">
<div style="font-size:72px;"><div class="default-barcode">A558431379312321Q</div></div>
</hls>
<span class="codeNumber"><hls id="cnoB" class="returnNoB coOrderParamDeliveryNo barcode-text" name="labelStyle.coOrderParamDeliveryNo">A558431379312321Q</hls></span>
</span>
                        </div>
<div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                        <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>派送单号条码</strong>
                        <span class="detail">
                            <hls id="masterDeliveryNoBarcode" class="masterDeliveryNoBarcode coOrderParamMasterDeliveryNoBarcode">
                            <div style="font-size:72px;"><div class="default-barcode">M558431379312321Q</div></div>
                            </hls>
                            <span class="codeNumber"><hls id="mdnoB" class="masterDeliveryNoB coOrderParamMasterDeliveryNo barcode-text" name="labelStyle.coOrderParamMasterDeliveryNo">M558431379312321Q</hls></span>
                        </span>
</div>

<div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
  <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>子派送单号条码 </strong>
  <span class="detail">
        <hls id="subDeliveryNoBarcode" class="subDeliveryNoBarcode coOrderParamSubDeliveryNoBarcode">
        <div style="font-size:72px;"><div class="default-barcode">A558431379312321Q</div></div>
        </hls>
        <span class="codeNumber"><hls id="sdnoB" class="subDeliveryNoB coOrderParamSubDeliveryNo barcode-text" name="labelStyle.coOrderParamSubDeliveryNo">A558431379312321Q</hls></span>
  </span>
</div>
<!--客户单号二维码 begin   -->
<div class="dragitem image ui-draggable" data-type="image" data-default-width="57" data-default-height="57">
  <strong class="title" id="title"><i class="glyphicon glyphicon-qrcode" aria-hidden="true"></i>客户单号二维码</strong>
  <span class="detail">
    <hls id="cnoQrcode" class="cnoQrcode coOrderParamCustomerOrderNoQrcode">
    <div class="default-qrcode">
    <img src="images/preset/qrCode.jpg"/>
    </div>
    </hls>
  </span>
 </div>
<!--客户单号二维码 end     -->
<!--运单号二维码 begin   -->
<div class="dragitem image ui-draggable" data-type="image" data-default-width="57" data-default-height="57">
    <strong class="title" id="title"><i class="glyphicon glyphicon-qrcode" aria-hidden="true"></i>运单号二维码</strong>
    <span class="detail">
      <hls id="wnoQrcode" class="wnoQrcode coOrderParamWaybillNoQrcode">
      <div class="default-qrcode">
      <img src="images/preset/qrCode.jpg"/>
      </div>
      </hls>
  </span>
</div>
<!--运单号二维码 end     -->
<!--派送单号二维码 begin   -->
<div class="dragitem image ui-draggable" data-type="image" data-default-width="57" data-default-height="57">
    <strong class="title" id="title"><i class="glyphicon glyphicon-qrcode" aria-hidden="true"></i>派送单号二维码</strong>
    <span class="detail">
      <hls id="masterDeliveryNoQrcode" class="masterDeliveryNoQrcode coOrderParamMasterDeliveryNoQrcode">
        <div class="default-qrcode">
          <img src="images/preset/qrCode.jpg"/>
        </div>
      </hls>
      <span></span>
    </span>
</div>
<!--派送单号二维码 end     -->
                    <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>客户编码 </strong>
                            <span class="detail"><hls id="ccode" class="ccode" name="labelStyle.ccode">zhangsan</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>客户名称 </strong>
                            <span class="detail"><hls id="cname" class="cname" name="labelStyle.cname">张三</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                          <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>客户备注 </strong>
                          <span class="detail"><hls id="coOrderParamCustomerRemark" class="coOrderParamCustomerRemark" name="labelStyle.coOrderParamCustomerRemark">客户备注内容</hls></span>
                      </div>
                  </div>
                </div>
            </div>

            <!-- 包裹信息 -->
            <div class="panel panel-default">
              <div class="panel-heading">
                  <h4 class="panel-title">
                      <a class="collapsed lo-a" href="#packageinfo" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                          <span class="glyphicon glyphicon-menu-down"></span>包裹信息
                      </a>
                  </h4>
              </div>
              <div class="panel-collapse collapse" id="packageinfo" aria-expanded="false">
                <div class="panel-body">
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹序号 </strong>
                    <span class="detail"><hls id="packageSerialNo" class="coOrderParamPackageSerialNo" name="labelStyle.coOrderParamPackageSerialNo">1</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹号/箱号 </strong>
                    <span class="detail"><hls id="subPackageNo" class="coOrderParamSubPackageNo" name="labelStyle.coOrderParamSubPackageNo">C001U01</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                    <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>包裹号/箱号条码 </strong>
                    <span class="detail">
                      <hls id="subPackageNoBarcode" class="subPackageNoBarcode coOrderParamSubPackageNoBarcode">
                      <div style="font-size:72px;"><div class="default-barcode">C0001U01</div></div>
                      </hls>
                      <span class="codeNumber"><hls id="spnoB" class="subPackageNoB coOrderParamSubPackageNo barcode-text" name="labelStyle.coOrderParamSubPackageNo">C0001U01</hls></span>
                    </span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="230" data-default-height="57">
                    <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>包裹号/箱号条码(含前缀) </strong>
                    <span class="detail">
                      <hls id="subPackageNoWithPrefixBarcode" class="subPackageNoWithPrefix coOrderParamSubPackageNoWithPrefixBarcode">
                      <div style="font-size:72px;"><div class="default-barcode">C0001U01</div></div>
                      </hls>
                      <span class="codeNumber"><hls id="subPackageNoWithPrefixB" class="subPackageNoWithPrefixB coOrderParamSubPackageNoWithPrefix barcode-text" name="labelStyle.coOrderParamSubPackageNoWithPrefix">JL-C0001U01</hls></span>
                    </span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹重量 </strong>
                    <span class="detail"><hls id="coOrderParamPackageWeightD" class="coOrderParamPackageWeightD" name="labelStyle.coOrderParamPackageWeightD">20.0</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹长 </strong>
                    <span class="detail"><hls id="coOrderParamPackageLengthD" class="coOrderParamPackageLengthD" name="labelStyle.coOrderParamPackageLengthD">20.0</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹宽 </strong>
                    <span class="detail"><hls id="coOrderParamPackageWidthD" class="coOrderParamPackageWidthD" name="labelStyle.coOrderParamPackageWidthD">10.0</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹高 </strong>
                    <span class="detail"><hls id="coOrderParamPackageHeightD" class="coOrderParamPackageHeightD" name="labelStyle.coOrderParamPackageHeightD">5.0</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-英文品名 </strong>
                    <span class="detail"><hls id="orderDeclareEnglishName" class="orderDeclareEnglishName" name="labelStyle.orderDeclareEnglishName">Huawei Mate 60</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-中文品名 </strong>
                    <span class="detail"><hls id="orderDeclareChineseName" class="orderDeclareChineseName" name="labelStyle.orderDeclareChineseName">华为Mate60手机</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-数量 </strong>
                    <span class="detail"><hls id="orderDeclareQuantity" class="orderDeclareQuantity" name="labelStyle.orderDeclareQuantity">2</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-净重 </strong>
                    <span class="detail"><hls id="orderDeclareUnitNetWeightD" class="orderDeclareUnitNetWeightD" name="labelStyle.orderDeclareUnitNetWeightD">2.468</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-金额 </strong>
                    <span class="detail"><hls id="orderDeclareUnitDeclarePriceD" class="orderDeclareUnitDeclarePriceD" name="labelStyle.orderDeclareUnitDeclarePriceD">6.99</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-海关编码 </strong>
                    <span class="detail"><hls id="orderDeclareHsCode" class="orderDeclareHsCode" name="labelStyle.orderDeclareHsCode">80001122</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-SKU </strong>
                    <span class="detail"><hls id="orderDeclareSku" class="orderDeclareSku" name="labelStyle.orderDeclareSku">SKU0012345</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-SKU*数量</strong>
                    <span class="detail"><hls id="orderDeclareSkuAndQty" class="orderDeclareSkuAndQty" name="labelStyle.orderDeclareSkuAndQty">SKU0012345 * 3</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-产品型号 </strong>
                    <span class="detail"><hls id="orderDeclareProductModel" class="orderDeclareProductModel" name="labelStyle.orderDeclareProductModel">7英寸</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-材质 </strong>
                    <span class="detail"><hls id="orderDeclareMaterial" class="orderDeclareMaterial" name="labelStyle.orderDeclareMaterial">昆仑玻璃</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-用途 </strong>
                    <span class="detail"><hls id="orderDeclarePurpose" class="orderDeclarePurpose" name="labelStyle.orderDeclarePurpose">通讯</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-原产地 </strong>
                    <span class="detail"><hls id="orderDeclareOrigin" class="orderDeclareOrigin" name="labelStyle.orderDeclareOrigin">CN</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-配货备注 </strong>
                    <span class="detail"><hls id="orderDeclarePickingRemark" class="orderDeclarePickingRemark" name="labelStyle.orderDeclarePickingRemark">Picking Remark</hls></span>
                  </div>
                  <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                    <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹内商品-商品URL </strong>
                    <span class="detail"><hls id="orderDeclareProductUrl" class="orderDeclareProductUrl" name="labelStyle.orderDeclareProductUrl">Picking Remark</hls></span>
                  </div>
                </div>
              </div>
          </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#postcodeArea" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                            <span class="glyphicon glyphicon-menu-down"></span>分拣码
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="postcodeArea" aria-expanded="false">
                    <div class="panel-body">
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>ITS物流产品分拣码</strong>
                            <span class="detail"><hls id="area1" class="sortCode" name="labelStyle.sortCode">21</hls></span>
                        </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>供应商分拣编码</strong>
                        <span class="detail"><hls id="area2" class="providerSortingCode" name="labelStyle.providerSortingCode">ZH0092-12</hls></span>
                      </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>供应商分拣编号</strong>
                        <span class="detail"><hls id="area3" class="providerSortingNo" name="labelStyle.providerSortingNo">321</hls></span>
                      </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#curDate" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                            <span class="glyphicon glyphicon-menu-down"></span>当前日期
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="curDate" aria-expanded="false">
                    <div class="panel-body">
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式1 yyyy-MM-dd">日期格式1 yyyy-MM-dd</span></strong>
                            <span class="detail"><hls id="curDate1" class="curDate1" dateFormat="yyyy-MM-dd" name="labelStyle.curDate.date1">2020-02-19</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式2 dd-MM-yyyy">日期格式2 dd-MM-yyyy</span></strong>
                            <span class="detail"><hls id="curDate2" class="curDate2" dateFormat="dd-MM-yyyy" name="labelStyle.curDate.date2">19-02-2020</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式3 yyyy/MM/dd">日期格式3 yyyy/MM/dd</span></strong>
                            <span class="detail"><hls id="curDate3" class="curDate3" dateFormat="yyyy/MM/dd" name="labelStyle.curDate.date3">2020/02/19</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式4 dd/MM/yyyy">日期格式4 dd/MM/yyyy</span></strong>
                            <span class="detail"><hls id="curDate4" class="curDate4" dateFormat="dd/MM/yyyy" name="labelStyle.curDate.date4">19/02/2020</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式5 yyyy.MM.dd">日期格式5 yyyy.MM.dd</span></strong>
                            <span class="detail"><hls id="curDate5" class="curDate5" dateFormat="yyyy.MM.dd" name="labelStyle.curDate.date5">2020.02.19</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式6 dd.MM.yyyy">日期格式6 dd.MM.yyyy</span></strong>
                            <span class="detail"><hls id="curDate6" class="curDate6" dateFormat="" name="labelStyle.curDate.date6">19.02.2020</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式7 yyyyMMdd">日期格式7 yyyyMMdd</span></strong>
                            <span class="detail"><hls id="curDate7" class="curDate7" dateFormat="yyyyMMdd" name="labelStyle.curDate.date7">20200219</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式8 ddMMyyyy">日期格式8 ddMMyyyy</span></strong>
                            <span class="detail"><hls id="curDate8" class="curDate8" dateFormat="ddMMyyyy" name="labelStyle.curDate.date8">19022020</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式9 yyyy-MM-dd HH:mm:ss">日期格式9 yyyy-MM-dd HH:mm:ss</span></strong>
                            <span class="detail"><hls id="curDate9" class="curDate9" dateFormat="yyyy-MM-dd HH:mm:ss" name="labelStyle.curDate.date9">2020-02-19 12:34:56</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式10 dd-MM-yyyy HH:mm:ss">日期格式10 dd-MM-yyyy HH:mm:ss</span></strong>
                            <span class="detail"><hls id="curDate10" class="curDate10" dateFormat="dd-MM-yyyy HH:mm:ss" name="labelStyle.curDate.date10">19-02-2020 12:34:56</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式11 yyyy/MM/dd HH:mm:ss">日期格式11 yyyy/MM/dd HH:mm:ss</span></strong>
                            <span class="detail"><hls id="curDate11" class="curDate11" dateFormat="yyyy/MM/dd HH:mm:ss" name="labelStyle.curDate.date11">2020/02/19 12:34:56</hls></span>
                        </div>
                        <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i><span title="日期格式12 MM/dd/yyyy HH:mm:ss">日期格式12 MM/dd/yyyy HH:mm:ss</span></strong>
                            <span class="detail"><hls id="curDate12" class="curDate12" dateFormat="MM/dd/yyyy HH:mm:ss" name="labelStyle.curDate.date12">02/19/2020 12:34:56</hls></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#pickinginfo" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                            <span class="glyphicon glyphicon-menu-down"></span>商品配货报关信息
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="pickinginfo" aria-expanded="false">
                    <div class="panel-body">
                        <div class="dragitem character declarelist ui-draggable" data-type="declarelist">
                            <strong class="title"><i class="ico-list-alt mr5"></i>报关明细</strong>
                            <span class="glyphicon glyphicon-ok form-control-feedback text-success"></span>
                            <div class="detail">
                                <table id="declareList" class="skulist-table declare declareList coOrderParamOrderDeclareList">
                                    <thead>
                                    <tr>
                                        <th class="no_declare no_head no" width="15" height="15">
                                            <span height="15">序号<br>No.</span>
                                        </th>
                                        <th class="name_declare name_header name name_header name" >
                                            <span>内件详细名称和数量<br>Quantity and detailed description of contents </span>
                                        </th>
                                        <th class="customsNo_declare customsNo" width="65" height="15">
                                            <span height="15">海关编码<br>Hs Tariff</span>
                                        </th>
                                        <th class="qty_declare qty" width="30" height="15">
                                            <span height="15">数量<br>Qty</span>
                                        </th>
                                        <th class="weight_declare  weight weight_header" width="45" height="15">
                                            <span class="weight-unit" height="15">重量<br>Weight</span>
                                        </th>
                                        <th class="price_declare price" width="40" height="15">
                                            <span class="currency-header" height="15">价值<br>Value</span>
                                        </th>
                                        <th class="origin_declare origin_head origin" width="50" height="15">
                                            <span height="15">原产地<br>Goods Origin</span>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr id="declare_item_0" height="15">
                                        <td class="no_declare no_body no" height="15">
<span height="15">
<hls id="name_declare_no_0" name="labelStyle.itemList[0].no" class="index_no">1</hls>
</span>
                                        </td>
                                        <td class="name_declare name_body name">
<span height="15">
<hls id="name_declare_name_0" name="labelStyle.itemList[0].name" class="name orderDeclareListEnglishName">Camera</hls>
<hls id="name_declare_productName_0" name="labelStyle.itemList[0].productName" class="name_cn orderDeclareListChineseName">/摄像机</hls>
<hls id="name_declare_number_0" name="labelStyle.itemList[0].qty" class="number orderDeclareListQuantity"> * 1</hls>
</span>
                                        </td>
                                        <td class="customsNo_declare customsNo" height="15">
<span height="15">
<hls id="name_declare_customsNo_0" name="labelStyle.itemList[0].customsNo" class="orderDeclareListHsCode">1000025485</hls>
</span>
                                        </td>
                                        <td class="qty_declare qty" height="15">
<span height="15">
<hls id="name_declare_qty_0" name="labelStyle.itemList[0].qty" class="orderDeclareListQuantity">1</hls>
</span>
                                        </td>
                                        <td class="weight_declare weight weight_body" height="15">
<span class="unit-kg" height="15">
<hls id="name_declare_weight1_0" name="labelStyle.itemList[0].netWeight1" class="orderDeclareListUnitNetWeightD">0.12</hls>
</span>
                                            <span class="unit-kg-body disn" height="15">KG</span>
                                        </td>
                                        <td class="price_declare price" height="15">
                                            <span id="currency_before_0" class="currency-before disn" height="15">USD</span>
                                            <span height="15">
<hls id="name_declare_price_0" name="labelStyle.itemList[0].price" class="orderDeclareListUnitDeclarePriceD">15</hls>
</span>
                                            <span id="currency_after_0" class="currency-after disn" height="15">USD</span>
                                        </td>
                                        <td class="origin_declare origin_body origin" height="15">
<span height="15">
<hls id="name_declare_origin_0" name="labelStyle.itemList[0].origin" class="orderDeclareListOrigin">CN</hls>
</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                    <tfoot>
                                    <tr class="tfootHeader">
                                        <th class="no_declare none no" height="25">
                                            <span height="25"></span>
                                        </th>
                                        <th class="origin_total" height="25">
<span height="25">
协调系统税则号列和货物原产国(只对商品邮件填写)<br>
HS tatiff number and country of origin of goods (For commerical items only)
</span>
                                        </th>
                                        <th class="none customsNo_declare customsNo" height="25">
                                            <span height="25"></span>
                                        </th>
                                        <th class="total_qty_declare qty_declare" width="65" height="25">
                                            <span height="25">总数量<br>Total Qty</span>
                                        </th>
                                        <th class="weight_declare" height="25">
                                            <span class="weight-unit" height="25">总重量<br>Total weight</span>
                                        </th>
                                        <th class="price_declare" height="25">
                                            <span class="currency-header" height="25">总价值<br>Total value</span>
                                        </th>
                                        <th class="origin_declare none origin" height="25">
                                            <span height="25"></span>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="no_declare none no" height="25">
                                            <span height="25"></span>
                                        </th>
                                        <td class="text-center tfoot origin_total" height="25"><span height="25">CN</span></td>
                                        <th class="none customsNo_declare customsNo" height="25">
                                            <span height="25" class="orderDeclareHsCode"></span>
                                        </th>
                                        <td class="total_qty_declare qty_declare tfoot" height="25">
                                            <span id="name_declare_total_qty" height="25" class="orderDeclareQuantity">1</span>
                                        </td>
                                        <td class="weight_declare tfoot" height="25">
                                            <span class="unit-kg" height="25"><hls id="name_declare_total_weight1" name="labelStyle.netWeight1" class="orderDeclareUnitNetWeightD">0.14</hls></span>
                                            <span class="unit-kg-body disn" height="25" >KG</span>
                                        </td>
                                        <td class="price_declare tfoot" height="25">
                                            <span id="currency_before_" class="currency-before disn" height="25">USD</span>
                                            <span height="25"><hls id="name_declare_total_sum" name="labelStyle.productSumD" class="orderDeclareUnitDeclarePriceD">5.6</hls></span>
                                            <span id="currency_after_" class="currency-after disn" height="25">USD</span>
                                        </td>
                                        <th class="origin_declare none origin" height="25">
                                            <span height="25" class="orderDeclareOrigin"></span>
                                        </th>
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="dragitem character productmemo ui-draggable" data-type="character" data-default-width="150">
                            <strong class="title pickMemoTitle"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>配货信息</strong>
                            <span class="detail pickMemoList" id="pickMemoList">
                            <hls id="ITEM_LIST_DETAIL_MEMO" name="labelStyle.itemList[0].pickingRemark"><span><span class="orderDeclarePickingRemark">摄像机/防水数码摄象机 * 1;</span><span class="orderDeclarePickingRemark disn">防水数码摄象机 * 1;</span></span></hls>
                            </span>
                        </div>
                        <div class="dragitem character productCname ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title chineseNameTitle"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>中文品名</strong>
                        <span class="detail chineseNameList" id="chineseNameList">
                            <hls id="ITEM_LIST_DETAIL_CHINESE_NAME" name="labelStyle.itemList[0].chineseName"><span><span class="name_cn orderDeclareChineseName">包装袋</span></span></hls>
                            </span>
                       </div>
                       <div class="dragitem character productEname ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title englishNameTitle"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>英文品名</strong>
                        <span class="detail englishNameList" id="englishNameList">
                            <hls id="ITEM_LIST_DETAIL_ENGLISH_NAME" name="labelStyle.itemList[0].englishName"><span><span class="name orderDeclareEnglishName">PACK BAG</span></span></hls>
                            </span>
                       </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>申报货品总数量 </strong>
                        <span class="detail"><hls id="rfo" class="orderDeclareQuantity" name="labelStyle.orderDeclareQuantity">23</hls></span>
                      </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>申报货品总净重 </strong>
                        <span class="detail"><hls id="cno" class="orderDeclareUnitNetWeightD" name="labelStyle.orderDeclareUnitNetWeightD">2.3</hls></span>
                      </div>
                      <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                        <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>申报货品总金额 </strong>
                        <span class="detail"><hls id="cno" class="orderDeclareUnitDeclarePriceD" name="labelStyle.orderDeclareUnitDeclarePriceD">234.2</hls></span>
                      </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#element" data-parent="#type-group" data-toggle="collapse" aria-expanded="false"> <span class="glyphicon glyphicon-menu-down"></span>构图元素
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="element" aria-expanded="false">
                    <div class="panel-body">
                        <div class="dragitem draw ui-draggable" data-type="line-x">
                            <i class="ico-resize-horizontal"></i><strong class="title">水平线</strong>
                        </div>
                        <div class="dragitem draw ui-draggable" data-type="line-y" data-default-width="2">
                            <i class="ico-resize-vertical"></i><strong class="title">垂直线</strong>
                        </div>
                        <div class="dragitem draw ui-draggable" data-type="customtext" data-default-width="100">
                            <i class="glyphicon glyphicon-text-width"></i><strong class="title">自订文本</strong>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed lo-a" href="#imagelibrary" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                            <span class="glyphicon glyphicon-menu-down"></span>预设图片
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse" id="imagelibrary" aria-expanded="false">
                    <div class="panel-body" id="imagelibrary-div">
                        <div class="dragitem image ui-draggable" data-type="image">
                            <strong class="title disn">预设图片</strong>
                            <img src="images/preset/UPS_label.png">
                        </div>
                    </div>
                </div>
            </div>

          <div class="panel panel-default">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a class="collapsed lo-a" href="#baginfo" data-parent="#type-group" data-toggle="collapse" aria-expanded="false">
                  <span class="glyphicon glyphicon-menu-down"></span>袋牌信息
                </a>
              </h4>
            </div>
            <div class="panel-collapse collapse" id="baginfo" aria-expanded="false">
              <div class="panel-body">
                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>袋牌号</strong>
                  <span class="detail"><hls id="bagNo" class="wsComOutWarehouseBagCardBagNo">BAG1000000024</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>供应商袋牌号</strong>
                  <span class="detail"><hls id="providerBagNo" class="wsComOutWarehouseBagCardBagNo">BAG1000000024</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>供应商代码</strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardProviderCode">UPS</hls></span>
                </div>
                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>尾程渠道名称</strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardDeliveryChannelName">测试渠道</hls></span>
                </div>
                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>物流产品名称</strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardLogisticsProductName">测试产品</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>容器毛重(KG) </strong>
                  <span class="detail"><hls id="wsComOutWarehouseBagCardGrossWeightD" class="wsComOutWarehouseBagCardGrossWeightD">0.14</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>容器净重(KG) </strong>
                  <span class="detail"><hls id="wsComOutWarehouseBagCardNetWeightD" class="wsComOutWarehouseBagCardNetWeightD">0.14</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="170" data-default-height="57">
                  <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>袋牌号条码 </strong>
                  <span class="detail">
                  <hls class="wsComOutWarehouseBagCardBagNoBarcode" name="labelStyle.wsComOutWarehouseBagCardBagNoBarcode">
                  <div style="font-size:72px;"><div class="default-barcode">BAG1000000024</div></div>
                  </hls>
                  <span class="codeNumber"><hls id="bagNo" class="wsComOutWarehouseBagCardBagNo barcode-text">BAG1000000024</hls></span>
                  </span>
                </div>

                <div class="dragitem character ui-draggable" data-type="barcode" data-default-width="170" data-default-height="57">
                  <strong class="title"><i class="glyphicon glyphicon-barcode" aria-hidden="true"></i>供应商袋牌号条码 </strong>
                  <span class="detail">
                  <hls class="wsComOutWarehouseBagCardProviderBagNoBarcode" name="labelStyle.wsComOutWarehouseBagCardProviderBagNoBarcode">
                  <div style="font-size:72px;"><div class="default-barcode">BAG1000000024</div></div>
                  </hls>
                  <span class="codeNumber"><hls id="providerBagNo" class="wsComOutWarehouseBagCardProviderBagNo barcode-text">BAG1000000024</hls></span>
                  </span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>供应商代码 </strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardProviderCode">zhangsan</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>目的地-英文-中文 </strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardDestinationCountry">美国</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>包裹件数 </strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardTotalVotes" name="labelStyle.wsComOutWarehouseBagCardTotalVotes">1</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>批次号 </strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardBatchNo" name="labelStyle.wsComOutWarehouseBagCardBatchNo">OUT-953453-202004001</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>供应商批次号 </strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardProviderBatchNo" name="labelStyle.wsComOutWarehouseBagCardProviderBatchNo">OUT-953453-202004001</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>目的仓 </strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardDestinationWarehouse" name="labelStyle.wsComOutWarehouseBagCardDestinationWarehouse">坂田火车站货场</hls></span>
                </div>

                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>发件网点</strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardFranchisePoint" name="labelStyle.wsComOutWarehouseBagCardFranchisePoint">宝安西乡网点</hls></span>
                </div>
                <div class="dragitem character ui-draggable" data-type="character" data-default-width="150">
                  <strong class="title"><i class="glyphicon glyphicon-text-width" aria-hidden="true"></i>备注</strong>
                  <span class="detail"><hls class="wsComOutWarehouseBagCardRemark" name="labelStyle.wsComOutWarehouseBagCardRemark">备注内容</hls></span>
                </div>

              </div>
            </div>
          </div>

        </div>
    </aside>
    <!-- label options set-->
    <aside class="label-set">
        <div class="panel panel-default tab-content">
            <div class="panel-heading">
                <p class="panel-title">发件人地址</p>
            </div>
            <div class="panel-toolbar-wrapper">
                <div class="panel-toolbar">
                    <ul class="nav nav-tabs nav-justified">
                        <li class="title"><a data-toggle="tab" href="#option-title" aria-expanded="true">标题</a></li>
                        <li class="detail appear active"><a data-toggle="tab" href="#option-detail" aria-expanded="true">内容</a></li>
                        <li class="field-address appear"><a data-toggle="tab" href="#option-field-address" aria-expanded="false">字段</a></li>
                        <li class="text"><a data-toggle="tab" href="#option-text">文字</a></li>
                        <li class="imgurl"><a data-toggle="tab" href="#option-imgurl">路径</a></li>
                        <li class="barcode appear"><a data-toggle="tab" href="#option-barcode" aria-expanded="false">条形码</a></li>
                        <li class="border appear"><a data-toggle="tab" href="#option-border" aria-expanded="false">边框</a></li>
                        <li class="table"><a data-toggle="tab" href="#option-table">表格</a></li>
                        <li class="field-sku"><a data-toggle="tab" href="#option-field-sku">字段</a></li>
                        <li class="field-declare"><a data-toggle="tab" href="#option-field-declare">字段</a></li>
                    </ul>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-title">
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="viewTitle">显示标题</label>
                </div>
                <div class="title-set">
                    <div class="form-group">
                        <p class="control-label mb5">标题文本:</p>
                        <input type="text" value="1" name="" class="form-control" id="titleName">
                    </div>
                    <div class="form-group">
                        <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="titleNowrap">标题整行显示</label>
                    </div>
                    <div class="moreinfo disn">
                        <div class="form-group">
                            <p class="control-label mb5">标题对齐方式:</p>
                            <select class="form-control" id="titleAlign">
                                <option value="left" selected="">左对齐</option>
                                <option value="center">中对齐</option>
                                <option value="right">右对齐</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <p class="control-label mb5">标题与内容间距:</p>
                            <select class="form-control" id="titlePaddingBottom">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">标题文字尺寸:</p>
                        <select class="form-control" id="titleFontSize">
                            <option value="6px">6px</option>
                            <option value="7px">7px</option>
                            <option value="8px">8px</option>
                            <option value="9px">9px</option>
                            <option value="10px">10px</option>
                            <option value="11px">11px</option>
                            <option value="12px" selected="">12px</option>
                            <option value="14px">14px</option>
                            <option value="18px">18px</option>
                            <option value="24px">24px</option>
                            <option value="30px">30px</option>
                            <option value="36px">36px</option>
                            <option value="48px">48px</option>
                            <option value="60px">60px</option>
                            <option value="72px">72px</option>
                            <option value="9pt">9pt</option>
                            <option value="10pt">10pt</option>
                            <option value="11pt">11pt</option>
                            <option value="12pt">12pt</option>
                            <option value="14pt">14pt</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">标题行距:</p>
                        <select class="form-control" id="titleLineHeight">
                            <option value="0.5">0.5倍</option>
                            <option value="0.6">0.6倍</option>
                            <option value="0.7">0.7倍</option>
                            <option value="0.8">0.8倍</option>
                            <option value="0.9">0.9倍</option>
                            <option value="1.0" selected="">1.0倍</option>
                            <option value="1.1">1.1倍</option>
                            <option value="1.2">1.2倍</option>
                            <option value="1.3">1.3倍</option>
                            <option value="1.4">1.4倍</option>
                            <option value="1.5">1.5倍</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="titleFontWeight" checked="checked">标题文字加粗</label>
                    </div>
                </div>
            </div>
            <div class="panel-body tab-pane active" id="option-detail">
                <div class="form-group disn content">
                    <p class="control-label mb5">内容文本:</p>
                    <input type="text" value="1" name="" class="form-control" id="detailName">
                </div>
                <div class="form-group">
                    <p class="control-label mb5">内容文字对齐方式:</p>
                    <select class="form-control" id="detailAlign">
                        <option value="left" selected="">左对齐</option>
                        <option value="center">中对齐</option>
                        <option value="right">右对齐</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">内容文字尺寸:</p>
                    <select class="form-control" id="detaillFontSize">
                        <option value="6px">6px</option>
                        <option value="7px">7px</option>
                        <option value="8px">8px</option>
                        <option value="9px">9px</option>
                        <option value="10px">10px</option>
                        <option value="11px">11px</option>
                        <option value="12px" selected="">12px</option>
                        <option value="14px">14px</option>
                        <option value="18px">18px</option>
                        <option value="24px">24px</option>
                        <option value="30px">30px</option>
                        <option value="36px">36px</option>
                        <option value="48px">48px</option>
                        <option value="60px">60px</option>
                        <option value="72px">72px</option>
                        <option value="9pt">9pt</option>
                        <option value="10pt">10pt</option>
                        <option value="11pt">11pt</option>
                        <option value="12pt">12pt</option>
                        <option value="14pt">14pt</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">内容文字行距:</p>
                    <select class="form-control" id="detailLineHeight">
                        <option value="0.5">0.5倍</option>
                        <option value="0.6">0.6倍</option>
                        <option value="0.7">0.7倍</option>
                        <option value="0.8">0.8倍</option>
                        <option value="0.9">0.9倍</option>
                        <option value="1.0" selected="">1.0倍</option>
                        <option value="1.1">1.1倍</option>
                        <option value="1.2">1.2倍</option>
                        <option value="1.3">1.3倍</option>
                        <option value="1.4">1.4倍</option>
                        <option value="1.5">1.5倍</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="bold" name="" id="detailFontWeight">内容文字加粗</label>
                </div>
                <div class="form-group" id="showProductNameDiv">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="showProductName" id="showProductName">是否展示中文品名</label>
                </div>
                <div class="form-group disn">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="wnoChange">运单号需转换</label>
                </div>
                <div class="form-group disn">
                    <p class="control-label mb5">单号转换前规则:</p>
                    <input type="text" value="" name="" class="form-control" id="wnoRuleBefore">
                    <p class="control-label mb5">单号转换后规则:</p>
                    <input type="text" value="" name="" class="form-control" id="wnoRuleAfter">
                    <a href="javascript:void(0);" class="add lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-plus" aria-hidden="true"></i></a>
                    <input type="text" value="" name="" class="form-control" id="wnoRuleList">
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-text">
                <div class="form-group">
                    <p class="control-label mb5">自定义文本:</p>
                    <textarea value="1" class="form-control" id="textDetail" rows="5" placeholder="ANY CONTENT"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">文本对齐方式:</p>
                    <select class="form-control" id="textAlign">
                        <option value="left" selected="">左对齐</option>
                        <option value="center">中对齐</option>
                        <option value="right">右对齐</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">文本文字尺寸:</p>
                    <select class="form-control" id="textFontSize">
                        <option value="6px">6px</option>
                        <option value="7px">7px</option>
                        <option value="8px">8px</option>
                        <option value="9px">9px</option>
                        <option value="10px">10px</option>
                        <option value="11px">11px</option>
                        <option value="12px" selected="">12px</option>
                        <option value="14px">14px</option>
                        <option value="18px">18px</option>
                        <option value="24px">24px</option>
                        <option value="30px">30px</option>
                        <option value="36px">36px</option>
                        <option value="48px">48px</option>
                        <option value="60px">60px</option>
                        <option value="72px">72px</option>
                        <option value="9pt">9pt</option>
                        <option value="10pt">10pt</option>
                        <option value="11pt">11pt</option>
                        <option value="12pt">12pt</option>
                        <option value="14pt">14pt</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">文本行距:</p>
                    <select class="form-control" id="textLineHeight">
                        <option value="0.5">0.5倍</option>
                        <option value="0.6">0.6倍</option>
                        <option value="0.7">0.7倍</option>
                        <option value="0.8">0.8倍</option>
                        <option value="0.9">0.9倍</option>
                        <option value="1.0" selected="">1.0倍</option>
                        <option value="1.1">1.1倍</option>
                        <option value="1.2">1.2倍</option>
                        <option value="1.3">1.3倍</option>
                        <option value="1.4">1.4倍</option>
                        <option value="1.5">1.5倍</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="textFontWeight">文本文字加粗</label>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="textCheckBox">含复选框 <i class="glyphicon glyphicon-check"></i></label>
                </div>
                <div class="form-group disn">
                    <select class="form-control" id="checkBoxType">
                        <option value="ico-checkbox-unchecked" selected="">未勾选</option>
                        <option value="ico-checkbox">勾选打钩</option>
                        <option value="ico-checkbox-remove">勾选打叉</option>
                    </select>
                </div>
                <div class="form-group disn">
                    <label class="checkbox-inline" id="product_checkbox"><input type="checkbox" value="OrderType" id="detailOrderType" name="viewField">按照订单所选货品类型</label>
                </div>
                <div class="form-group disn">
                    <select class="form-control" id="productType">
                        <option value="G" selected="">礼物</option>
                        <option value="D">文件</option>
                        <option value="S">商业样本</option>
                        <option value="R">退回货品</option>
                        <option value="O">其他</option>
                    </select>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-border">
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" id="borderTop" name="" value="1">显示上边框</label>
                    <div class="row mt5">
                        <div class="col-sm-6">
                            <p class="control-label mb5">上边框厚度:</p>
                            <select class="form-control" id="borderTopWidth" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <p class="control-label mb5">上边距:</p>
                            <select class="form-control" id="paddingTop" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                                <option value="6px">6px</option>
                                <option value="7px">7px</option>
                                <option value="8px">8px</option>
                                <option value="9px">9px</option>
                                <option value="10px">10px</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" id="borderBottom" name="" value="1">显示下边框</label>
                    <div class="row mt5">
                        <div class="col-sm-6">
                            <p class="control-label mb5">下边框厚度:</p>
                            <select class="form-control" id="borderBottomWidth" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <p class="control-label mb5">下边距:</p>
                            <select class="form-control" id="paddingBottom" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                                <option value="6px">6px</option>
                                <option value="7px">7px</option>
                                <option value="8px">8px</option>
                                <option value="9px">9px</option>
                                <option value="10px">10px</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" id="borderLeft" name="" value="1">显示左边框</label>
                    <div class="row mt5">
                        <div class="col-sm-6">
                            <p class="control-label mb5">左边框厚度:</p>
                            <select class="form-control" id="borderLeftWidth" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <p class="control-label mb5">左边距:</p>
                            <select class="form-control" id="paddingLeft" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                                <option value="6px">6px</option>
                                <option value="7px">7px</option>
                                <option value="8px">8px</option>
                                <option value="9px">9px</option>
                                <option value="10px">10px</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" id="borderRight" name="" value="1">显示右边框</label>
                    <div class="row mt5">
                        <div class="col-sm-6">
                            <p class="control-label mb5">右边框厚度:</p>
                            <select class="form-control" id="borderRightWidth" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <p class="control-label mb5">右边距:</p>
                            <select class="form-control" id="paddingRight" disabled="disabled">
                                <option value="0px" selected="">0px</option>
                                <option value="1px">1px</option>
                                <option value="2px">2px</option>
                                <option value="3px">3px</option>
                                <option value="4px">4px</option>
                                <option value="5px">5px</option>
                                <option value="6px">6px</option>
                                <option value="7px">7px</option>
                                <option value="8px">8px</option>
                                <option value="9px">9px</option>
                                <option value="10px">10px</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-line-x">
                <div class="form-group">
                    <p class="control-label mb5">线条宽度(单位:px):</p>
                    <span class="customnum clear">
<a href="javascript:void(0);" class="subtract subtract-x lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-minus" aria-hidden="true"></i></a>
<input id="xLineWidth" type="text" class="form-control" onkeyup="value=value.replace(/[^\d]/g,'') ">
<a href="javascript:void(0);" class="add add-x lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-plus" aria-hidden="true"></i></a>
<button type="button" class="lo-btn lo-btn-primary lo-btn-xs fr" id="setMaxWidth">设为100%</button>
</span>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">线条类型:</p>
                    <select class="form-control" id="xLineStyle">
                        <option value="solid" selected="">实线</option>
                        <option value="dotted">点状线</option>
                        <option value="dashed">虚线</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">线条粗细:</p>
                    <select class="form-control" id="xLineWeight">
                        <option value="1px" selected="">1px</option>
                        <option value="2px">2px</option>
                        <option value="3px">3px</option>
                        <option value="4px">4px</option>
                        <option value="5px">5px</option>
                        <option value="6px">6px</option>
                    </select>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-line-y">
                <div class="form-group">
                    <p class="control-label mb5">线条高度(单位:px):</p>
                    <span class="customnum clear">
<a href="javascript:void(0);" class="subtract subtract-y lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-minus" aria-hidden="true"></i></a>
<input id="yLineHeight" type="text" class="form-control" onkeyup="value=value.replace(/[^\d]/g,'') ">
<a href="javascript:void(0);" class="add add-y lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-plus" aria-hidden="true"></i></a>
<button type="button" class="lo-btn lo-btn-primary lo-btn-xs fr" id="setMaxHeight">设为100%</button>
</span>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">线条类型:</p>
                    <select class="form-control" id="yLineStyle">
                        <option value="solid" selected="">实线</option>
                        <option value="dotted">点状线</option>
                        <option value="dashed">虚线</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">线条粗细:</p>
                    <select class="form-control" id="yLineWeight">
                        <option value="1px" selected="">1px</option>
                        <option value="2px">2px</option>
                        <option value="3px">3px</option>
                        <option value="4px">4px</option>
                        <option value="5px">5px</option>
                        <option value="6px">6px</option>
                    </select>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-circletext">
                <div class="form-group">
                    <p class="control-label mb5">圆边粗细:</p>
                    <select class="form-control" id="circleBorderWidth">
                        <option value="1px">1px</option>
                        <option value="2px" selected="">2px</option>
                        <option value="3px">3px</option>
                        <option value="4px">4px</option>
                        <option value="5px">5px</option>
                        <option value="6px">6px</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">内容文本:</p>
                    <input type="text" value="A" name="" class="form-control" id="circleText">
                </div>
                <div class="form-group">
                    <p class="control-label mb5">内容文字尺寸:</p>
                    <select class="form-control" id="circleFontSize">
                        <option value="6px">6px</option>
                        <option value="8px">8px</option>
                        <option value="7px">7px</option>
                        <option value="9px">9px</option>
                        <option value="10px">10px</option>
                        <option value="11px">11px</option>
                        <option value="12px">12px</option>
                        <option value="14px">14px</option>
                        <option value="18px">18px</option>
                        <option value="24px" selected="">24px</option>
                        <option value="30px">30px</option>
                        <option value="36px">36px</option>
                        <option value="48px">48px</option>
                        <option value="60px">60px</option>
                        <option value="72px">72px</option>
                        <option value="9pt">9pt</option>
                        <option value="10pt">10pt</option>
                        <option value="11pt">11pt</option>
                        <option value="12pt">12pt</option>
                        <option value="14pt">14pt</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">内容文字行距:</p>
                    <select class="form-control" id="circleLineHeight">
                        <option value="0.5">3.5倍</option>
                        <option value="0.6">0.6倍</option>
                        <option value="0.7">0.7倍</option>
                        <option value="0.8">0.8倍</option>
                        <option value="0.9">0.9倍</option>
                        <option value="1.0" selected="">1.0倍</option>
                        <option value="1.1">1.1倍</option>
                        <option value="1.2">1.2倍</option>
                        <option value="1.3">1.3倍</option>
                        <option value="1.4">1.4倍</option>
                        <option value="1.5">1.5倍</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="circleFontWeight" checked="">文字内容加粗</label>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-barcode">
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="viewCodeNum" checked="checked">显示条码代码</label>
                </div>
                <div class="codenum-set">
                    <div class="form-group">
                        <p class="control-label mb5">条码代码文字对齐方式:</p>
                        <select class="form-control" id="codeNumAlign">
                            <option value="left">左对齐</option>
                            <option value="center" selected="">中对齐</option>
                            <option value="right">右对齐</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">条码缩放:</p>
                        <select class="form-control" id="codeFontStretch">
                            <option value="10%">10%</option>
                            <option value="20%">20%</option>
                            <option value="25%">25%</option>
                            <option value="30%">30%</option>
                            <option value="35%">35%</option>
                            <option value="40%">40%</option>
                            <option value="45%">45%</option>
                            <option value="50%" selected="">50%</option>
                            <option value="55%">55%</option>
                            <option value="60%">60%</option>
                            <option value="65%">65%</option>
                            <option value="70%">70%</option>
                            <option value="75%">75%</option>
                            <option value="80%">80%</option>
                            <option value="85%">85%</option>
                            <option value="90%">90%</option>
                            <option value="95%">95%</option>
                            <option value="100%">100%</option>
                            <option value="110%">110%</option>
                            <option value="120%">120%</option>
                            <option value="140%">140%</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">条码代码文字尺寸:</p>
                        <select class="form-control" id="codeNumFontSize">
                            <option value="6px">6px</option>
                            <option value="7px">7px</option>
                            <option value="8px">8px</option>
                            <option value="9px">9px</option>
                            <option value="10px">10px</option>
                            <option value="11px">11px</option>
                            <option value="12px" selected="">12px</option>
                            <option value="14px">14px</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="codeNumFontWeight">条码代码文字加粗</label>
                    </div>
                    <div class="form-group disn">
                        <p class="control-label mb5">条码宽度(单位:px):</p>
                        <span class="barcodeWidth clear">
<a href="javascript:void(0);" class="subtract subtract-x lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-minus" aria-hidden="true"></i></a>
<input id="xBarcodeWidth" type="text" class="form-control" onkeyup="value=value.replace(/[^\d]/g,'') ">
<a href="javascript:void(0);" class="add add-x lo-btn lo-btn-primary lo-btn-xs pl5 pr5"><i class="glyphicon glyphicon-plus" aria-hidden="true"></i></a>
</span>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">条码高度:</p>
                        <select class="form-control" id="xBarcodeHeight">
                            <option value="20px" selected="">20px</option>
                            <option value="25px">25px</option>
                            <option value="30px">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px">40px</option>
                            <option value="45px">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">单号:</p>
                        <input type="text" name="barcodeTxt" class="form-control" id="barcodeTxt">
                    </div>
                    <!-- <div class="form-group">
                    <p class="control-label mb5">条码代码前缀:</p>
                    <input type="text" value="" name="" class="form-control" id="codePrefix">
                    </div> -->
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-table">
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="viewTdBorder" checked="">显示表格框线</label>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="viewThead" checked="">显示表头</label>
                </div>
                <div class="moreinfo">
                    <div class="form-group">
                        <p class="control-label mb5">表头文字尺寸:</p>
                        <select class="form-control" id="theadFontSize">
                            <option value="6px">6px</option>
                            <option value="7px">7px</option>
                            <option value="8px">8px</option>
                            <option value="9px">9px</option>
                            <option value="10px">10px</option>
                            <option value="11px">11px</option>
                            <option value="12px" selected="">12px</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">表头文字行距:</p>
                        <select class="form-control" id="theadLineHeight">
                            <option value="0.5">0.5倍</option>
                            <option value="0.6">0.6倍</option>
                            <option value="0.7">0.7倍</option>
                            <option value="0.8">0.8倍</option>
                            <option value="0.9">0.9倍</option>
                            <option value="1.0" selected="">1.0倍</option>
                            <option value="1.1">1.1倍</option>
                            <option value="1.2">1.2倍</option>
                            <option value="1.3">1.3倍</option>
                            <option value="1.4">1.4倍</option>
                            <option value="1.5">1.5倍</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">表头高度:</p>
                        <select class="form-control" id="theadHeight">
                            <option value="10px">10px</option>
                            <option value="15px">15px</option>
                            <option value="20px">20px</option>
                            <option value="25px">25px</option>
                            <option value="30px" selected="">30px</option>
                            <option value="40px">40px</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">表格内容文字尺寸:</p>
                    <select class="form-control" id="tbodyFontSize">
                        <option value="6px">6px</option>
                        <option value="7px">7px</option>
                        <option value="8px">8px</option>
                        <option value="9px">9px</option>
                        <option value="10px">10px</option>
                        <option value="11px">11px</option>
                        <option value="12px" selected="">12px</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">表格文字行距:</p>
                    <select class="form-control" id="tbodyLineHeight">
                        <option value="0.5">0.5倍</option>
                        <option value="0.6">0.6倍</option>
                        <option value="0.7">0.7倍</option>
                        <option value="0.8">0.8倍</option>
                        <option value="0.9">0.9倍</option>
                        <option value="1.0" selected="selected">1.0倍</option>
                        <option value="1.1">1.1倍</option>
                        <option value="1.2">1.2倍</option>
                        <option value="1.3">1.3倍</option>
                        <option value="1.4">1.4倍</option>
                        <option value="1.5">1.5倍</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">表格内容高度:</p>
                    <select class="form-control" id="tbodyHeight">
                        <option value="10px">10px</option>
                        <option value="15px" selected="selected">15px</option>
                        <option value="20px">20px</option>
                        <option value="25px">25px</option>
                        <option value="30px">30px</option>
                        <option value="40px">40px</option>
                    </select>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">表格内容宽度:</p>
                    <div class="td-group">
                        <span class="form-text">序号：</span>
                        <select class="form-control-select" id="noWidth">
                            <option value="10px">10px</option>
                            <option value="15px" selected="selected">15px</option>
                            <option value="20px">20px</option>
                            <option value="25px">25px</option>
                            <option value="30px">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px">40px</option>
                            <option value="45px">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                        </select>
                    </div>
                    <div class="td-group">
                        <span class="form-text">海关编码：</span>
                        <select class="form-control-select" id="customsNoWidth">
                            <option value="30px">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px">40px</option>
                            <option value="45px">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                            <option value="65px" selected="selected">65px</option>
                            <option value="70px">70px</option>
                        </select>
                    </div>
                    <div class="td-group">
                        <span class="form-text">品名：</span>
                        <select class="form-control-select" id="nameWidth">
                            <option value="60px">60px</option>
                            <option value="65px" selected="selected">65px</option>
                            <option value="70px">70px</option>
                            <option value="80px">80px</option>
                            <option value="90px">90px</option>
                            <option value="100px">100px</option>
                            <option value="120px">120px</option>
                            <option value="130px">130px</option>
                            <option value="140px">140px</option>
                            <option value="150px">150px</option>
                            <option value="160px">160px</option>
                            <option value="170px">170px</option>
                            <option value="180px">180px</option>
                            <option value="190px">190px</option>
                            <option value="200px">200px</option>
                            <option value="250px">250px</option>
                            <option value="300px">300px</option>
                        </select>
                    </div>
                    <div class="td-group">
                        <span class="form-text">报关数量：</span>
                        <select class="form-control-select" id="qtyWidth">
                            <option value="30px" selected="selected">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px">40px</option>
                            <option value="45px">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                            <option value="65px">65px</option>
                            <option value="70px">70px</option>
                        </select>
                    </div>
                    <div class="td-group">
                        <span class="form-text">报关重量：</span>
                        <select class="form-control-select" id="weightWidth">
                            <option value="30px">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px">40px</option>
                            <option value="45px" selected="selected">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                            <option value="65px">65px</option>
                            <option value="70px">70px</option>
                        </select>
                    </div>
                    <div class="td-group">
                        <span class="form-text">报关价值：</span>
                        <select class="form-control-select" id="priceWidth">
                            <option value="30px">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px" selected="selected">40px</option>
                            <option value="45px">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                            <option value="65px">65px</option>
                            <option value="70px">70px</option>
                        </select>
                    </div>
                    <div class="td-group">
                        <span class="form-text">原产地：</span>
                        <select class="form-control-select" id="originWidth">
                            <option value="30px">30px</option>
                            <option value="35px">35px</option>
                            <option value="40px">40px</option>
                            <option value="45px" selected="selected">45px</option>
                            <option value="50px">50px</option>
                            <option value="55px">55px</option>
                            <option value="60px">60px</option>
                            <option value="65px">65px</option>
                            <option value="70px">70px</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="viewTfoot" checked="">显示脚注</label>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="viewTfootHeader" checked="">显示脚注标题</label>
                </div>
                <div class="moreinfo">
                    <div class="form-group">
                        <p class="control-label mb5">脚注文字尺寸:</p>
                        <select class="form-control" id="tfootFontSize">
                            <option value="6px">6px</option>
                            <option value="7px">7px</option>
                            <option value="8px">8px</option>
                            <option value="9px">9px</option>
                            <option value="10px">10px</option>
                            <option value="11px">11px</option>
                            <option value="12px" selected="">12px</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">脚注文字行距:</p>
                        <select class="form-control" id="tfootLineHeight">
                            <option value="0.5">0.5倍</option>
                            <option value="0.6">0.6倍</option>
                            <option value="0.7">0.7倍</option>
                            <option value="0.8">0.8倍</option>
                            <option value="0.9">0.9倍</option>
                            <option value="1.0" selected="">1.0倍</option>
                            <option value="1.1">1.1倍</option>
                            <option value="1.2">1.2倍</option>
                            <option value="1.3">1.3倍</option>
                            <option value="1.4">1.4倍</option>
                            <option value="1.5">1.5倍</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <p class="control-label mb5">脚注高度:</p>
                        <select class="form-control" id="tfootHeight">
                            <option value="10px">10px</option>
                            <option value="15px">15px</option>
                            <option value="20px">20px</option>
                            <option value="25px">25px</option>
                            <option value="30px" selected="">30px</option>
                            <option value="40px">40px</option>
                        </select>
                    </div>
                    <!--<div class="form-group">
                    <p class="control-label mb5">脚注文字对齐方式:</p>
                    <select class="form-control" id="tfootAlign">
                    <option value="left">左对齐</option>
                    <option value="center">中对齐</option>
                    <option value="right" selected>右对齐</option>
                    </select>
                    </div>-->
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-field-address">
                <div class="form-group multiple">
                    <p class="control-label mb5">显示字段设置:</p>
                    <label class="checkbox-inline check"><input type="checkbox" value="name" name="viewField" checked="checked">显示姓名</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="street" name="viewField" checked="checked">显示街道</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="area" name="viewField" checked="checked">显示省份/城市/地区</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="country" name="viewField" checked="checked">显示国家</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="country_cn" name="viewField" checked="checked">显示国家中文名</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="country_en" name="viewField" checked="checked">显示国家英文名</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="postcode" name="viewField" checked="checked">显示邮编</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="tel1" name="viewField" checked="checked">显示电话</label>
                    <label class="checkbox-inline check"><input type="checkbox" value="tel2" name="viewField" checked="checked">显示手机</label>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">字段换行设置:</p>
                    <label class="checkbox-inline"><input type="checkbox" value="1" name="" id="newline" checked="checked">单字段换行</label>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-field-sku">
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="photo" name="viewField">显示商品缩略图</label>
                    <input type="text" id="fieldTextPhoto" class="form-control mt5" name="" value="图片" disabled="">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="sku" name="viewField" checked="">显示商品编号</label>
                    <input type="text" id="fieldTextSku" class="form-control mt5" name="" value="SKU">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="sku_original" name="viewField">显示原厂编号</label>
                    <input type="text" id="fieldTextOriginal" class="form-control mt5" name="" value="原厂SKU" disabled="disabled">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="itemid" name="viewField">显示itemID</label>
                    <input type="text" id="fieldTextItemid" class="form-control mt5" name="" value="itemId" disabled="disabled">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="name" name="viewField" checked="">显示中文名称</label>
                    <input type="text" id="fieldTextName" class="form-control mt5" name="" value="名称">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="name_en" name="viewField">显示英文名称</label>
                    <input type="text" id="fieldTextNameEn" class="form-control mt5" name="" value="name" disabled="disabled">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="warehouse" name="viewField">显示仓库</label>
                    <input type="text" id="fieldTextWarehouse" class="form-control mt5" name="" value="仓库" disabled="disabled">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="position" name="viewField" checked="">显示仓位</label>
                    <input type="text" id="fieldTextPosition" class="form-control mt5" name="" value="仓位">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="number" name="viewField" checked="">显示数量</label>
                    <input type="text" id="TextNumber" class="form-control mt5" name="" value="数量">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="weight" name="viewField">显示重量</label>
                    <input type="text" id="fieldTextWeight" class="form-control mt5" name="" value="重量(kg)" disabled="disabled">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="price" name="viewField">显示单价</label>
                    <input type="text" id="fieldTextPrice" class="form-control mt5" name="" value="单价" disabled="disabled">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="total" name="viewField">显示金额小计</label>
                    <input type="text" id="fieldTextTotal" class="form-control mt5" name="" value="小计" disabled="disabled">
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-field-declare">
                <div class="form-group">
                    <p class="control-label mb5">报关品名表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="5" id="declareNameTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关品名显示内容:</p>
                    <select class="form-control" id="declareType">
                        <option value="sort" selected="selected">显示英文品名</option>
                        <option value="group">多字段组合</option>
                    </select>
                </div>
                <div id="declareName">
                    <div class="form-group multiple sort disn">
                        <p class="control-label mb5">显示英文品名:</p>
                        <label class="checkbox-inline"><input type="checkbox" value="name" name="catalogue">显示商品英文名</label>
                    </div>
                    <div class="form-group multiple group disn">
                        <p class="control-label mb5">多字段组合内容格式:</p>
                        <label class="checkbox-inline"><input type="checkbox" value="name_cn" name="catalogue" checked="">显示商品中文名</label>
                        <label class="checkbox-inline"><input type="checkbox" value="name" name="catalogue">显示商品英文名</label>
                        <label class="checkbox-inline"><input type="checkbox" value="number" name="catalogue" checked="">显示数量</label>
                    </div>
                    <div class="form-group multiple custom disn">
                        <p class="control-label mb5">自定义文字内容:</p>
                        <textarea placeholder="请输入文本内容" rows="5" id="declareNameCustom" class="form-control">自定义内容</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="no" name="viewField" checked="">显示序号</label>
                    <input type="text" id="fieldTextNumber" class="form-control mt5" name="" value="序号">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="qty" name="viewField" checked="">显示数量</label>
                    <input type="text" id="fieldQty" class="form-control mt5" name="" value="数量">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="customsNo_declare orderDeclareListHsCode" name="viewField" checked="">显示海关编码</label>
                    <input type="text" id="fieldTextCustom" class="form-control mt5" name="" value="海关编码">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="origin_declare orderDeclareListOrigin" name="viewField" checked="">显示原产地</label>
                    <input type="text" id="fieldTextAddress" class="form-control mt5" name="" value="原产地">
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="totalQty" name="viewField" checked="">显示总数量</label>
                    <input type="text" id="fieldTextQty" class="form-control mt5" name="" value="总数量">
                </div>
                <div class="form-group">
                    <p class="control-label mb5">重量单位:</p>
                    <label class="radio-inline bold"><input type="radio" checked="checked" name="weight-unit" value="kg">千克(kg)</label>
                    <label class="radio-inline bold"><input type="radio" name="weight-unit" value="g">克(g)</label>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="currency" id="showWeightUnit" name="viewField">显示重量单位</label>
                </div>
                <div class="form-group">
                    <label class="checkbox-inline"><input type="checkbox" value="currency" id="showCurrency" name="viewField">显示币种</label>
                    <select id="fieldTextCurrency" name="labelStyle.currency" class="formText disn">
                        <option value="CNY">人民币</option>
                        <option value="USD" selected="selected">美元</option>
                        <option value="HKD">港币</option>
                        <option value="RUB">卢布</option>
                        <option value="JPN">日元</option>
                    </select>
                    <label class="checkbox-inline disn" id="switchCurrencyLabel"><input type="checkbox" id="switchCurrency" value="switchCurrency" name="viewField">是否需要转换为当前币种</label>
                </div>
                <div class="form-group disn" id="currency-position">
                    <p class="control-label mb5">币种展示位置:</p>
                    <label class="radio-currency-position bold"><input type="radio" name="currency-position" value="before">价值前</label>
                    <label class="radio-currency-position bold"><input type="radio" name="currency-position" value="after">价值后</label>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关序号表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareNoTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关数量表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareQtyTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关海关编码表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareCustomTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关重量表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareWeightTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关价值表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declarePriceTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关原产地表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="5" id="declareOriginTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">报关原产国文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareOrigin" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">协调系统税则号/原产国表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="5" id="declareOriginToTalTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">总数量表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareTotalQtyTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">总重量表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareTotalWeightTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">总价值表头文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareTotalPriceTitle" class="form-control" value="1"></textarea>
                </div>
                <div class="form-group">
                    <p class="control-label mb5">协调系统税则号/原产国文字:</p>
                    <textarea placeholder="请输入文本内容" rows="2" id="declareOriginToTal" class="form-control" value="1"></textarea>
                </div>
            </div>
            <div class="panel-body tab-pane" id="option-imgurl">
                <div class="form-group">
                    <p class="control-label mb5">在线地址:</p>
                    <textarea placeholder="请输入图片在线路径" rows="3" id="imageUrl" class="form-control" value="1"></textarea>
                    <button type="button" class="btn btn-default mt5 pl10 pr10" id="loadImgUrl">加载</button>
                    <p class="text-muted mt10" style="word-break: break-all; word-wrap: break-word;">
                        可输入以下路径测试: <small class="help-block text-danger mb0">http://www.qqpk.cn/Article/UploadFiles/201111/hb29.jpg</small>
                    </p>
                </div>
            </div>
        </div>
        <div class="btn-group edit-btn">
            <button class="lo-btn lo-btn-primary lo-btn-m lo-w-50 btn-copy" type="button">
                <i class="glyphicon glyphicon-duplicate mr5"></i>复制
            </button>
            <button class="lo-btn lo-btn-danger lo-btn-m lo-w-50 btn-clear-item" type="button">
                <i class="glyphicon glyphicon-remove mr5"></i>删除
            </button>
        </div>
    </aside>
    <!-- view-panel -->
    <aside class="view-panel">
        <div class="btn-group viewpct">
            <button type="button" class="lo-btn lo-btn-primary lo-btn-sm dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                <i class=" ico-search5 mr5"></i>显示比例:<span class="text pr5 pl5">100%</span><span class="caret"></span>
            </button>
            <button type="button" class="lo-btn lo-btn-primary lo-btn-sm btn-close-view">
                <i class="ico-exit mr5"></i>退出预览
            </button>
            <ul class="dropdown-menu pull-left copytext" role="menu">
                <li><a href="javascript:void(0);" rel="default">100%</a></li>
                <li><a href="javascript:void(0);" rel="scale150">150%</a></li>
                <li><a href="javascript:void(0);" rel="scale200">200%</a></li>
                <li><a href="javascript:void(0);" rel="scale300">300%</a></li>
            </ul>
        </div>
    </aside>
    <aside class="hotkey-panel" style="opacity: 0;">
        <a onclick="$('.hotkey-panel').remove()" href="javascript:void(0);" class="close-hotkey glyphicon glyphicon-remove-circle"></a>
        <h3>快捷键</h3>
        <ul>
            <li><span class="key"><i class="glyphicon glyphicon-arrow-up"></i></span><strong>上移</strong></li>
            <li><span class="key"><i class="glyphicon glyphicon-arrow-down"></i></span><strong>下移</strong></li>
            <li><span class="key"><i class="glyphicon glyphicon-arrow-left"></i></span><strong>左移</strong></li>
            <li><span class="key"><i class="glyphicon glyphicon-arrow-right"></i></span><strong>右移</strong></li>
            <li><span class="key">Delete</span>或<span class="key">Del</span><strong>删除</strong></li>
        </ul>
    </aside>
    <!-- custom palette -->
    <section class="custom-label">
        <div class="label-content ui-draggable" style="width: 98mm; height: 98mm; margin-left: -49mm; display: block;">
            <div class="viewCover"></div>
            <div class="ui-widget-content ui-droppable" id="custom-palette">
            </div>
        </div>
    </section>

</div>

<script type="text/javascript" src="js/diyStyle.js?v=V201709"></script>
</body></html>
