const t = {}

t.loading = 'Loading...'

t.brand = {}
t.brand.lg = 'WOMS'
t.brand.mini = 'ITS'

t.system = {}
t.system.companyId = '所属公司'
t.system.serialNumber = '序号'

t.detailMantain = '明细维护'
t.customer = '客户'
t.zoneFor = '分区区间'
t.addOrUpdate = 'add/update'
t.preview = 'Preview'
t.status = 'Status'
t.add = 'Add'
t.pushcustomerbills = 'pushBills'
t.delete = 'Delete'
t.trackByHand = 'TrackByHand'
t.deleteBatch = 'Delete'
t.update = 'Edit'
t.query = 'Query'
t.export = 'Export'
t.handle = 'Action'
t.confirm = 'Confirm'
t.submit = 'Submit'
t.cancel = 'Cancel'
t.logout = 'Sign Out'
t.enable = 'Enable'
t.disable = 'Disable'
t.pleaseSelect = '请选择'
t.seniorSearch = 'Senior Search'
t.showWxBindQrCode = '微信小程序'
t.view = 'View'
t.modifyConsigneeAddress = 'modify Consignee Address'
t.copy = 'Copy'
t.reset = 'Reset'
t.detail = 'Detail'

t.audit = 'audit'
t.abandon = 'Recoil'

t.prompt = {}
t.prompt.title = 'Prompt'
t.prompt.info = 'Confirm to carry out [{handle}] operation?'
t.prompt.success = 'Succeeded'
t.prompt.failed = 'Failed'
t.prompt.deleteBatch = 'Please select delete item'
t.prompt.auditBatch = 'Please select audit item'
t.prompt.abandonBatch = 'Please select abandon item'
t.prompt.finishBatch = 'Please select finish item'
t.prompt.actionStatusBatch = 'Please select operation item'
t.prompt.export = 'Please select export template'

t.validate = {}
t.volDiv = '请选择材积除!'
t.bubbleRate = '请设置分泡比例'
t.mismatchingBubbleCountingChargeableWeightRule = '请设置不符合计泡规则结算重算法'
t.validate.nonnegativeNumber = '只能输入非负数'
t.validate.required = 'Required field cannot be empty'
t.validate.format = '{attr} format error'

t.upload = {}
t.upload.text = 'Drop files here, or <em>Click Upload</em>'
t.upload.tip = 'Only support {format} format files! '
t.upload.button = 'Click to upload'

t.datePicker = {}
t.datePicker.range = 'To'
t.datePicker.start = 'Start Date'
t.datePicker.end = 'End Date'

t.fullscreen = {}
t.fullscreen.prompt = 'Your browser does not support this operation'

t.updatePassword = {}
t.updatePassword.title = 'Change Password'
t.updatePassword.username = 'Account'
t.updatePassword.password = 'Original'
t.updatePassword.newPassword = 'New Password'
t.updatePassword.confirmPassword = 'Confirm'
t.updatePassword.validate = {}
t.updatePassword.validate.confirmPassword = 'Confirm password is not consistent with new password input'

t.contentTabs = {}
t.contentTabs.closeCurrent = 'Close Current Tab'
t.contentTabs.closeOther = 'Close Other Tabs'
t.contentTabs.closeAll = 'Close All Tabs'

/* pages */
t.notFound = {}
t.notFound.desc = 'Sorry! <em>missing</em> on the page you visited...'
t.notFound.back = 'Previous Page'
t.notFound.home = 'Home'

t.login = {}
t.login.title = 'Sign In'
t.login.username = 'Username'
t.login.password = 'Password'
t.login.captcha = 'Verification Code'
t.login.demo = 'WOMS'
t.login.copyright = 'STO'
t.login.forgetPassword = 'forget password?'
t.login.mail = 'Mail'

t.forget = {}
t.forget.title = 'Forget Password'
t.forget.email = 'Please enter your email address'
t.forget.illegalEmail = 'Please enter the correct email format'
t.forget.send = 'Send E-mail'
t.forget.sendSuccess = 'The email to reset password has been sent to your mailbox, please note that check...'

t.reset = {}
t.reset.title = 'Reset Password'
t.reset.passwordFirst = 'Please enter your new password'
t.reset.passwordSecond = 'Please enter again'
t.reset.successMsgPerfix = 'Congratulations! Successful password reset! You can now log in with your new password'
t.reset.successMsgSuffix = 'S will automatically jump to the login page...'
t.reset.passwordDiff = 'Please enter the same password'

t.home = {}
t.home.desc = {}
t.home.desc.title = 'Project introduction'
t.home.desc.list = [
  'WOMS'
]

/* modules */
t.model = {}
t.model.name = 'Name'
t.model.key = 'Information'
t.model.version = 'Version'
t.model.createTime = 'Create Time'
t.model.lastUpdateTime = 'Update Time'
t.model.design = 'Online Design'
t.model.deploy = 'Deployment'
t.model.description = 'Description'

t.process = {}
t.process.name = 'name'
t.process.key = 'Identification'
t.process.deployFile = 'Deploy process file'
t.process.id = 'Process ID'
t.process.deploymentId = 'Deployment ID'
t.process.version = 'Version'
t.process.resourceName = 'XML'
t.process.diagramResourceName = 'Image'
t.process.deploymentTime = 'Deployment Time'
t.process.active = 'Activate'
t.process.suspend = 'Hang'
t.process.convertToModel = 'Convert to model'

t.running = {}
t.running.id = 'Instance ID'
t.running.definitionKey = 'Define Key'
t.running.processDefinitionId = 'Define ID'
t.running.processDefinitionName = 'Define the name'
t.running.activityId = 'Current Link'
t.running.suspended = 'Whether Hang'
t.running.suspended0 = 'No'
t.running.suspended1 = 'Yes'

t.news = {}
t.news.title = 'Title'
t.news.pubDate = 'Publish Time'
t.news.createDate = 'Create Time'
t.news.content = 'Content'

t.schedule = {}
t.schedule.beanName = 'Bean Name'
t.schedule.beanNameTips = 'Spring bean name, eg: testTask'
t.schedule.pauseBatch = 'Pause'
t.schedule.resumeBatch = 'Recovery'
t.schedule.runBatch = 'Execution'
t.schedule.log = 'Log List'
t.schedule.params = 'Parameters'
t.schedule.cronExpression = 'Cron Expression'
t.schedule.cronExpressionTips = 'Example: 0 0 12 * * ?'
t.schedule.remark = 'Remarks'
t.schedule.status = 'Status'
t.schedule.status0 = 'Pause'
t.schedule.status1 = 'Normal'
t.schedule.statusLog0 = 'Failed'
t.schedule.statusLog1 = 'Success'
t.schedule.pause = 'Pause'
t.schedule.resume = 'Restore'
t.schedule.run = 'Execute'
t.schedule.jobId = 'Task ID'
t.schedule.times = 'Time-consuming (ms)'
t.schedule.createDate = 'Execution Time'

t.mail = {}
t.mail.name = 'Name'
t.mail.config = 'Mail Configuration'
t.mail.subject = 'Theme'
t.mail.createDate = 'Create Time'
t.mail.send = 'Send Mail'
t.mail.content = 'Content'
t.mail.smtp = 'SMTP'
t.mail.port = 'Port Number'
t.mail.username = 'Email Account'
t.mail.password = 'Mailbox Password'
t.mail.mailTo = 'Recipient'
t.mail.mailCc = 'Cc'
t.mail.params = 'Template Parameter'
t.mail.paramsTips = 'Example: {"code": "123456"}'
t.mail.templateId = 'Template ID'
t.mail.status = 'Status'
t.mail.status0 = 'Send Failed'
t.mail.status1 = 'Successfully Sent'
t.mail.mailFrom = 'Sender'
t.mail.createDate = 'Send Time'

t.sms = {}
t.sms.mobile = 'Mobile Number'
t.sms.status = 'Status'
t.sms.status0 = 'Send Failed'
t.sms.status1 = 'Successfully Sent'
t.sms.config = 'SMS Configuration'
t.sms.send = 'Send SMS'
t.sms.platform = 'platform Type'
t.sms.platform1 = 'Alibaba Cloud'
t.sms.platform2 = 'Tencent Cloud'
t.sms.params = 'Parameters'
t.sms.paramsTips = 'eg: {"code": "123456"}'
t.sms.params1 = 'Parameter 1'
t.sms.params2 = 'Parameter 2'
t.sms.params3 = 'Parameter 3'
t.sms.params4 = 'Parameter 4'
t.sms.createDate = 'Send Time'
t.sms.aliyunAccessKeyId = 'Key'
t.sms.aliyunAccessKeyIdTips = 'Alibaba Cloud AccessKeyId'
t.sms.aliyunAccessKeySecret = 'Secret'
t.sms.aliyunAccessKeySecretTips = 'Alibaba Cloud AccessKeySecret'
t.sms.aliyunSignName = 'SMS Signature'
t.sms.aliyunTemplateCode = 'SMS Template'
t.sms.aliyunTemplateCodeTips = 'SMS Template CODE'
t.sms.qcloudAppId = 'AppId'
t.sms.qcloudAppIdTips = 'Tencent Cloud AppId'
t.sms.qcloudAppKey = 'AppKey'
t.sms.qcloudAppKeyTips = 'Tencent Cloud AppKey'
t.sms.qcloudSignName = 'SMS Signature'
t.sms.qcloudTemplateId = 'SMS Template'
t.sms.qcloudTemplateIdTips = 'SMS template ID'

t.oss = {}
t.oss.config = 'Cloud Storage Configuration'
t.oss.upload = 'Upload File'
t.oss.url = 'URL Address'
t.oss.createDate = 'Create Time'
t.oss.type = 'Type'
t.oss.type1 = 'Seven Cows'
t.oss.type2 = 'Alibaba Cloud'
t.oss.type3 = 'Tencent Cloud'
t.oss.type4 = 'FastDFS'
t.oss.type5 = 'Local Upload'
t.oss.qiniuDomain = 'Domain Name'
t.oss.qiniuDomainTips = 'Seven cattle bound domain name'
t.oss.qiniuPrefix = 'Path Prefix'
t.oss.qiniuPrefixTips = 'Do not set default to empty'
t.oss.qiniuAccessKey = 'AccessKey'
t.oss.qiniuAccessKeyTips = 'Seven cattle AccessKey'
t.oss.qiniuSecretKey = 'SecretKey'
t.oss.qiniuSecretKeyTips = 'Seven Cow SecretKey'
t.oss.qiniuBucketName = 'Space Name'
t.oss.qiniuBucketNameTips = 'Seven cattle storage space name'
t.oss.aliyunDomain = 'Domain Name'
t.oss.aliyunDomainTips = 'Alibaba Cloud bound domain name, such as: http://cdn.renren.io'
t.oss.aliyunPrefix = 'Path Prefix'
t.oss.aliyunPrefixTips = 'Do not set default to empty'
t.oss.aliyunEndPoint = 'EndPoint'
t.oss.aliyunEndPointTips = 'Ali Cloud EndPoint'
t.oss.aliyunAccessKeyId = 'AccessKeyId'
t.oss.aliyunAccessKeyIdTips = 'Alibaba Cloud AccessKeyId'
t.oss.aliyunAccessKeySecret = 'AccessKeySecret'
t.oss.aliyunAccessKeySecretTips = 'Alibaba Cloud AccessKeySecret'
t.oss.aliyunBucketName = 'BucketName'
t.oss.aliyunBucketNameTips = 'Alibaba Cloud BucketName'
t.oss.qcloudDomain = 'Domain Name'
t.oss.qcloudDomainTips = 'Tencent cloud bound domain name'
t.oss.qcloudPrefix = 'Path Prefix'
t.oss.qcloudPrefixTips = 'Do not set default to empty'
t.oss.qcloudAppId = 'AppId'
t.oss.qcloudAppIdTips = 'Tencent Cloud AppId'
t.oss.qcloudSecretId = 'SecretId'
t.oss.qcloudSecretIdTips = 'Tencent Cloud SecretD'
t.oss.qcloudSecretKey = 'SecretKey'
t.oss.qcloudSecretKeyTips = 'Tencent Cloud SecretKey'
t.oss.qcloudBucketName = 'BucketName'
t.oss.qcloudBucketNameTips = 'Tencent Cloud BucketName'
t.oss.qcloudRegion = 'Affiliate'
t.oss.qcloudRegionTips = 'Please Select'
t.oss.qcloudRegionBeijing1 = 'Beijing District 1 (North China)'
t.oss.qcloudRegionBeijing = 'Beijing'
t.oss.qcloudRegionShanghai = 'Shanghai (East China)'
t.oss.qcloudRegionGuangzhou = 'Guangzhou (South China)'
t.oss.qcloudRegionChengdu = 'Chengdu (Southwest)'
t.oss.qcloudRegionChongqing = 'Chongqing'
t.oss.qcloudRegionSingapore = 'Singapore'
t.oss.qcloudRegionHongkong = 'HongKong'
t.oss.qcloudRegionToronto = 'Toronto'
t.oss.qcloudRegionFrankfurt = 'Frankfurt'
t.oss.localDomain = 'Domain Name'
t.oss.localDomainTips = 'Binded domain name, eg http://cdn.renren.io:8084'
t.oss.fastdfsDomain = 'Domain Name'
t.oss.fastdfsDomainTips = 'Binded domain name, eg http://cdn.renren.io'
t.oss.localPrefix = 'Path Prefix'
t.oss.localPrefixTips = 'Do not set default to empty'
t.oss.localPath = 'Storage Directory'
t.oss.localPathTips = 'eg: D:/upload'

t.dept = {}
t.dept.name = 'Name'
t.dept.parentName = 'Superior'
t.dept.sort = 'Sort'
t.dept.parentNameDefault = 'Top Department'

t.dict = {}
t.dict.dictName = 'Name'
t.dict.dictType = 'Type'
t.dict.dictValue = 'Value'
t.dict.sort = 'Sort'
t.dict.remark = 'Remarks'
t.dict.createDate = 'Create Date'

t.logError = {}
t.logError.module = 'Module Name'
t.logError.requestUri = 'Request URI'
t.logError.requestMethod = 'Request Method'
t.logError.requestParams = 'Request Parameters'
t.logError.ip = 'IP'
t.logError.userAgent = 'User Agent'
t.logError.createDate = 'Create Date'
t.logError.errorInfo = 'Exception'

t.logLogin = {}
t.logLogin.creatorName = 'Username'
t.logLogin.status = 'Status'
t.logLogin.status0 = 'Failed'
t.logLogin.status1 = 'Success'
t.logLogin.status2 = 'Locked'
t.logLogin.operation = 'User Action'
t.logLogin.operation0 = 'Login'
t.logLogin.operation1 = 'Exit'
t.logLogin.ip = 'IP'
t.logLogin.userAgent = 'User-Agent'
t.logLogin.createDate = 'Create Date'

t.logOperation = {}
t.logOperation.module = 'Module Name'
t.logOperation.status = 'Status'
t.logOperation.status0 = 'Failed'
t.logOperation.status1 = 'Success'
t.logOperation.creatorName = 'Username'
t.logOperation.operation = 'User Action'
t.logOperation.requestUri = 'Request URI'
t.logOperation.requestMethod = 'Request Mode'
t.logOperation.requestParams = 'Request Parameters'
t.logOperation.requestTime = 'Request Duration'
t.logOperation.ip = 'IP'
t.logOperation.userAgent = 'User-Agent'
t.logOperation.createDate = 'Create Date'

t.menu = {}
t.menu.name = 'Name'
t.menu.icon = 'Icon'
t.menu.type = 'Type'
t.menu.type0 = 'Menu'
t.menu.type1 = 'Button'
t.menu.sort = 'Sort'
t.menu.url = 'Route'
t.menu.permissions = 'Auth ID'
t.menu.permissionsTips = 'eg: sys:menu:save'
t.menu.parentName = 'Superior'
t.menu.parentNameDefault = 'Top Menu'
t.menu.resource = 'Auth Resources'
t.menu.resourceUrl = 'Resource URL'
t.menu.resourceMethod = 'Request Method'
t.menu.resourceAddItem = 'Add an Item'

t.params = {}
t.params.paramCode = 'Code'
t.params.paramValue = 'Value'
t.params.remark = 'Remarks'

t.role = {}
t.role.name = 'Name'
t.role.remark = 'Remarks'
t.role.createDate = 'Create Date'
t.role.menuList = 'Menu Scope'
t.role.deptList = 'Data Scope'

t.user = {}
t.user.username = 'Username'
t.user.deptName = 'Department'
t.user.email = 'Email'
t.user.mobile = 'Mobile'
t.user.status = 'Status'
t.user.status0 = 'Disable'
t.user.status1 = 'Enable'
t.user.createDate = 'Create Date'
t.user.password = 'Password'
t.user.confirmPassword = 'Confirm'
t.user.realName = 'Real Name'
t.user.gender = 'Gender'
t.user.gender0 = 'Male'
t.user.gender1 = 'Female'
t.user.gender2 = 'Secure'
t.user.roleIdList = 'Role Config'
t.user.validate = {}
t.user.validate.confirmPassword = 'Confirm password is not consistent with password input'

t.sysSerialNumber = {}
t.sysSerialNumber.type = 'Type'
t.sysSerialNumber.productOrChannel = 'Product or channel'
t.sysSerialNumber.productOrChannelId = 'Product or channel Id'
t.sysSerialNumber.serialNumber = 'Serial number'
t.sysSerialNumber.version = 'Version'
t.sysSerialNumber.status = 'Status'

/* 渠道链路成本分析 BdChannelRouteCostAnalysis */
t.bdChannelRouteCostAnalysis = {}
t.bdChannelRouteCostAnalysis.id = 'ID'
t.bdChannelRouteCostAnalysis.logisticsProductId = '物流产品编号'
t.bdChannelRouteCostAnalysis.zone = '分区'
t.bdChannelRouteCostAnalysis.channelRouteId = '渠道链路编号'
t.bdChannelRouteCostAnalysis.weight = '重量'
t.bdChannelRouteCostAnalysis.cost = '成本费用'

/* 物流产品规则 BdLogicsticsProductRule */
t.bdLogicsticsProductRule = {}
t.bdLogicsticsProductRule.id = '物流产品编号'
t.bdLogicsticsProductRule.chargeableWeightRule = '结算重算法'
t.bdLogicsticsProductRule.bubbleRate = '分泡比例(%)'
t.bdLogicsticsProductRule.volDiv = '材积除'
t.bdLogicsticsProductRule.bubbleCountingFormula = '计泡条件'
t.bdLogicsticsProductRule.mismatchingBubbleCountingChargeableWeightRule = '不满足计泡条件结算重算法'
t.bdLogicsticsProductRule.carryRule = '大货界定值'
t.bdLogicsticsProductRule.bigCarry = '大货计量单位'
t.bdLogicsticsProductRule.smallCarry = '小货计量单位'
t.bdLogicsticsProductRule.multiWeightRule = '多件重量算法'
t.bdLogicsticsProductRule.multiWeightCarry = '多件重量进位'
t.bdLogicsticsProductRule.overrunRule = '超限费用算法'
t.bdLogicsticsProductRule.constraintRule = '约束规则'

/* 物流产品其他信息 BdLogisticsProductOther */
t.bdLogisticsProductOther = {}
t.bdLogisticsProductOther.id = '物流产品编号'
t.bdLogisticsProductOther.earlyWarningInventory = '预警库存'
t.bdLogisticsProductOther.earlyWarningContact = '预警联系人'
t.bdLogisticsProductOther.waybillNoGroupId = '号段组'
t.bdLogisticsProductOther.postcodeRegionCheck = '邮编地址库校验'
t.bdLogisticsProductOther.usStateShortCodeConversion = '美国州简码转换'
t.bdLogisticsProductOther.orderVerifyId = '订单校验模板'
t.bdLogisticsProductOther.returnAddrId = '回邮地址'
t.bdLogisticsProductOther.zoneTemplateId = '分区'
t.bdLogisticsProductOther.remoteZoneTemplateId = '偏远分区'
t.bdLogisticsProductOther.tenXTen = '10x10标签模版'
t.bdLogisticsProductOther.tenXFifteen = '10x15标签模版'
t.bdLogisticsProductOther.tenXEighteen = '10x18标签模版'
t.bdLogisticsProductOther.a4 = 'A4标签模版'
t.bdLogisticsProductOther.labelTemplateId = '标签模板'
t.bdLogisticsProductOther.handoverListId = '交接清单模版'
t.bdLogisticsProductOther.proformaInvoiceId = '形式发票模版'
t.bdLogisticsProductOther.customsDeclarationDocId = '配货单模板'
t.bdLogisticsProductOther.bagCardId = '袋牌模板'
t.bdLogisticsProductOther.returnDeliveryNo = '返回派送单号'
t.bdLogisticsProductOther.postalTrackingNoSource = '邮政单号作为运单号返回客户'

/* 产品渠道链路维护 BdLogisticsProductChannelRoute */
t.bdLogisticsProductChannelRoute = {}
t.bdLogisticsProductChannelRoute.id = 'ID'
t.bdLogisticsProductChannelRoute.logisticsProductId = '物流产品'
t.bdLogisticsProductChannelRoute.zoneType = '分区类型'
t.bdLogisticsProductChannelRoute.logisticsProductZone = '物流产品分区'
t.bdLogisticsProductChannelRoute.logisticsChannelLinkId = '物流渠道链路'

/* 物流产品授权 BdLogisticsProductAuthorization */
t.bdLogisticsProductAuthorization = {}
t.bdLogisticsProductAuthorization.id = 'ID'
t.bdLogisticsProductAuthorization.customerType = '客户类型'
t.bdLogisticsProductAuthorization.customerSource = '客户归属'
t.bdLogisticsProductAuthorization.logisticsProductId = '物流产品'
t.bdLogisticsProductAuthorization.objectId = '客户编号'
t.bdLogisticsProductAuthorization.objectName = '客户名称'

/* 物流产品 BdLogisticsProduct */
t.bdLogisticsProduct = {}
t.bdLogisticsProduct.id = 'ID'
t.bdLogisticsProduct.code = '产品代号'
t.bdLogisticsProduct.name = '产品名称'
t.bdLogisticsProduct.newName = '新产品名称'
t.bdLogisticsProduct.businessType = '业务类型'
t.bdLogisticsProduct.allowCustomerCancelOrder = '允许客户取消订单'
t.bdLogisticsProduct.logisticsProductType = '产品类型'
t.bdLogisticsProduct.departPortCity = '出发口岸城市'
t.bdLogisticsProduct.specialItem = '特殊物品类型'
t.bdLogisticsProduct.logisticsChannelLinksNode = '渠道链路节点'
t.bdLogisticsProduct.ownLogisticsChannelRoute = '一对一渠道链路'
t.bdLogisticsProduct.sendTime = '时效说明'
t.bdLogisticsProduct.sortNum = '排序'
t.bdLogisticsProduct.waybillNoSource = '运单号来源'
t.bdLogisticsProduct.lableType = '面单类型'

/* 客户面单规则 BdCustomerLables */
t.bdCustomerLables = {}
t.bdCustomerLables.id = 'ID'
t.bdCustomerLables.customerType = '客户类型'
t.bdCustomerLables.customerSource = '客户归属'
t.bdCustomerLables.objectId = '客户编号'
t.bdCustomerLables.objectName = '客户名称'
t.bdCustomerLables.logisticsProductId = '物流产品'
t.bdCustomerLables.labelTemplateId = '标签模版'
t.bdCustomerLables.handoverListId = '交接清单模版'
t.bdCustomerLables.proformaInvoiceId = '形式发票模版'
t.bdCustomerLables.customsDeclarationDocId = '配货单模板'

/* 干线运输渠道-海运明细 BdTrunkLineChannelOceanShipping */
t.bdTrunkLineChannelOceanShipping = {}
t.bdTrunkLineChannelOceanShipping.id = 'ID'
t.bdTrunkLineChannelOceanShipping.trunkLineId = '干线渠道编号'
t.bdTrunkLineChannelOceanShipping.departurePort = '起运港口'
t.bdTrunkLineChannelOceanShipping.transferPort = '中转港口'
t.bdTrunkLineChannelOceanShipping.arrivalPort = '抵达港口'
t.bdTrunkLineChannelOceanShipping.shippingRoutes = '海运航线'
t.bdTrunkLineChannelOceanShipping.shipCompany = '船公司'

/* 干线运输渠道-空运明细 BdTrunkLineChannelAir */
t.bdTrunkLineChannelAir = {}
t.bdTrunkLineChannelAir.id = 'ID'
t.bdTrunkLineChannelAir.trunkLineId = '干线渠道编号'
t.bdTrunkLineChannelAir.takeoffAirport = '起飞机场'
t.bdTrunkLineChannelAir.transferAirport = '中转机场'
t.bdTrunkLineChannelAir.landingAirport = '降落机场'
t.bdTrunkLineChannelAir.airRoutes = '航空航线'
t.bdTrunkLineChannelAir.airlineCompany = '航空公司'
t.bdTrunkLineChannelAir.isCharged = '是否带电'

/* 尾程派送渠道 BdLogisticsChannel */
t.bdLogisticsChannel = {}
t.bdLogisticsChannel.id = 'ID'
t.bdLogisticsChannel.code = '渠道代号'
t.bdLogisticsChannel.name = '渠道名称'
t.bdLogisticsChannel.logisticsChannelType = '物流渠道类型'
t.bdLogisticsChannel.providerId = '供应商'
t.bdLogisticsChannel.specialItem = '特殊物品类型'
t.bdLogisticsChannel.shippingAddressSource = '发货地址来源'
t.bdLogisticsChannel.withholdingFee = '下单预扣费'
t.bdLogisticsChannel.targetDispatchHandoverId = '尾程派送交接地'
t.bdLogisticsChannel.beginWeight = '起始重量'
t.bdLogisticsChannel.endWeight = '结束重量'
t.bdLogisticsChannel.weightUnit = '重量单位'
t.bdLogisticsChannel.lengthUnit = '长度单位'
t.bdLogisticsChannel.maxTime = '最长耗时(天)'
t.bdLogisticsChannel.minTime = '最短耗时(天)'
t.bdLogisticsChannel.sortNum = '排序'
t.bdLogisticsChannel.waybillNoSource = '运单号来源'
t.bdLogisticsChannel.trackPlatformType = '轨迹平台类型'
t.bdLogisticsChannel.carrierCode = '尾程服务商代码'
t.bdLogisticsChannel.apiTypeId = 'API接口类型'
t.bdLogisticsChannel.lableType = '面单类型'
t.bdLogisticsChannel.trackNoSource = '轨迹单号来源'
t.bdLogisticsChannel.waybillNo = '运单号'
t.bdLogisticsChannel.deliveryNo = '派送单号'
t.bdLogisticsChannel.postalTrackingNo = '邮政单号'
t.bdLogisticsChannel.providerNo = '供应商单号'
t.bdLogisticsChannel.customerOrderNo = '客户单号'

/* 尾程派送渠道规则 BdLogisticsChannelRule */
t.bdLogisticsChannelRule = {}
t.bdLogisticsChannelRule.id = '渠道编号'
t.bdLogisticsChannelRule.chargeableWeightRule = '结算重算法'
t.bdLogisticsChannelRule.volDiv = '材积除'
t.bdLogisticsChannelRule.bubbleCountingFormula = '计泡条件'
t.bdLogisticsChannelRule.carryFrontier = '大货界定值'
t.bdLogisticsChannelRule.bigCarry = '大货进位值'
t.bdLogisticsChannelRule.smallCarry = '小货进位值'
t.bdLogisticsChannelRule.multiWeightRule = '多件重量算法'
t.bdLogisticsChannelRule.overrunRule = '超限费用算法'

/* 物流渠道API类型 BdLogisticsChannelApiType */
t.bdLogisticsChannelApiType = {}
t.bdLogisticsChannelApiType.id = 'ID'
t.bdLogisticsChannelApiType.name = '渠道类型名称'
t.bdLogisticsChannelApiType.apiValue = '渠道类型值'
t.bdLogisticsChannelApiType.apiType = '实现接口'
t.bdLogisticsChannelApiType.requestContent = '包含内容'
t.bdLogisticsChannelApiType.notInRequestContent = '不包含内容'
t.bdLogisticsChannelApiType.remark = '描述'

/* 物流渠道API类型明细 BdLogisticsChannelApiTypeDetail */
t.bdLogisticsChannelApiTypeDetail = {}
t.bdLogisticsChannelApiTypeDetail.id = 'ID'
t.bdLogisticsChannelApiTypeDetail.apiTypeId = '渠道API类型编号'
t.bdLogisticsChannelApiTypeDetail.paramKey = '参数名'
t.bdLogisticsChannelApiTypeDetail.paramValue = '参数值'
t.bdLogisticsChannelApiTypeDetail.remark = '备注'
t.bdLogisticsChannelApiTypeDetail.viewLog = '查看示例报文'

/* 境外清关渠道 BdTargetCustomsChannel */
t.bdTargetCustomsChannel = {}
t.bdTargetCustomsChannel.id = 'ID'
t.bdTargetCustomsChannel.code = '渠道代号'
t.bdTargetCustomsChannel.name = '渠道名称'
t.bdTargetCustomsChannel.country = '国家'
t.bdTargetCustomsChannel.providerId = '供应商'
t.bdTargetCustomsChannel.customsPort = '清关口岸'
t.bdTargetCustomsChannel.airport = '落地机场'
t.bdTargetCustomsChannel.customsMode = '清关模式'
t.bdTargetCustomsChannel.maxTime = '最长耗时(天)'
t.bdTargetCustomsChannel.minTime = '最短耗时(天)'
t.bdTargetCustomsChannel.sortNum = '排序'
t.bdTargetCustomsChannel.addressSource = '发货地址来源'
t.bdTargetCustomsChannel.apiType = 'API接口类型'
t.bdTargetCustomsChannel.apiTypeId = 'API接口类型'

/* 境内清关渠道api参数 BdSourceCustomsChannelApi */
t.bdSourceCustomsChannelApi = {}
t.bdSourceCustomsChannelApi.id = 'ID'
t.bdSourceCustomsChannelApi.sourceCustomsId = '境内清关编号'
t.bdSourceCustomsChannelApi.paramKey = '参数名'
t.bdSourceCustomsChannelApi.paramValue = '参数值'
t.bdSourceCustomsChannelApi.remark = '备注'

/* 境内清关渠道 BdSourceCustomsChannel */
t.bdSourceCustomsChannel = {}
t.bdSourceCustomsChannel.id = 'ID'
t.bdSourceCustomsChannel.code = '渠道代号'
t.bdSourceCustomsChannel.name = '渠道名称'
t.bdSourceCustomsChannel.country = '国家'
t.bdSourceCustomsChannel.providerId = '供应商'
t.bdSourceCustomsChannel.customsPort = '清关口岸'
t.bdSourceCustomsChannel.airport = '起运机场'
t.bdSourceCustomsChannel.customsMode = '清关模式'
t.bdSourceCustomsChannel.tariffDeliveryMode = '关税交付方式'
t.bdSourceCustomsChannel.maxTime = '最长耗时(天)'
t.bdSourceCustomsChannel.minTime = '最短耗时(天)'
t.bdSourceCustomsChannel.sortNum = '排序'
t.bdSourceCustomsChannel.addressSource = '发货地址来源'
t.bdSourceCustomsChannel.apiType = 'API接口类型'
t.bdSourceCustomsChannel.apiTypeId = 'API接口类型'

/* 干线运输渠道api参数 BdTrunkLineChannelApi */
t.bdTrunkLineChannelApi = {}
t.bdTrunkLineChannelApi.id = 'ID'
t.bdTrunkLineChannelApi.trunkLineId = '干线运输编号'
t.bdTrunkLineChannelApi.paramKey = '参数名'
t.bdTrunkLineChannelApi.paramValue = '参数值'
t.bdTrunkLineChannelApi.remark = '备注'

/* 干线运输渠道 BdTrunkLineChannel */
t.bdTrunkLineChannel = {}
t.bdTrunkLineChannel.id = 'ID'
t.bdTrunkLineChannel.code = '渠道代号'
t.bdTrunkLineChannel.name = '渠道名称'
t.bdTrunkLineChannel.trunkTransportWay = '运输方式'
t.bdTrunkLineChannel.providerId = '供应商'
t.bdTrunkLineChannel.deliverCountry = '起运地'
t.bdTrunkLineChannel.arrivalCountry = '目的地'
t.bdTrunkLineChannel.maxTime = '最长耗时(天)'
t.bdTrunkLineChannel.minTime = '最短耗时(天)'
t.bdTrunkLineChannel.sortNum = '排序'
t.bdTrunkLineChannel.addressSource = '发货地址来源'
t.bdTrunkLineChannel.apiType = 'API接口类型'
t.bdTrunkLineChannel.apiTypeId = 'API接口类型名称'

/* 物流渠道链路 BdLogisticsChannelRoute */
t.bdLogisticsChannelRoute = {}
t.bdLogisticsChannelRoute.id = 'ID'
t.bdLogisticsChannelRoute.code = '渠道链路代号'
t.bdLogisticsChannelRoute.name = '渠道链路名称'
t.bdLogisticsChannelRoute.sourceCustomsId = '境内清关渠道'
t.bdLogisticsChannelRoute.trunkLineId = '干线运输渠道'
t.bdLogisticsChannelRoute.targetCustomsId = '境外清关渠道'
t.bdLogisticsChannelRoute.targetHandoverId = '尾程交接渠道'
t.bdLogisticsChannelRoute.logisticsChannelId = '尾程派送渠道'
t.bdLogisticsChannelRoute.collectPlaceId = '揽收地'
t.bdLogisticsChannelRoute.handoverPlaceId = '交接地'
t.bdLogisticsChannelRoute.maxTime = '最长耗时(天)'
t.bdLogisticsChannelRoute.minTime = '最短耗时(天)'
t.bdLogisticsChannelRoute.sortNum = '排序'
t.bdLogisticsChannelRoute.remark = '备注'

/* 尾程派送渠道邮编映射 BdLogisticsChannelPostcode */
t.bdLogisticsChannelPostcode = {}
t.bdLogisticsChannelPostcode.id = 'ID'
t.bdLogisticsChannelPostcode.logisticsChannelRouteId = '物流渠道链路编号'
t.bdLogisticsChannelPostcode.whetherInclude = '是否包括'
t.bdLogisticsChannelPostcode.postcode = '邮编'
t.bdLogisticsChannelPostcode.startPostcode = '起始邮编'
t.bdLogisticsChannelPostcode.endPostcode = '结束邮编'

/* 尾程派送渠道其他信息表 BdLogisticsChannelOther */
t.bdLogisticsChannelOther = {}
t.bdLogisticsChannelOther.id = '渠道编号'
t.bdLogisticsChannelOther.orderVerifyId = '订单校验'
t.bdLogisticsChannelOther.returnAddrId = '回邮地址'
t.bdLogisticsChannelOther.zoneTemplateId = '分区'
t.bdLogisticsChannelOther.remoteZoneTemplateId = '偏远分区'
t.bdLogisticsChannelOther.labelTemplateId = '标签模板'
t.bdLogisticsChannelOther.tenXTen = '10x10标签模版'
t.bdLogisticsChannelOther.tenXFifteen = '10x15标签模版'
t.bdLogisticsChannelOther.tenXEighteen = '10x18标签模版'
t.bdLogisticsChannelOther.a4 = 'A4标签模版'
t.bdLogisticsChannelOther.handoverListId = '交接清单模版'
t.bdLogisticsChannelOther.proformaInvoiceId = '形式发票模版'
t.bdLogisticsChannelOther.customsDeclarationDocId = '配货单模板'

/* 中转交接渠道api参数 BdTransitHandoverChannelApi */
t.bdTransitHandoverChannelApi = {}
t.bdTransitHandoverChannelApi.id = 'ID'
t.bdTransitHandoverChannelApi.transitHandoverChannelId = '中转交接渠道编号'
t.bdTransitHandoverChannelApi.paramKey = '参数名'
t.bdTransitHandoverChannelApi.fieldValue = '参数值'
t.bdTransitHandoverChannelApi.remark = '备注'

/* 尾程派送api参数 BdLogisticsChannelApi */
t.bdLogisticsChannelApi = {}
t.bdLogisticsChannelApi.id = 'ID'
t.bdLogisticsChannelApi.logisticsChannelId = '尾程派送渠道编号'
t.bdLogisticsChannelApi.paramKey = '参数名'
t.bdLogisticsChannelApi.paramValue = '参数值'
t.bdLogisticsChannelApi.remark = '备注'

/* 尾程交接渠道api参数 BdTargetHandoverChannelApi */
t.bdTargetHandoverChannelApi = {}
t.bdTargetHandoverChannelApi.id = 'ID'
t.bdTargetHandoverChannelApi.targetHandoverId = '尾程交接渠道编号'
t.bdTargetHandoverChannelApi.fieldKey = '参数名'
t.bdTargetHandoverChannelApi.fieldValue = '参数值'
t.bdTargetHandoverChannelApi.remark = '备注'

/* 尾程交接渠道 BdTargetHandoverChannel */
t.bdTargetHandoverChannel = {}
t.bdTargetHandoverChannel.id = 'ID'
t.bdTargetHandoverChannel.code = '渠道代号'
t.bdTargetHandoverChannel.name = '渠道名称'
t.bdTargetHandoverChannel.transportWay = '运输方式'
t.bdTargetHandoverChannel.providerId = '供应商'
t.bdTargetHandoverChannel.country = '所在国家'
t.bdTargetHandoverChannel.sourceAddr = '起运地'
t.bdTargetHandoverChannel.targetAddr = '目的地'
t.bdTargetHandoverChannel.maxTime = '最长耗时(天)'
t.bdTargetHandoverChannel.minTime = '最短耗时(天)'
t.bdTargetHandoverChannel.sortNum = '排序'
t.bdTargetHandoverChannel.apiType = 'API接口类型'
t.bdTargetHandoverChannel.apiTypeId = 'API接口名称'

/* 境外清关渠道api参数 BdTargetCustomsChannelApi */
t.bdTargetCustomsChannelApi = {}
t.bdTargetCustomsChannelApi.id = 'ID'
t.bdTargetCustomsChannelApi.targetCustomerId = '境外清关渠道编号'
t.bdTargetCustomsChannelApi.paramKey = '参数名'
t.bdTargetCustomsChannelApi.paramValue = '参数值'
t.bdTargetCustomsChannelApi.remark = '备注'

/* 中转交接渠道表 BdTransitHandoverChannel */
t.bdTransitHandoverChannel = {}
t.bdTransitHandoverChannel.id = 'ID'
t.bdTransitHandoverChannel.code = '渠道代号'
t.bdTransitHandoverChannel.name = '渠道名称'
t.bdTransitHandoverChannel.transportWay = '运输方式'
t.bdTransitHandoverChannel.providerId = '供应商'
t.bdTransitHandoverChannel.sourceAddr = '起运地'
t.bdTransitHandoverChannel.targetAddr = '目的地'
t.bdTransitHandoverChannel.vehicleType = '车型'
t.bdTransitHandoverChannel.transportMode = '运输模式'
t.bdTransitHandoverChannel.maxTime = '最长耗时(天)'
t.bdTransitHandoverChannel.minTime = '最短耗时(天)'
t.bdTransitHandoverChannel.sortNum = '排序'
t.bdTransitHandoverChannel.apiType = 'API接口类型'
t.bdTransitHandoverChannel.apiTypeId = 'API接口类型'

/* 交接地 BdHandoverPlace */
t.bdHandoverPlace = {}
t.bdHandoverPlace.id = 'ID'
t.bdHandoverPlace.code = '代号'
t.bdHandoverPlace.name = '名称'
t.bdHandoverPlace.country = '国家'
t.bdHandoverPlace.province = '省/州'
t.bdHandoverPlace.city = '市'
t.bdHandoverPlace.district = '区/县'
t.bdHandoverPlace.address = '详细地址'

/* 订单校验明细 BdOrderVerificationDetail */
t.bdOrderVerificationDetail = {}
t.bdOrderVerificationDetail.id = 'ID'
t.bdOrderVerificationDetail.orderVerificationTempateId = '订单校验模板编号'
t.bdOrderVerificationDetail.type = '类型'
t.bdOrderVerificationDetail.fieldKey = '字段属性'
t.bdOrderVerificationDetail.fieldName = '字段属性名称'
t.bdOrderVerificationDetail.fieldType = '字段类型'
t.bdOrderVerificationDetail.required = '是否必填'
t.bdOrderVerificationDetail.minLength = '最小长度'
t.bdOrderVerificationDetail.maxLength = '最大长度'
t.bdOrderVerificationDetail.minValue = '最小值'
t.bdOrderVerificationDetail.maxValue = '最大值'
t.bdOrderVerificationDetail.includeCn = '是否包含中文'
t.bdOrderVerificationDetail.blacklistType = '黑名单模板类型'
t.bdOrderVerificationDetail.blacklistTemplateId = '黑名单模板'
t.bdOrderVerificationDetail.regularExpression = '正则表达式'
t.bdOrderVerificationDetail.regularExpressionTip = '正则表达式校验提示'
t.bdOrderVerificationDetail.remark = '备注'

/* 运单截取规则 BdCutRule */
t.bdCutRule = {}
t.bdCutRule.id = 'ID'
t.bdCutRule.cutRule = '截取规则'
t.bdCutRule.cutBeginPosition = '截取开始位置'
t.bdCutRule.cutLength = '截取长度'
t.bdCutRule.remark = '备注'

/* 分区模板 BdZoneTemplate */
t.bdZoneTemplate = {}
t.bdZoneTemplate.id = 'ID'
t.bdZoneTemplate.code = '代号'
t.bdZoneTemplate.name = '名称'
t.bdZoneTemplate.tag = '标签'
t.bdZoneTemplate.type = '分区类型'
t.bdZoneTemplate.specialType = '特殊类型'
t.bdZoneTemplate.qty = '分区数量'

/* 分区模板明细 bdZoneTemplateDetail */
t.bdZoneTemplateDetail = {}
t.bdZoneTemplateDetail.id = 'ID'
t.bdZoneTemplateDetail.zoneTemplateId = '分区模板编号'
t.bdZoneTemplateDetail.zone = '分区'
t.bdZoneTemplateDetail.country = '国家'
t.bdZoneTemplateDetail.city = '城市'
t.bdZoneTemplateDetail.beginPostcode = '起始邮编'
t.bdZoneTemplateDetail.endPostcode = '结束邮编'

/* 订单校验模板 BdOrderVerificationTemplate */
t.bdOrderVerificationTemplate = {}
t.bdOrderVerificationTemplate.id = 'ID'
t.bdOrderVerificationTemplate.code = '代号'
t.bdOrderVerificationTemplate.name = '名称'

/* 邮编截取规则 BdPostcodeRole */
t.bdPostcodeRole = {}
t.bdPostcodeRole.id = 'ID'
t.bdPostcodeRole.country = '国家'
t.bdPostcodeRole.cutRole = '截取规则'
t.bdPostcodeRole.memo = '备注'

/* 编码规则 SysNumberRule */
t.sysNumberRule = {}
t.sysNumberRule.id = 'ID'
t.sysNumberRule.name = '名称'
t.sysNumberRule.codeType = '编码类型'
t.sysNumberRule.prefixCode = '编码前缀'
t.sysNumberRule.includedDate = '包含日期'
t.sysNumberRule.includedCheckBit = '包含校验位'
t.sysNumberRule.dateFormat = '日期格式'
t.sysNumberRule.serialNumLen = '流水号长度'
t.sysNumberRule.maxUse = '已用流水号'
t.sysNumberRule.description = '描述'

/* 回邮地址 BdReturnMailingAddress */
t.bdReturnMailingAddress = {}
t.bdReturnMailingAddress.id = 'ID'
t.bdReturnMailingAddress.tag = '标签'
t.bdReturnMailingAddress.contact = '联系人'
t.bdReturnMailingAddress.shippingCompany = '发货公司'
t.bdReturnMailingAddress.phone = '手机号'
t.bdReturnMailingAddress.mailbox = '邮箱'
t.bdReturnMailingAddress.country = '国家'
t.bdReturnMailingAddress.province = '省/州'
t.bdReturnMailingAddress.city = '市'
t.bdReturnMailingAddress.district = '区/县'
t.bdReturnMailingAddress.street = '街道'
t.bdReturnMailingAddress.address = '详细地址'
t.bdReturnMailingAddress.houseNumber = '门牌号'
t.bdReturnMailingAddress.postcode = '邮编'
t.bdReturnMailingAddress.defaultFlag = '是否默认'

/* 坏账异常 BaBadDebtAbnormal */
t.baBadDebtAbnormal = {}
t.baBadDebtAbnormal.id = 'ID'
t.baBadDebtAbnormal.receiveableSettlementId = '应收结算单Id'
t.baBadDebtAbnormal.endDate = '帐期结束日期'
t.baBadDebtAbnormal.settlementObjectType = '应收结算对象类型'
t.baBadDebtAbnormal.settlementObjectId = '应收结算对象编码'
t.baBadDebtAbnormal.settlementObejctName = '应收结算对象名称'
t.baBadDebtAbnormal.badAmount = '坏账金额'
t.baBadDebtAbnormal.currency = '币种'
t.baBadDebtAbnormal.remark = '备注'
t.baBadDebtAbnormal.auditor = '审核人'
t.baBadDebtAbnormal.auditDate = '审核时间'

/* 物流产品渠道分拣码 BdLogisticsProductChannelSortCode */
t.bdLogisticsProductChannelSortCode = {}
t.bdLogisticsProductChannelSortCode.id = 'ID'
t.bdLogisticsProductChannelSortCode.setSortCode = '分拣码设置'
t.bdLogisticsProductChannelSortCode.objectType = '对象类型'
t.bdLogisticsProductChannelSortCode.objectId = '对象编号'
t.bdLogisticsProductChannelSortCode.zone = '分区号'
t.bdLogisticsProductChannelSortCode.labelTemplateId = '标签模板'
t.bdLogisticsProductChannelSortCode.sortCode = '分拣码'
t.bdLogisticsProductChannelSortCode.coordinateXAxis = '坐标x轴'
t.bdLogisticsProductChannelSortCode.coordinateYAxis = '坐标y轴'

/* 物流渠道类型 BdLogisticsChannelType */
t.bdLogisticsChannelType = {}
t.bdLogisticsChannelType.id = 'ID'
t.bdLogisticsChannelType.code = '代号'
t.bdLogisticsChannelType.name = '名称'
t.bdLogisticsChannelType.logisticsType = '物流类型'

/* 地区 BdRegion */
t.bdRegion = {}
t.bdRegion.level = '级别'
t.bdRegion.code = '代码'
t.bdRegion.name = '名称'
t.bdRegion.enName = '英文名称'
t.bdRegion.postcode = '邮编'
t.bdRegion.path = '级联路径'
t.bdRegion.parentCode = '父级代码'
t.bdRegion.cname = '常用名称'

/* 订单默认值表 BdOrderDefaultValue */
t.bdOrderDefaultValue = {}
t.bdOrderDefaultValue.id = 'ID'
t.bdOrderDefaultValue.logisticsChannelId = '尾程派送渠道'
t.bdOrderDefaultValue.fieldKey = '字段属性'
t.bdOrderDefaultValue.fieldName = '字段属性名称'
t.bdOrderDefaultValue.fieldType = '字段类型'
t.bdOrderDefaultValue.defaultValue = '默认值'

/* 消息通知内容表 MsNotice */
t.msNotice = {}
t.msNotice.id = 'ID'
t.msNotice.title = '标题'
t.msNotice.type = '类型'
t.msNotice.receiverType = '通知接收者类型'
t.msNotice.publishDate = '发布时间'
t.msNotice.content = '内容'
t.msNotice.creatorName = '发布者'

/* 消息通知-接收者 MsNoticeReceiver */
t.msNoticeReceiver = {}
t.msNoticeReceiver.id = 'ID'
t.msNoticeReceiver.noticeId = '通知ID'
t.msNoticeReceiver.recevierId = '接收者ID，（公司员工账号ID或客户账号ID)'

/* 轨迹替换 TksTrackReplace */
t.tksTrackReplace = {}
t.tksTrackReplace.id = 'ID'
t.tksTrackReplace.logisticsChannelCode = '尾程派送渠道'
t.tksTrackReplace.matchType = '匹配类型'
t.tksTrackReplace.doType = '处理方式'
t.tksTrackReplace.sourceValue = '原轨迹描述或发生地'
t.tksTrackReplace.targetValue = '新轨迹描述或发生地'

export default t
