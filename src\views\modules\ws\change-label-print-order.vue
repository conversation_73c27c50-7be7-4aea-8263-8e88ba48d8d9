<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="120px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :span="6">
                  <three-no-input ref="threeNoInput" @setNoSize="setNoSize"
                                  :customerOrderNo.sync="dataForm.customerOrderNos"
                                  :waybillNo.sync="dataForm.waybillNos"
                                  :deliveryNo.sync="dataForm.deliveryNos"
                                  :postalTrackingNo.sync="dataForm.postalTrackingNos"
                                  :packageCustomerNos.sync="dataForm.packageCustomerNos"
                                  :subDeliveryNos.sync="dataForm.subDeliveryNos"
                                  :outBatchNos.sync="dataForm.outBatchNos"
                                  :hideItem="[4,5,6,7, 8, 9, 10, 11,12,13,14]"
                                  :autosize="threeNoInputAutoSize" :noSize="5000"/>
                  <div>
                <span v-if="noSize > 0" style="padding-left: 5px;padding-top: 5px;">
                  搜索( <span style="color: #0ea55d">{{noSize}}</span> )条
                </span>
                    <span v-if="notInScopeNoList.length > 0" style="padding-left: 5px;padding-top: 5px;">
                  <el-button type="text" style="margin-left: 5px;font-size: 12px;color: #ebb563" icon="el-icon-warning" @click="showNotInScopeNosHandle">未匹配单号(<span style="color: #8a979e">{{notInScopeNoList.length}}</span>)条(点击查看)</el-button>
                </span>
                  </div>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsChangeLabelPrintOrder.customerId')" prop="customerId">
                    <el-select v-model="dataForm.customerId" clearable
                               :placeholder="$t('twoCharToSelectForCustomer')"
                               :loading="loading" filterable remote reserve-keyword
                               :remote-method="getCustomerByCodeOrName">
                      <el-option v-for="item in customerList" :key="item.code" :label="item.name"
                                 :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsChangeLabelPrintOrder.logisticsProductCode')" prop="logisticsProductCode">
                    <el-select v-model="dataForm.logisticsProductCode" filterable clearable>
                      <el-option v-for="item in logisticsProductByParamsList" :key="item.code"
                                 :label="item.name" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsChangeLabelPrintOrder.hasLabel')" prop="hasLabel">
                    <el-select v-model="dataForm.hasLabel" clearable filterable
                               :placeholder="$t('wsChangeLabelPrintOrder.hasLabel')">
                      <el-option v-for="item in yesOrNoList" :key="item.dictValue"
                                 :label="item.dictName"
                                 :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsChangeLabelPrintOrder.logisticsChannelCode')" prop="logisticsChannelCode">
                    <el-select v-model="dataForm.logisticsChannelCode" filterable clearable>
                      <el-option v-for="item in logisticsChannelByParamsList" :key="item.code"
                                 :label="item.name" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsChangeLabelPrintOrder.printTimes')" prop="printTimes">
                    <el-input v-model="dataForm.printTimes" :placeholder="$t('wsChangeLabelPrintOrder.printTimes')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsComWaybill.createDate')" prop="createDateArray">
                    <el-date-picker v-model="createDateArray" value-format="yyyy-MM-dd HH:mm:ss"
                                    type="datetimerange" :start-placeholder="$t('startTime')"
                                    :default-time="['00:00:00', '23:59:59']"
                                    :end-placeholder="$t('endTime')" style="width: 100%"></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetHandle" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12">
            <el-button size="mini" type="primary" plain @click="batchPrintOrder()">{{ $t('batchPrint') }}</el-button>
            <el-button size="mini" type="primary" plain @click="batchDownloadLabelHandle()">{{ $t('batchDownloadLabel') }}</el-button>

            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini" type="primary" icon="el-icon-upload2" v-if="$hasPermission('ws:comwaybill:export')" plain @click="importPdf()">导入面单</el-button>
            <el-button size="mini" type="primary" icon="el-icon-download"  v-if="dataList.length > 0" plain @click="exportHandle()">导出查询结果</el-button>
            <el-button size="mini" type="primary" plain  @click="addByWaybillNoHandle()">导单生成Y2换标</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <el-table-column type="expand" fixed="left">
              <template slot-scope="scope">
                <el-form label-position="right" inline class="index-table-expand">
                  <el-form-item :label="$t('system.createDate')"><span>{{ scope.row.createDate | gtmToLtm }}</span></el-form-item>
                  <el-form-item :label="$t('system.updateDate')"><span>{{ scope.row.updateDate | gtmToLtm }}</span></el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100, 500, 1000]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <el-dialog title="导入生成Y2换标单" :visible.sync="dialogVisible"
               :close-on-click-modal="false" :close-on-press-escape="false"  :lock-scroll="true" top="30px" width="800px" height="800px">
      <el-form :model="submitDataForm" ref="submitDataForm" label-width="108px">
        <el-row>
          <div style="padding-bottom: 15px;padding-left: 10px;">按运单号批量查询已预报订单，若该订单没有Y2换标单，生成Y2换标单</div>
        </el-row>
        <el-row :gutter="20" type="flex">
          <el-col :span="24">
            <el-form-item  :label="$t('wsComWaybill.waybillNo')" prop="waybillNos">
              <el-input type="textarea" v-model="submitDataForm.waybillNos" clearable :rows="20"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button @click="cancelFn">{{this.$t('back')}}</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveOrder">{{ $t('confirm') }}</el-button>
    </span>
    </el-dialog>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <label-upload-dialog ref="labelUploadDialog" :uploadFileUrl="uploadFileUrl" :excelSizeLimit="300" @successHandle="successHandle"></label-upload-dialog>
    <labelDownload v-if="labelDownloadVisible" ref="labelDownload" @backView="backView"/>
    <not-in-scope-no v-if="notInScopeNosVisible" ref="notInScopeNos" @backView="notInScopeNosVisible=false"></not-in-scope-no>
    <exportDetail v-if="exportVisible" ref="exportDetail" @backView="backView"/>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterCodeNativeName, formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import ViewDetail from './change-label-print-order-view-detail'
import LabelUploadDialog from './label-upload-dialog'
import threeNoInput from '@/components/multiple-no-input.vue'
import baseData from '@/api/baseData'
import baseDataApi from '@/api'
import comMixins from '@/mixins/comMixins'
import LabelDownload from './label-download-dialog'
import { printFn } from '@/utils/print'
import notInScopeNo from '@/components/not-in-scope-no-detail.vue'
import ExportDetail from '@/views/modules/bd/excel-export-dialog.vue'
import { getBeforeDay, getNowDate } from '@/utils/tools'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '100', prop: 'customerName', label: this.$t('wsChangeLabelPrintOrder.customerName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '180', prop: 'customerOrderNo', label: this.$t('wsChangeLabelPrintOrder.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '180', prop: 'deliveryNo', label: this.$t('wsChangeLabelPrintOrder.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('wsChangeLabelPrintOrder.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsProductCode', label: this.$t('wsChangeLabelPrintOrder.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsChannelCode', label: this.$t('wsChangeLabelPrintOrder.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'pushOrderToProviderStatus', label: this.$t('wsChangeLabelPrintOrder.pushOrderToProviderStatus'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '100', prop: 'pushOrderToProviderErrorMessage', label: this.$t('wsChangeLabelPrintOrder.pushOrderToProviderErrorMessage'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '100', prop: 'deliveryNoSource', label: this.$t('wsChangeLabelPrintOrder.deliveryNoSource'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '80', prop: 'hasLabel', label: this.$t('wsChangeLabelPrintOrder.hasLabel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'printTimes', label: this.$t('wsChangeLabelPrintOrder.printTimes'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'createDate', label: this.$t('system.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '550', prop: 'channelLabelUrl', label: this.$t('wsChangeLabelPrintOrder.channelLabelUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ws/changelabelprintorder/page',
        getDataListIsPage: true,
        getDataListURLOfRequestType: 'post',
        deleteURL: '/ws/changelabelprintorder',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        logisticsProductCode: '',
        logisticsChannelCode: '',
        deliveryNoSource: '',
        customerOrderNos: '',
        waybillNos: '',
        postalTrackingNos: '',
        deliveryNos: '',
        printTimes: null,
        createDateFrom: '',
        createDateTo: ''
      },
      submitDataForm: {
        waybillNos: ''
      },
      labelDownloadVisible: false,
      notInScopeNosVisible: false,
      exportVisible: false,
      loading: false,
      dialogVisible: false,
      saveLoading: false,
      createDateArray: [getBeforeDay(7), getNowDate()],
      notInScopeNoList: [],
      yesOrNoList: [],
      deliveryNoSourceList: [],
      customerList: [],
      logisticsProductByParamsList: [],
      logisticsChannelByParamsList: [],
      uploadFileUrl: '/ws/changelabelprintorder/uploadLabel',
      activeName: 'all',
      tableName: 'ws-changelabelprintorder'
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    async getDict () {
      this.deliveryNoSourceList = await this.getDictTypeList('changeLabelDeliveryNoSource')
      // 是否
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
    },
    async getBaseData () {
      this.logisticsProductByParamsList = await baseData(baseDataApi.listAllByCurrent).catch(() => {})
      this.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByParamsList, { autoFilter: true }).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'hasLabel':
          value = formatterType(scope.row.hasLabel, this.yesOrNoList)
          break
        case 'deliveryNoSource':
          value = formatterType(scope.row.deliveryNoSource, this.deliveryNoSourceList)
          break
        case 'logisticsProductCode':
          value = formatterCodeNativeName(scope.row.logisticsProductCode, this.logisticsProductByParamsList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeNativeName(scope.row.logisticsChannelCode, this.logisticsChannelByParamsList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    importPdf () {
      this.uploadVisible = true
      this.$refs.labelUploadDialog.init()
    },
    successHandle (res) {
      this.$message.success({
        message: res.data,
        duration: 5000,
        dangerouslyUseHTMLString: true
      })
      this.$refs.labelUploadDialog.cancelFn()
      this.searchHandle()
    },
    // 查看未匹配单号
    showNotInScopeNosHandle () {
      this.notInScopeNosVisible = true
      this.$nextTick(() => {
        this.$refs.notInScopeNos.noList = this.notInScopeNoList
        this.$refs.notInScopeNos.init()
      })
    },
    findNotInScopeNos () {
      this.$http.post('/ws/changelabelprintorder/findNoInScopeNos', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.notInScopeNoList = res.data || []
      }).catch(() => {})
    },
    // 导出
    exportHandle (id) {
      this.exportVisible = true
      this.$nextTick(() => {
        let masterDTOName = 'WsChangeLabelPrintOrderDTO'
        this.$refs.exportDetail.dataForm.masterDTOName = masterDTOName
        if (id) {
          this.$refs.exportDetail.queryDataForm.id = id
        } else {
          this.$refs.exportDetail.queryDataForm = this.dataForm
        }
        this.$refs.exportDetail.init()
      })
    },
    // 预报/所有订单 批量打印
    batchPrintOrder () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      let orderIdArr = this.dataListSelections.map(item => item.orderId)
      printFn('/co/order/channel/label?orderByMode=11&nos=' + orderIdArr)
    },
    // 预报/所有订单 批量打印
    batchDownloadLabelHandle () {
      if (!this.dataListSelections.length) {
        return this.$message.warning(this.$t('select'))
      }
      this.labelDownloadVisible = true
      this.$nextTick(() => {
        this.$refs.labelDownload.dataList = this.dataListSelections
        this.$refs.labelDownload.init()
      })
    },
    // 查询前预提交单号到后台
    searchHandle (page) {
      if (page) {
        this.page = page
      }
      let initFlag = this.$refs.threeNoInput.setValue()
      if (!initFlag) {
        return
      }
      // 查询
      this.getDataList()
      // 单号不匹配
      this.findNotInScopeNos()
    },
    resetHandle () {
      this.createDateArray = [getBeforeDay(7), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    },
    cancelFn () {
      this.$refs.submitDataForm.resetFields()
      this.dialogVisible = false
    },
    addByWaybillNoHandle () {
      this.dialogVisible = true
    },
    saveOrder () {
      this.saveLoading = true
      this.submitDataForm.waybillNos = this.submitDataForm.waybillNos.replace(/\n|\s+/g, ',').trim()
      this.$http.post('/ws/changelabelprintorder/saveByWaybillNo', this.submitDataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        } else {
          this.$refs.submitDataForm.resetFields()
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
      }).catch(() => {
      }).finally(() => { this.saveLoading = false })
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 3, maxRows: 6 }
      } else {
        return { minRows: 6, maxRows: 6 }
      }
    },
    getUploadFileUrl () {
      return `${this.$baseUrl}` + `${this.uploadFileUrl}`
    }
  },
  watch: {
    createDateArray: {
      handler (value, oldName) {
        if (value !== undefined && value !== '' && value !== null) {
          this.dataForm.createDateFrom = timestampFormat(value[0])
          this.dataForm.createDateTo = timestampFormat(value[1])
        } else {
          this.createDateArray = [getBeforeDay(7), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  components: {
    ExportDetail,
    notInScopeNo,
    threeNoInput,
    LabelUploadDialog,
    ViewDetail,
    tableSet,
    LabelDownload
  }
}
</script>
