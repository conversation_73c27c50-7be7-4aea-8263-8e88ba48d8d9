<template>
  <el-dialog :visible.sync="visible"  :title="$t('wsComWaybill.modifyDeclareInfoBatch')"  width="90%"  top="10px" :append-to-body="false" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true" class="location_model">
<!--    <div class=" flex_wrap" >-->
      <!-- 批量编辑表单 -->
      <el-form :model='batchForm' style="margin-top: 10px" ref="batchForm" :inline-message='true' label-width="150px">
        <el-card shadow="never" style="margin-bottom: 20px;">
          <div slot="header" class="clearfix">
            <span style="color: #909399; font-size: 12px; margin-left: 10px;">填写需要批量修改的字段，留空的字段将不会被修改，勾选置空将清空该字段</span>
          </div>

          <el-row :gutter="20">
            <!-- 中文名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.chineseName')" :prop="'chineseName'" :rules="declareRule['chineseName']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.chineseName"
                    :placeholder="$t('coOrderDeclare.chineseName')"
                    :disabled="batchForm.chineseNameClear">
                  </el-input>
                  <!-- 不允许置空 -->
                  <!-- <el-checkbox
                    v-model="batchForm.chineseNameClear"
                    class="clear-checkbox"
                    @change="handleClearChange('chineseName', $event)">
                    是否置空
                  </el-checkbox> -->
                </div>
              </el-form-item>
            </el-col>

            <!-- 英文名称 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.englishName')" :prop="'englishName'" :rules="declareRule['englishName']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.englishName"
                    :placeholder="$t('coOrderDeclare.englishName')"
                    :disabled="batchForm.englishNameClear">
                  </el-input>
                  <!-- 不允许置空 -->
                  <!-- <el-checkbox
                    v-model="batchForm.englishNameClear"
                    class="clear-checkbox"
                    @change="handleClearChange('englishName', $event)">
                    是否置空
                  </el-checkbox> -->
                </div>
              </el-form-item>
            </el-col>

            <!-- 数量 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.quantity')" :prop="'quantity'" :rules="declareRule['quantity']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.quantity"
                    :placeholder="$t('coOrderDeclare.quantity')"
                    :disabled="batchForm.quantityClear">
                  </el-input>
                  <!-- 不允许置空 -->
                  <!-- <el-checkbox
                    v-model="batchForm.quantityClear"
                    class="clear-checkbox"
                    @change="handleClearChange('quantity', $event)">
                    是否置空
                  </el-checkbox> -->
                </div>
              </el-form-item>
            </el-col>

            <!-- 单价 -->
            <el-col :span="12">
              <el-form-item :label="getUnitPriceLabel()" :prop="'unitDeclarePriceD'" :rules="declareRule['unitDeclarePriceD']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.unitDeclarePriceD"
                    :placeholder="getUnitPriceLabel()"
                    :disabled="batchForm.unitDeclarePriceDClear">
                  </el-input>
                  <!-- 不允许置空 -->
                  <!-- <el-checkbox
                    v-model="batchForm.unitDeclarePriceDClear"
                    class="clear-checkbox"
                    @change="handleClearChange('unitDeclarePriceD', $event)">
                    是否置空
                  </el-checkbox> -->
                </div>
              </el-form-item>
            </el-col>

            <!-- HS编码 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.hsCode')" :prop="'hsCode'" :rules="declareRule['hsCode']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.hsCode"
                    :placeholder="$t('coOrderDeclare.hsCode')"
                    :disabled="batchForm.hsCodeClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.hsCodeClear"
                    class="clear-checkbox"
                    @change="handleClearChange('hsCode', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 单位净重 -->
            <el-col :span="12">
              <el-form-item :label="getUnitWeightLabel()" :prop="'unitNetWeightD'" :rules="declareRule['unitNetWeightD']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.unitNetWeightD"
                    :placeholder="getUnitWeightLabel()"
                    :disabled="batchForm.unitNetWeightDClear">
                  </el-input>
                  <!-- 不允许置空 -->
                  <!-- <el-checkbox
                    v-model="batchForm.unitNetWeightDClear"
                    class="clear-checkbox"
                    @change="handleClearChange('unitNetWeightD', $event)">
                    是否置空
                  </el-checkbox> -->
                </div>
              </el-form-item>
            </el-col>

            <!-- 品牌 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.brand')" :prop="'brand'" :rules="declareRule['brand']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.brand"
                    :placeholder="$t('coOrderDeclare.brand')"
                    :disabled="batchForm.brandClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.brandClear"
                    class="clear-checkbox"
                    @change="handleClearChange('brand', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 商品条码 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" :prop="'goodsBarcode'" :rules="declareRule['goodsBarcode']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.goodsBarcode"
                    :placeholder="$t('coOrderDeclare.goodsBarcode')"
                    :disabled="batchForm.goodsBarcodeClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.goodsBarcodeClear"
                    class="clear-checkbox"
                    @change="handleClearChange('goodsBarcode', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- SKU -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.sku')" :prop="'sku'" :rules="declareRule['sku']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.sku"
                    :placeholder="$t('coOrderDeclare.sku')"
                    :disabled="batchForm.skuClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.skuClear"
                    class="clear-checkbox"
                    @change="handleClearChange('sku', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 产品型号 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.productModel')" :prop="'productModel'" :rules="declareRule['productModel']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.productModel"
                    :placeholder="$t('coOrderDeclare.productModel')"
                    :disabled="batchForm.productModelClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.productModelClear"
                    class="clear-checkbox"
                    @change="handleClearChange('productModel', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 材质 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.material')" :prop="'material'" :rules="declareRule['material']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.material"
                    :placeholder="$t('coOrderDeclare.material')"
                    :disabled="batchForm.materialClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.materialClear"
                    class="clear-checkbox"
                    @change="handleClearChange('material', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 用途 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.purpose')" :prop="'purpose'" :rules="declareRule['purpose']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.purpose"
                    :placeholder="$t('coOrderDeclare.purpose')"
                    :disabled="batchForm.purposeClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.purposeClear"
                    class="clear-checkbox"
                    @change="handleClearChange('purpose', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 原产地 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.origin')" :prop="'origin'" :rules="declareRule['origin']">
                <div class="batch-input-wrapper">
                  <el-select v-model="batchForm.origin" :placeholder="$t('coOrderDeclare.origin')" filterable clearable>
                    <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                  </el-select>
                  <el-checkbox
                    v-model="batchForm.originClear"
                    class="clear-checkbox"
                    @change="handleClearChange('origin', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 拣货备注 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.pickingRemark')" :prop="'pickingRemark'" :rules="declareRule['pickingRemark']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.pickingRemark"
                    :placeholder="$t('coOrderDeclare.pickingRemark')"
                    :disabled="batchForm.pickingRemarkClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.pickingRemarkClear"
                    class="clear-checkbox"
                    @change="handleClearChange('pickingRemark', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 产品链接 -->
            <el-col :span="12">
              <el-form-item :label="$t('coOrderDeclare.productUrl')" :prop="'productUrl'" :rules="declareRule['productUrl']">
                <div class="batch-input-wrapper">
                  <el-input
                    v-model="batchForm.productUrl"
                    :placeholder="$t('coOrderDeclare.productUrl')"
                    :disabled="batchForm.productUrlClear">
                  </el-input>
                  <el-checkbox
                    v-model="batchForm.productUrlClear"
                    class="clear-checkbox"
                    @change="handleClearChange('productUrl', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>

            <!-- 图片上传 -->
            <el-col :span="24">
              <el-form-item :label="$t('wsComWaybill.declare.picUrl')" :prop="'picUrl'" :rules="declareRule['picUrl']">
                <div class="batch-input-wrapper">
                  <div class="pic-upload-container">
                    <!-- 图片预览 -->
                    <div v-if="batchForm.picUrl || batchForm.picBase64" class="pic-preview">
                      <el-image
                        style="width: 100px; height: 100px;"
                        fit="scale-down"
                        :src="batchForm.picBase64 || batchForm.picUrl"
                        :preview-src-list="[batchForm.picBase64 || batchForm.picUrl]">
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i class='el-icon-close pic-remove-btn' @click='picRemoveHandle()'></i>
                    </div>

                    <!-- 上传按钮 -->
                    <el-upload
                      action="#"
                      :limit="1"
                      accept='.jpg,.img,.jpeg,.bmp,.png,.gif'
                      :on-change="picUploadHandle"
                      :on-error="errorHandle"
                      :auto-upload='false'
                      :show-file-list='false'
                      :file-list="picFileList"
                      :disabled="batchForm.picUrlClear">
                      <el-button type="primary" size="small" :disabled="batchForm.picUrlClear">
                        <i class="el-icon-upload"></i>
                        {{ $t('fba.uploadPic') }}
                      </el-button>
                    </el-upload>
                  </div>
                  <el-checkbox
                    v-model="batchForm.picUrlClear"
                    class="clear-checkbox"
                    @change="handleClearChange('picUrl', $event)">
                    是否置空
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 操作按钮 -->
          <div style="text-align: center; margin-top: 20px;">
            <el-button type="primary" @click="batchSave" :loading="saveLoading">
              <i class="el-icon-check"></i>
              提交
            </el-button>
            <el-button @click="resetBatchForm">
              <i class="el-icon-refresh"></i>
              重置表单
            </el-button>
            <el-button @click="cancelFn">
              <i class="el-icon-close"></i>
              取消
            </el-button>
          </div>
        </el-card>
      </el-form>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import {
  isOverLength,
  isPlusInteger2,
  isPlusFloat,
  isDecimal3, isURL
} from '@/utils/validate'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
export default {
  mixins: [mixinViewModule, listPage],
  data () {
    return {
      visible: false,
      tableHeight2: '',
      declareDataList: [],
      picFileList: [],
      countryList: [],
      dataForm: {
        orderId: '',
        waybillId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeightD: '',
        unitDeclarePriceD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: ''
      },
      orderDataForm: {
      },
      declareForm: {
        dataList: []
      },
      // 批量编辑表单数据
      batchForm: {
        chineseName: '',
        chineseNameClear: false,
        englishName: '',
        englishNameClear: false,
        quantity: '',
        quantityClear: false,
        unitDeclarePriceD: '',
        unitDeclarePriceDClear: false,
        hsCode: '',
        hsCodeClear: false,
        unitNetWeightD: '',
        unitNetWeightDClear: false,
        brand: '',
        brandClear: false,
        goodsBarcode: '',
        goodsBarcodeClear: false,
        sku: '',
        skuClear: false,
        productModel: '',
        productModelClear: false,
        material: '',
        materialClear: false,
        purpose: '',
        purposeClear: false,
        origin: '',
        originClear: false,
        pickingRemark: '',
        pickingRemarkClear: false,
        productUrl: '',
        productUrlClear: false,
        picUrl: '',
        picBase64: '',
        picUrlClear: false
      },
      saveLoading: false,
      mixinViewModuleOptions: {
        getDataListURL: '/co/orderdeclare/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      tableColumns: [
        { type: '', width: '200', prop: 'packageCustomerNo', label: this.$t('coOrderDeclare.packageCustomerNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'chineseName', label: this.$t('coOrderDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'englishName', label: this.$t('coOrderDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'quantity', label: this.$t('coOrderDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'unitDeclarePriceD', label: this.$t('coOrderDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '160', prop: 'hsCode', label: this.$t('coOrderDeclare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitNetWeightD', label: this.$t('coOrderDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'brand', label: this.$t('coOrderDeclare.brand'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'goodsBarcode', label: this.$t('coOrderDeclare.goodsBarcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'sku', label: this.$t('coOrderDeclare.sku'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'productModel', label: this.$t('coOrderDeclare.productModel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'material', label: this.$t('coOrderDeclare.material'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'purpose', label: this.$t('coOrderDeclare.purpose'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'origin', label: this.$t('coOrderDeclare.origin'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'pickingRemark', label: this.$t('coOrderDeclare.pickingRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'productUrl', label: this.$t('coOrderDeclare.productUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'picUrl', label: this.$t('wsComWaybill.declare.picUrl'), align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        if (item.prop === 'unitNetWeightD') {
          item.label = this.$t('fba.unitNetWeight') + '(' + (this.orderDataForm.standardUnit === 1 ? 'LB' : 'KG') + ')'
        }
        if (item.prop === 'unitDeclarePriceD' && this.orderDataForm.declareCurrency) {
          item.label = this.$t('wsComWaybill.declare.unitDeclarePrice') + '(' + this.orderDataForm.declareCurrency + ')'
        }
        return item.isShow
      })
      return arr
    },
    declareRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isLength2 = (rule, value, callback) => {
        if (value && !isOverLength(value, 2)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 2 })))
        }
        callback()
      }
      const isLength255 = (rule, value, callback) => {
        if (value && !isOverLength(value, 255)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 255 })))
        }
        callback()
      }
      const isLength32 = (rule, value, callback) => {
        if (value && !isOverLength(value, 32)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 32 })))
        }
        callback()
      }
      const isLength64 = (rule, value, callback) => {
        if (value && !isOverLength(value, 64)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 64 })))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        if (value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        if (value && value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      const picURL = (rule, value, callback) => {
        if (value && !isURL(value)) {
          return callback(new Error('不是合法的图片地址'))
        }
        callback()
      }
      // const repeatBoxNoSKU = (rule, value, callback) => {
      //   if (!value) {
      //     return callback()
      //   }
      //   let sameBoxNoDiffSKUList = this.declareForm.dataList.filter(item => item.packageCustomerNo === value)
      //     .map(item => item.sku).filter(item => typeof item !== 'undefined' && item !== '')
      //   if (sameBoxNoDiffSKUList.length === 1) {
      //     return callback()
      //   }
      //   let sameBoxNoDiffSKUSet = new Set(sameBoxNoDiffSKUList)
      //   if (sameBoxNoDiffSKUList.length > 1 && sameBoxNoDiffSKUList.length !== sameBoxNoDiffSKUSet.size) {
      //     return callback(new Error('已有相同箱和相同SKU'))
      //   }
      //   callback()
      // }
      // const repeatSKUBoxNo = (rule, value, callback) => {
      //   if (!value) {
      //     callback()
      //   }
      //   let sameSKUDiffBoxNoMap = new Map()
      //   this.declareForm.dataList.filter(item => item.sku === value)
      //     .filter(item => typeof item !== 'undefined')
      //     .map(item => {
      //       if (sameSKUDiffBoxNoMap.has(item.sku)) {
      //         let arr = sameSKUDiffBoxNoMap.get(item.sku)
      //         arr.push(item.packageCustomerNo)
      //         sameSKUDiffBoxNoMap.set(item.sku, arr)
      //       } else {
      //         sameSKUDiffBoxNoMap.set(item.sku, [item.packageCustomerNo])
      //       }
      //     })
      //   console.log('sameSKUDiffBoxNoMap', sameSKUDiffBoxNoMap)
      //   sameSKUDiffBoxNoMap.forEach((val, key) => {
      //     let sameSKUDiffBoxNoSet = new Set(val)
      //     if (val.length !== sameSKUDiffBoxNoSet.size) {
      //       return callback(new Error('已有相同箱和相同SKU'))
      //     }
      //   })
      //   callback()
      // }
      const isValidBoxNo = (rule, value, callback) => {
        if (!value) {
          callback()
        }
        let sameBoxNoList = this.declareForm.dataList.filter(item => item.packageCustomerNo === value)
        if (!sameBoxNoList || sameBoxNoList.length === 0) {
          return callback(new Error('请先录入该箱信息'))
        }
        callback()
      }
      return {
        packageCustomerNo: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength36, trigger: 'blur' },
          { validator: isValidBoxNo, trigger: 'blur' }
          // { validator: repeatBoxNoSKU, trigger: 'blur' }
        ],
        sku: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
          // { validator: repeatSKUBoxNo, trigger: 'blur' }
        ],
        englishName: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        chineseName: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        quantity: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isInteger, trigger: 'blur' }
        ],
        unitDeclarePriceD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        unitNetWeightD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        hsCode: [
          { validator: isLength36, trigger: 'blur' }
        ],
        origin: [
          { validator: isLength2, trigger: 'blur' }
        ],
        material: [
          { validator: isLength64, trigger: 'blur' }
        ],
        productModel: [
          { validator: isLength32, trigger: 'blur' }
        ],
        purpose: [
          { validator: isLength64, trigger: 'blur' }
        ],
        brand: [
          { validator: isLength32, trigger: 'blur' }
        ],
        productUrl: [
          { validator: isLength255, trigger: 'blur' }
        ],
        picUrl: [
          { validator: picURL, trigger: 'blur' }
        ],
        pickingRemark: [
          { validator: isLength255, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    init (orderId, waybillId) {
      this.visible = true
      this.dataForm.orderId = orderId
      this.dataForm.waybillId = waybillId
      this.$nextTick(() => {
        // 只获取订单信息，不加载申报信息列表
        new Promise(this.getOrder).then(() => {
          // 重置批量表单
          this.resetBatchForm()
        }).finally(() => {
          this.$refs.batchForm && this.$refs.batchForm.clearValidate()
        })
      })
    },
    getOrder (resolve, reject) {
      this.$http.get(`/co/order/${this.dataForm.orderId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return reject(this.$message.error(res.msg))
        }
        this.orderDataForm = {
          ...this.orderDataForm,
          ...res.data
        }
      }).catch(() => {}).finally(() => resolve())
    },
    $getDataListCallback () {
      // 查询列表的回调方法（批量模式下不使用）
      this.dataList.forEach((item) => {
        this.$set(item, 'update', false)
      })
      this.declareForm.dataList = this.dataList
      return false
    },
    // 重置批量表单
    resetBatchForm () {
      this.batchForm = {
        chineseName: '',
        chineseNameClear: false,
        englishName: '',
        englishNameClear: false,
        quantity: '',
        quantityClear: false,
        unitDeclarePriceD: '',
        unitDeclarePriceDClear: false,
        hsCode: '',
        hsCodeClear: false,
        unitNetWeightD: '',
        unitNetWeightDClear: false,
        brand: '',
        brandClear: false,
        goodsBarcode: '',
        goodsBarcodeClear: false,
        sku: '',
        skuClear: false,
        productModel: '',
        productModelClear: false,
        material: '',
        materialClear: false,
        purpose: '',
        purposeClear: false,
        origin: '',
        originClear: false,
        pickingRemark: '',
        pickingRemarkClear: false,
        productUrl: '',
        productUrlClear: false,
        picUrl: '',
        picBase64: '',
        picUrlClear: false
      }
      this.$nextTick(() => {
        this.$refs.batchForm && this.$refs.batchForm.clearValidate()
      })
    },
    // 处理置空复选框变化
    handleClearChange (fieldName, checked) {
      if (checked) {
        // 如果勾选置空，清空对应的输入框值
        this.batchForm[fieldName] = ''
        if (fieldName === 'picUrl') {
          this.batchForm.picBase64 = ''
          this.picFileList = []
        }
      }
    },
    // 获取单价标签
    getUnitPriceLabel () {
      const baseLabel = this.$t('wsComWaybill.declare.unitDeclarePrice')
      return this.orderDataForm.declareCurrency
        ? `${baseLabel}(${this.orderDataForm.declareCurrency})`
        : baseLabel
    },
    // 获取单位重量标签
    getUnitWeightLabel () {
      const baseLabel = this.$t('fba.unitNetWeight')
      const unit = this.orderDataForm.standardUnit === 1 ? 'LB' : 'KG'
      return `${baseLabel}(${unit})`
    },
    async getBaseData () {
      this.countryList = await baseData(baseDataApi.countryList)
    },
    picRemoveHandle () {
      this.picFileList = []
      this.$nextTick(() => {
        this.batchForm.picBase64 = ''
        this.batchForm.picUrl = ''
      })
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    picUploadHandle (file) {
      this.picFileList = []
      console.log('file.name', file.name)
      let regStr = '(.jpg)$|(.img)$|(.jpeg)$|(.bmp)$|(.png)$|(.gif)$'
      let formatTip = '.jpg,.img,.jpeg,.bmp,.png,.gif'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        return this.$message.error(this.$t('upload.tip', { 'format': `${formatTip}` }))
      }
      console.log('file.size', file.size)
      const sizeLimit = file.size / 1024 / 1024 > 1
      if (sizeLimit) {
        return this.$message.error(this.$t('upload.sizeMsg', { 'size': `${1}` }))
      }
      console.log('picUploadHandle file', file)
      let fileReader = new FileReader()
      fileReader.readAsDataURL(file.raw)
      let that = this
      fileReader.onloadend = ev => {
        that.$nextTick(() => {
          that.batchForm.picUrl = ''
          that.batchForm.picBase64 = ev.target.result
          console.log('图片上传成功，base64数据长度:', ev.target.result ? ev.target.result.length : 0)
          console.log('batchForm.picBase64 已设置:', that.batchForm.picBase64 ? 'Yes' : 'No')
        })
      }
    },
    // 批量保存
    batchSave () {
      // 表单验证
      this.$refs.batchForm.validate((valid) => {
        if (!valid) {
          this.$message.error('请检查表单输入')
          return
        }

        // 构建批量更新数据
        const updateData = this.buildBatchUpdateData()

        // 检查是否有需要更新的字段
        if (Object.keys(updateData).length === 0) {
          this.$message.warning('请至少填写一个需要更新的字段')
          return
        }

        // 确认操作
        this.$confirm('确定要批量更新申报信息吗？', '批量更新确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.saveLoading = true

          // 调用批量更新接口
          // const requestData = {
          //   orderId: this.dataForm.orderId,
          //   // waybillId: this.dataForm.waybillId,
          //   updateData
          // }

          this.$http.post(`/co/orderdeclare/batchUpdate/${this.dataForm.orderId}`, updateData).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }

            this.$message({
              message: '批量更新成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
            this.$message.error('批量更新失败，请稍后重试')
          }).finally(() => {
            this.saveLoading = false
          })
        })
      })
    },

    // 构建批量更新数据
    buildBatchUpdateData () {
      const updateData = {}
      const fields = [
        'chineseName', 'englishName', 'quantity', 'unitDeclarePriceD',
        'hsCode', 'unitNetWeightD', 'brand', 'goodsBarcode', 'sku',
        'productModel', 'material', 'purpose', 'origin', 'pickingRemark',
        'productUrl', 'picUrl', 'picBase64'
      ]

      updateData['waybillId'] = this.dataForm.waybillId

      fields.forEach(field => {
        const clearField = field + 'Clear'

        if (this.batchForm[clearField]) {
          // 如果勾选了置空，设置为 N/A
          updateData[field] = 'N/A'
        } else {
          // 检查是否有值
          if (this.batchForm[field] !== '' && this.batchForm[field] !== null && this.batchForm[field] !== undefined) {
            updateData[field] = this.batchForm[field]
          }
        }
      })

      return updateData
    },
    // 取消
    cancelFn () {
      this.visible = false
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    notAddRedStar (h, { column }) {
      return [h('span', ' ' + column.label)]
    },
    indexMethod (index) {
      if (!this.dataList || this.dataList.length <= 0) {
        return index
      }
      return (this.page - 1) * this.limit + index + 1
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  }
}
</script>

<style lang="scss" scoped>
::v-deep #declareTable .el-form-item  {
  margin-bottom: 0px !important;
}

.batch-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;

  .el-input {
    flex: 1;
  }

  .clear-checkbox {
    flex-shrink: 0;
    white-space: nowrap;

    ::v-deep .el-checkbox__label {
      font-size: 12px;
      color: #606266;
    }
  }
}

.pic-upload-container {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;

  .pic-preview {
    position: relative;
    display: inline-block;

    .pic-remove-btn {
      position: absolute;
      top: -8px;
      right: -8px;
      color: #f56c6c;
      background: #fff;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-size: 12px;

      &:hover {
        background: #f56c6c;
        color: #fff;
      }
    }
  }
}

::v-deep .el-form-item__label {
  font-weight: 600;
  color: #303133;
}

::v-deep .el-card__header {
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.el-button + .el-button {
  margin-left: 12px;
}

@media (max-width: 768px) {
  .batch-input-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;

    .clear-checkbox {
      align-self: flex-start;
    }
  }

  .pic-upload-container {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
