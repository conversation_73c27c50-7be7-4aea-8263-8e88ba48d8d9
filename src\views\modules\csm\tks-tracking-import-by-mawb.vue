<template>
  <div class="add-body panel_body">
    <!-- 导入 -->
    <import-excel
      ref="importExcel"
      :file-type="['xls', 'xlsx']"
      :importExcelUrl="importExcelUrl"
      :downLoadUrl="downLoadUrl"
      :showFooter="showFooter"></import-excel>
  </div>
</template>

<script>
import importExcel from '@/components/importExcel'
import mixinViewModule from '@/mixins/view-module'

export default {
  name: 'tks-tracking-import-by-mawb',
  mixins: [mixinViewModule],
  components: {
    importExcel
  },
  data () {
    return {
      // 避免引入mixinViewModule初始化查询
      mixinViewModuleOptions: {
        activatedIsNeed: false
      },
      importExcelUrl: `${this.$baseUrl}/ws/commawb/importByMawb`,
      downLoadUrl: '/csm/tksTrackingByMawb.xlsx',
      showFooter: false
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.importExcelVisible = true
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs.importExcel.init()
      })
    },
    // 返回
    backFn () {
      this.visible = false
      this.visible = false
      this.$emit('backSeaportImportExcel')
    }
  }
}
</script>

<style scoped>

</style>
