<template>
<el-dialog :visible.sync="visible"  :title="$t('wsComWaybill.modifyFbaPackageInfo')"  width="70%"  top="10px"
      :append-to-body="true" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true"
      class="location_model" @close="cancelFn">
    <!--    <div class=" flex_wrap" >-->
    <el-card class="search_box" shadow="never">
      <el-form ref="searchForm"  class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="120px">
        <el-row :gutter="2" type="flex">
          <el-col >
            <el-row :gutter="2">
              <el-col :sm="24" :md="8">
                <el-form-item label-width="0" prop="packageNo">
                  <el-input v-model="dataForm.packageNo" :placeholder="$t('wsComInSubWaybill.packageNo')" clearable ></el-input>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item label-width="0" prop="subCustomerOrderNo">
                  <el-input v-model="dataForm.subCustomerOrderNo" :placeholder="$t('coOrder.subCustomerOrderNo')" clearable ></el-input>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item label-width="0" prop="deliveryNo">
                  <el-input v-model="dataForm.deliveryNo" :placeholder="$t('wsComInSubWaybill.deliveryNo')" clearable ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <div class="search_box_btn">
            <el-row>
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <!-- <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button> -->
              <el-button type="info" @click="addRow()" icon="el-icon-plus">增加</el-button>
            </el-row>
          </div>
        </el-row>
      </el-form>
    </el-card>
    <el-form :model='packageForm' style="margin-top: 10px"  ref="packageForm" key='9' :inline-message='true'>
      <el-table id="declareTable"  v-loading="dataListLoading" :data="packageForm.dataList" @selection-change="dataListSelectionChangeHandle" max-height="410px"
          show-summary :summary-method="getSummaries">
        <!-- 动态显示表格 -->
        <el-table-column prop="serialNo" :label="this.$t('label.serialNo')" type="index" width='50' :index="indexMethod" fixed="left"></el-table-column>
        <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop"
                         :render-header="['packageNo','lengthD','widthD','heightD','weightD'].indexOf(item.prop) > -1 ? addRedStar: notAddRedStar"
                         header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
          <template slot-scope="scope">
            <el-form-item :prop="'dataList.' + scope.$index + '.'+item.prop"  :rules='declareRule[item.prop]'>
              <span v-show="!scope.row.update" v-text="scope.row[item.prop]"></span>
              <el-input v-show="scope.row.update"  v-model="scope.row[item.prop]" :disabled="disabledColumn(item.prop)"></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="100">
          <template slot="header" slot-scope="scope">
            <span>{{$t('handle')}}</span>
            <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-link v-if="scope.row.update" :underline='false' @click="declareInfoHandle(scope)">{{ $t('addDeclareCargo') }}</el-link>
            <el-link  :underline='false' @click="saveRow(scope, scope.row.update ? 'update' : 'save' )" :class="scope.row.update ? 'el-link--warning' : 'el-link--default'">
              <!--              <label :class="scope.row.update ? 'warning' : 'primary'">-->
              {{ scope.row.update ? $t('save') : $t('update') }}
              <!--              </label>-->
            </el-link>
            <el-link v-if="scope.row.id" type="danger" :underline="false" @click="deleteRow(scope.row.id)">{{ $t('delete') }}</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
    </el-pagination>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import { formatterType, gtmToLtm, timestampFormat, numberFormat } from '@/filters/filters'
import {
  isOverLength,
  isPlusInteger2,
  isPlusFloat,
  isDecimal3
} from '@/utils/validate'
export default {
  mixins: [mixinViewModule, listPage],
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        waybillId: '',
        subCustomerOrderNo: '',
        customerOrderNo: '',
        waybillNo: '',
        status: '',
        deliveryNo: ''
      },
      waybillDataForm: {
      },
      packageForm: {
        dataList: []
      },
      mixinViewModuleOptions: {
        getDataListURL: '/ws/cominsubwaybill/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      // 能否修改重量和尺寸
      canModifySizeOrWeight: false,
      tableColumns: [
        { type: '', width: '10', prop: 'id', label: this.$t('comInSubWaybill.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '200', prop: 'waybillId', label: this.$t('wsComInSubWaybill.waybillId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '200', prop: 'packageNo', label: this.$t('wsComInSubWaybill.packageNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'subCustomerOrderNo', label: this.$t('coOrder.subCustomerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'lengthD', label: this.$t('wsComInSubWaybill.length'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'widthD', label: this.$t('wsComInSubWaybill.width'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'heightD', label: this.$t('wsComInSubWaybill.height'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'weightD', label: this.$t('wsComInSubWaybill.weight'), align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    declareRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        if (value && value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      // const repeatBoxNoSKU = (rule, value, callback) => {
      //   if (!value) {
      //     return callback()
      //   }
      //   let sameBoxNoList = this.packageForm.dataList.filter(item => item.packageNo === value)
      //     .map(item => item.sku).filter(item => typeof item !== 'undefined' && item !== '')
      //   if (sameBoxNoList.length === 1) {
      //     return callback()
      //   }
      //   callback()
      // }
      return {
        packageNo: [
          { required: false, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' }
          // { validator: repeatBoxNoSKU, trigger: 'blur' }
        ],
        widthD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        lengthD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        heightD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        weightD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    init (waybillId) {
      this.visible = true
      this.canModifySizeOrWeight = false
      this.dataForm.waybillId = waybillId
      this.$nextTick(() => {
        new Promise(this.getOrder).then(() => this.queryPageByParam()).finally(() => {
          this.$refs.packageForm.clearValidate()
        })
      })
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (column.property === 'weightD') {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = sums[index].toFixed(3)
          } else {
            sums[index] = ''
          }
        } else if (column.property === 'serialNo') {
          sums[index] = '合计'
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    // 禁止修改的属性
    disabledColumn (property) {
      console.log('canModifySizeOrWeight', this.canModifySizeOrWeight)
      if (property === 'lengthD' || property === 'widthD' || property === 'heightD' || property === 'weightD') {
        return this.canModifySizeOrWeight
      } else return false
    },
    getOrder (resolve, reject) {
      this.$http.get(`/ws/comwaybill/${this.dataForm.waybillId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return reject(this.$message.error(res.msg))
        }
        this.waybillDataForm = {
          ...this.waybillDataForm,
          ...res.data
        }
        // 如果已获取尾程单号, 则不允许修改重量和尺寸
        // if (this.waybillDataForm.deliveryNode !== 10) {
        //   this.canModifySizeOrWeight = true
        // }
      }).catch(() => {}).finally(() => resolve())
    },
    $getDataListCallback () {
      // 查询列表的回调方法
      this.dataList.forEach((item) => {
        this.$set(item, 'update', false)
      })
      this.packageForm.dataList = this.dataList
      return false
    },
    async getBaseData () {
    },
    // 删除箱明细
    deleteRow (id) {
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('delete') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        let reqObj = { id: id, waybillId: this.dataForm.waybillId }
        this.$http.post(`/ws/cominsubwaybill/deletePackageInfo`, reqObj).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$nextTick(() => {
              // this.$set(scope.row, 'update', !scope.row.update)
                this.packageForm.dataList = this.packageForm.dataList.filter(item => item.id !== id)
              })
            }
          })
        }).catch(() => {
        })
      })
    },
    // 增加一行箱明细
    addRow () {
      let dataList = this.packageForm.dataList
      let packageNo = this.waybillDataForm.customerOrderNo + '-' + (dataList.length + 1)
      let item = { ...dataList[0] }
      item.update = true
      item.id = ''
      item.packageNo = packageNo
      item.subCustomerOrderNo = ''
      item.lengthD = ''
      item.widthD = ''
      item.heightD = ''
      item.weightD = ''
      this.packageForm.dataList.push(item)
    },
    
    saveRow (scope, action) {
      if (action === 'update') {
        let arr = [
          'dataList.' + scope.$index + '.id',
          'dataList.' + scope.$index + '.waybillId',
          'dataList.' + scope.$index + '.packageNo',
          'dataList.' + scope.$index + '.lengthD',
          'dataList.' + scope.$index + '.widthD',
          'dataList.' + scope.$index + '.heightD',
          'dataList.' + scope.$index + '.weightD'
        ]
        let validatedMsgList = []
        this.$refs.packageForm.validateField(arr, (error) => {
          if (error) {
            validatedMsgList.push(error)
          }
        })

        if (validatedMsgList.every((item) => item === '')) {
          let reqObj = Object.assign(scope.row, { waybillId: this.dataForm.waybillId })
          if (!reqObj.weightD) {
            return this.$message.error('实重必填')
          }
          let method = reqObj.id ? 'updatePackageInfo' : 'addPackageInfo'
          this.$http.post(`/ws/cominsubwaybill/${method}`, reqObj).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.$nextTick(() => {
                  this.$set(scope.row, 'update', !scope.row.update)
                })
              }
            })
          }).catch(() => {
          })
        }
      } else {
        this.$nextTick(() => {
          this.$set(scope.row, 'update', !scope.row.update)
        })
      }
    },
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('modifyPackageInfoAfterHandle')
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    notAddRedStar (h, { column }) {
      return [h('span', ' ' + column.label)]
    },
    indexMethod (index) {
      if (!this.dataList || this.dataList.length <= 0) {
        return index
      }
      return (this.page - 1) * this.limit + index + 1
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    numberFormat,
    timestampFormat
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep #declareTable .el-form-item  {
    margin-bottom: 0px !important;
  }
</style>
