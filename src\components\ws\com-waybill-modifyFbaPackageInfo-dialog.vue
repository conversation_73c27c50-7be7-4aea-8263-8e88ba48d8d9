<template>
<el-dialog :visible.sync="visible"  :title="$t('wsComWaybill.modifyFbaPackageInfo')"  width="70%"  top="10px"
      :append-to-body="true" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true"
      class="location_model" @close="cancelFn">
    <!--    <div class=" flex_wrap" >-->
    <el-card class="search_box" shadow="never">
      <el-form ref="searchForm"  class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="120px">
        <el-row :gutter="2" type="flex">
          <el-col >
            <el-row :gutter="2">
              <el-col :sm="24" :md="8">
                <el-form-item label-width="0" prop="packageNo">
                  <el-input v-model="dataForm.packageNo" :placeholder="$t('wsComInSubWaybill.packageNo')" clearable ></el-input>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item label-width="0" prop="subCustomerOrderNo">
                  <el-input v-model="dataForm.subCustomerOrderNo" :placeholder="$t('coOrder.subCustomerOrderNo')" clearable ></el-input>
                </el-form-item>
              </el-col>
              <el-col :sm="24" :md="8">
                <el-form-item label-width="0" prop="deliveryNo">
                  <el-input v-model="dataForm.deliveryNo" :placeholder="$t('wsComInSubWaybill.deliveryNo')" clearable ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <div class="search_box_btn">
            <el-row>
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <!-- <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button> -->
              <el-button type="info" @click="addRow()" icon="el-icon-plus">增加</el-button>
            </el-row>
          </div>
        </el-row>
      </el-form>
    </el-card>
    <el-form :model='packageForm' style="margin-top: 10px"  ref="packageForm" key='9' :inline-message='true'>
      <el-table id="declareTable"  v-loading="dataListLoading" :data="packageForm.dataList" @selection-change="dataListSelectionChangeHandle" max-height="410px"
          show-summary :summary-method="getSummaries">
        <!-- 动态显示表格 -->
        <el-table-column prop="serialNo" :label="this.$t('label.serialNo')" type="index" width='50' :index="indexMethod" fixed="left"></el-table-column>
        <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop"
                         :render-header="['packageNo','lengthD','widthD','heightD','weightD'].indexOf(item.prop) > -1 ? addRedStar: notAddRedStar"
                         header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
          <template slot-scope="scope">
            <el-form-item :prop="'dataList.' + scope.$index + '.'+item.prop"  :rules='declareRule[item.prop]'>
              <span v-show="!scope.row.update" v-text="scope.row[item.prop]"></span>
              <el-input v-show="scope.row.update"  v-model="scope.row[item.prop]" :disabled="disabledColumn(item.prop)"></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="100">
          <template slot="header" slot-scope="scope">
            <span>{{$t('handle')}}</span>
            <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-link v-if="scope.row.update" :underline='false' @click="declareInfoHandle(scope)">
              {{ $t('addDeclareCargo') }}
              <el-badge
                v-if="hasTemporaryDeclareInfo(scope.row.packageNo)"
                :value="getTemporaryDeclareCount(scope.row.packageNo)"
                class="declare-badge">
              </el-badge>
            </el-link>
            <el-link  :underline='false' @click="saveRow(scope, scope.row.update ? 'update' : 'save' )" :class="scope.row.update ? 'el-link--warning' : 'el-link--default'">
              <!--              <label :class="scope.row.update ? 'warning' : 'primary'">-->
              {{ scope.row.update ? $t('save') : $t('update') }}
              <!--              </label>-->
            </el-link>
            <el-link v-if="scope.row.id" type="danger" :underline="false" @click="deleteRow(scope.row.id)">{{ $t('delete') }}</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
    </el-pagination>

    <!-- 申报明细管理弹窗 -->
    <el-dialog
      :visible.sync="declareDialogVisible"
      title="申报明细管理"
      width="80%"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDeclareDialog">

      <el-tabs v-model="declareActiveTab" @tab-click="handleTabClick">
        <!-- 第一个标签页：填充已有明细 -->
        <el-tab-pane label="填充已有明细" name="existing">
          <div v-if="existingDeclareList.length === 0" class="no-data-tip">
            <el-empty description="暂无其他箱的申报明细数据"></el-empty>
          </div>
          <div v-else>
            <div class="declare-tip">
              <el-alert
                title="提示：选择其他箱内已存在的申报明细，可快速填充到当前箱中"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
            <!-- <el-table
              :data="existingDeclareList"
              @selection-change="handleExistingSelectionChange"
              max-height="400px"
              style="margin-top: 15px;">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="packageNo" label="箱号" width="150"></el-table-column>
              <el-table-column prop="chineseName" label="中文名称" width="150"></el-table-column>
              <el-table-column prop="englishName" label="英文名称" width="150"></el-table-column>
              <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
              <el-table-column prop="unitDeclarePriceD" label="单价" width="100"></el-table-column>
              <el-table-column prop="hsCode" label="HS编码" width="120"></el-table-column>
              <el-table-column prop="brand" label="品牌" width="100"></el-table-column>
              <el-table-column prop="sku" label="SKU" width="120"></el-table-column>
            </el-table> -->
            <el-form :model='declareForm' style="margin-top: 10px"  ref="declareForm" key='9' :inline-message='true'>
            <el-table id="declareTable"  v-loading="dataListLoading" :data="existingDeclareList"  border @selection-change="handleExistingSelectionChange" max-height="410px">
              <!-- 动态显示表格 -->
              <el-table-column :label="this.$t('label.serialNo')" type="index" width='50' :index="indexMethod" fixed="left"></el-table-column>
              <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" :render-header="['quantity','englishName'].indexOf(item.prop) > -1 ? addRedStar: notAddRedStar" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                <template slot-scope="scope">
                    <el-form-item :prop="'dataList.' + scope.$index + '.'+item.prop"  :rules='declareRule[item.prop]'>
                      <div v-show='!scope.row.update' >
                        <div v-if="item.prop === 'picUrl'">
                          <el-image v-if='scope.row.picUrl' style="width: 5vw; height: 5vh;" fit="scale-down"  :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" >
                            <div slot="placeholder" class="image-slot">
                              加载中<span class="dot">...</span>
                            </div>
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                          <el-image v-else-if='scope.row.picBase64' style="width: 5vw; height: 5vh;" fit="scale-down" :src="scope.row.picBase64" :preview-src-list="[scope.row.picBase64]" >
                            <div slot="placeholder" class="image-slot">
                              加载中<span class="dot">...</span>
                            </div>
                            <div slot="error" class="image-slot">
                              <i class="el-icon-picture-outline"></i>
                            </div>
                          </el-image>
                        </div>
                        <span v-else v-text="scope.row[item.prop]"></span>
                      </div>
                      <div v-show='scope.row.update' >
                        <div v-if="item.prop === 'picUrl'">
                          <div  v-if='scope.row.picUrl'>
                            <el-image style="width: 5vw; height: 5vh;" fit="scale-down"  :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" >
                              <div slot="placeholder" class="image-slot">
                                加载中<span class="dot">...</span>
                              </div>
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                            <i  class='el-icon-close ' style='position:absolute; top: 0; color: red;cursor:pointer' @click='picRemoveHandle(scope.row)'></i>
                          </div>
                          <div v-else-if='scope.row.picBase64'>
                            <el-image  style="width: 5vw; height: 5vh;" fit="scale-down" :src="scope.row.picBase64" :preview-src-list="[scope.row.picBase64]" >
                              <div slot="placeholder" class="image-slot">
                                加载中<span class="dot">...</span>
                              </div>
                              <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                            <i  class='el-icon-close ' style='position:absolute; top: 0; color: red;cursor:pointer' @click='picRemoveHandle(scope.row)'></i>
                          </div>
                          <el-upload  action="#" :limit="1" accept='.jpg,.img,.jpeg,.bmp,.png,.gif' :on-change="(file) => picUploadHandle(file, scope.row)"
                                      :on-error="errorHandle" :auto-upload='false' :show-file-list='false' :file-list="picFileList" style='display: inline-flex;margin-top: 2px'>
                            <el-button type="text" size="mini" class='margin_left10 margin_right10 ' style='color: #1890ff' slot="trigger" >{{ $t('fba.uploadPic') }}</el-button>
                          </el-upload>
                        </div>
                        <el-input v-else  v-model="scope.row[item.prop]" :disabled="item.prop === 'packageCustomerNo'"></el-input>
                      </div>
                    </el-form-item>
                </template>
              </el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                <template slot="header" slot-scope="scope">
                  <span>{{$t('handle')}}</span>
                  <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  </el-tooltip>
                </template>
                <template slot-scope="scope">
                  <el-link  :underline='false' @click="saveRow(scope, scope.row.update ? 'update' : 'save' )" :class="scope.row.update ? 'el-link--warning' : 'el-link--default'">
      <!--              <label :class="scope.row.update ? 'warning' : 'primary'">-->
                    {{ scope.row.update ? $t('save') : $t('update') }}
      <!--              </label>-->
                  </el-link>
                  <el-link v-if="declareForm.dataList.length > 1" :underline='false' type="danger" @click="deleteRow(scope.row)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
            </el-form>
              <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
              </el-pagination>
          </div>
        </el-tab-pane>

        <!-- 第二个标签页：添加申报明细 -->
        <el-tab-pane label="添加申报明细" name="new">
          <el-form
            :model="newDeclareForm"
            ref="newDeclareForm"
            label-width="120px"
            :rules="newDeclareRules">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="中文名称" prop="chineseName">
                  <el-input v-model="newDeclareForm.chineseName" placeholder="请输入中文名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="英文名称" prop="englishName">
                  <el-input v-model="newDeclareForm.englishName" placeholder="请输入英文名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数量" prop="quantity">
                  <el-input v-model="newDeclareForm.quantity" placeholder="请输入数量"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单价" prop="unitDeclarePriceD">
                  <el-input v-model="newDeclareForm.unitDeclarePriceD" placeholder="请输入单价"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="HS编码" prop="hsCode">
                  <el-input v-model="newDeclareForm.hsCode" placeholder="请输入HS编码"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位净重" prop="unitNetWeightD">
                  <el-input v-model="newDeclareForm.unitNetWeightD" placeholder="请输入单位净重"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品牌" prop="brand">
                  <el-input v-model="newDeclareForm.brand" placeholder="请输入品牌"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品条码" prop="goodsBarcode">
                  <el-input v-model="newDeclareForm.goodsBarcode" placeholder="请输入商品条码"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="SKU" prop="sku">
                  <el-input v-model="newDeclareForm.sku" placeholder="请输入SKU"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品型号" prop="productModel">
                  <el-input v-model="newDeclareForm.productModel" placeholder="请输入产品型号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="材质" prop="material">
                  <el-input v-model="newDeclareForm.material" placeholder="请输入材质"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用途" prop="purpose">
                  <el-input v-model="newDeclareForm.purpose" placeholder="请输入用途"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="原产地" prop="origin">
                  <el-input v-model="newDeclareForm.origin" placeholder="请输入原产地"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品链接" prop="productUrl">
                  <el-input v-model="newDeclareForm.productUrl" placeholder="请输入产品链接"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="拣货备注" prop="pickingRemark">
                  <el-input
                    v-model="newDeclareForm.pickingRemark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入拣货备注">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDeclareDialog">取消</el-button>
        <el-button type="primary" @click="saveDeclareInfo" :loading="declareLoading">
          保存申报明细
        </el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import { formatterType, gtmToLtm, timestampFormat, numberFormat } from '@/filters/filters'
import {
  isOverLength,
  isPlusInteger2,
  isPlusFloat,
  isDecimal3
} from '@/utils/validate'
export default {
  mixins: [mixinViewModule, listPage],
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        orderId: '',
        waybillId: '',
        subCustomerOrderNo: '',
        customerOrderNo: '',
        waybillNo: '',
        status: '',
        deliveryNo: ''
      },
      waybillDataForm: {
      },
      packageForm: {
        dataList: []
      },
      mixinViewModuleOptions: {
        getDataListURL: '/ws/cominsubwaybill/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      // 能否修改重量和尺寸
      canModifySizeOrWeight: false,
      // 申报明细弹窗相关数据
      declareDialogVisible: false,
      declareActiveTab: 'existing', // 'existing' 或 'new'
      currentPackageScope: null, // 当前操作的箱信息
      // 已有明细数据
      existingDeclareList: [],
      selectedExistingDeclares: [], // 选中的已有明细
      // 新增明细表单
      newDeclareForm: {
        chineseName: '',
        englishName: '',
        quantity: '',
        unitDeclarePriceD: '',
        hsCode: '',
        unitNetWeightD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: '',
        picUrl: ''
      },
      // 暂存的申报明细数据 - 按packageNo分组
      tempDeclareData: {},
      declareLoading: false,
      tableColumns: [
        { type: '', width: '10', prop: 'id', label: this.$t('comInSubWaybill.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '200', prop: 'waybillId', label: this.$t('wsComInSubWaybill.waybillId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '200', prop: 'packageNo', label: this.$t('wsComInSubWaybill.packageNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'subCustomerOrderNo', label: this.$t('coOrder.subCustomerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'lengthD', label: this.$t('wsComInSubWaybill.length'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'widthD', label: this.$t('wsComInSubWaybill.width'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'heightD', label: this.$t('wsComInSubWaybill.height'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'weightD', label: this.$t('wsComInSubWaybill.weight'), align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    declareRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        if (value && value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      // const repeatBoxNoSKU = (rule, value, callback) => {
      //   if (!value) {
      //     return callback()
      //   }
      //   let sameBoxNoList = this.packageForm.dataList.filter(item => item.packageNo === value)
      //     .map(item => item.sku).filter(item => typeof item !== 'undefined' && item !== '')
      //   if (sameBoxNoList.length === 1) {
      //     return callback()
      //   }
      //   callback()
      // }
      return {
        packageNo: [
          { required: false, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' }
          // { validator: repeatBoxNoSKU, trigger: 'blur' }
        ],
        widthD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        lengthD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        heightD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        weightD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ]
      }
    },
    // 新申报明细表单验证规则
    newDeclareRules () {
      const isLength64 = (rule, value, callback) => {
        if (value && !isOverLength(value, 64)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 64 })))
        }
        callback()
      }
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isLength32 = (rule, value, callback) => {
        if (value && !isOverLength(value, 32)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 32 })))
        }
        callback()
      }
      const isLength255 = (rule, value, callback) => {
        if (value && !isOverLength(value, 255)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 255 })))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        if (value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        if (value && value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }

      return {
        englishName: [
          { required: true, message: '英文名称必填', trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        chineseName: [
          { validator: isLength64, trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '数量必填', trigger: 'blur' },
          { validator: isInteger, trigger: 'blur' }
        ],
        unitDeclarePriceD: [
          { validator: isFloat, trigger: 'blur' }
        ],
        unitNetWeightD: [
          { validator: isFloat, trigger: 'blur' }
        ],
        hsCode: [
          { validator: isLength36, trigger: 'blur' }
        ],
        brand: [
          { validator: isLength32, trigger: 'blur' }
        ],
        goodsBarcode: [
          { validator: isLength32, trigger: 'blur' }
        ],
        sku: [
          { validator: isLength32, trigger: 'blur' }
        ],
        productModel: [
          { validator: isLength32, trigger: 'blur' }
        ],
        material: [
          { validator: isLength64, trigger: 'blur' }
        ],
        purpose: [
          { validator: isLength64, trigger: 'blur' }
        ],
        origin: [
          { validator: isLength32, trigger: 'blur' }
        ],
        productUrl: [
          { validator: isLength255, trigger: 'blur' }
        ],
        pickingRemark: [
          { validator: isLength255, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    init (orderId, waybillId) {
      this.visible = true
      this.canModifySizeOrWeight = false
      this.dataForm.orderId = orderId
      this.dataForm.waybillId = waybillId
      this.$nextTick(() => {
        new Promise(this.getOrder).then(() => this.queryPageByParam()).finally(() => {
          this.$refs.packageForm.clearValidate()
        })
      })
    },
    getSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (column.property === 'weightD') {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] = sums[index].toFixed(3)
          } else {
            sums[index] = ''
          }
        } else if (column.property === 'serialNo') {
          sums[index] = '合计'
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    // 禁止修改的属性
    disabledColumn (property) {
      console.log('canModifySizeOrWeight', this.canModifySizeOrWeight)
      if (property === 'lengthD' || property === 'widthD' || property === 'heightD' || property === 'weightD') {
        return this.canModifySizeOrWeight
      } else return false
    },
    getOrder (resolve, reject) {
      this.$http.get(`/ws/comwaybill/${this.dataForm.waybillId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return reject(this.$message.error(res.msg))
        }
        this.waybillDataForm = {
          ...this.waybillDataForm,
          ...res.data
        }
        // 如果已获取尾程单号, 则不允许修改重量和尺寸
        // if (this.waybillDataForm.deliveryNode !== 10) {
        //   this.canModifySizeOrWeight = true
        // }
      }).catch(() => {}).finally(() => resolve())
    },
    $getDataListCallback () {
      // 查询列表的回调方法
      this.dataList.forEach((item) => {
        this.$set(item, 'update', false)
      })
      this.packageForm.dataList = this.dataList
      return false
    },
    async getBaseData () {
    },
    // 删除箱明细
    deleteRow (id) {
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('delete') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        let reqObj = { id: id, waybillId: this.dataForm.waybillId }
        this.$http.post(`/ws/cominsubwaybill/deletePackageInfo`, reqObj).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$nextTick(() => {
              // this.$set(scope.row, 'update', !scope.row.update)
                this.packageForm.dataList = this.packageForm.dataList.filter(item => item.id !== id)
              })
            }
          })
        }).catch(() => {
        })
      })
    },
    // 增加一行箱明细
    addRow () {
      let dataList = this.packageForm.dataList
      let packageNo = this.waybillDataForm.customerOrderNo + '-' + (dataList.length + 1)
      let item = { ...dataList[0] }
      item.update = true
      item.id = ''
      item.packageNo = packageNo
      item.subCustomerOrderNo = ''
      item.lengthD = ''
      item.widthD = ''
      item.heightD = ''
      item.weightD = ''
      this.packageForm.dataList.push(item)

      // 初始化该箱的申报明细暂存数据
      if (!this.tempDeclareData[packageNo]) {
        this.tempDeclareData[packageNo] = []
      }
    },
    // 申报明细管理
    declareInfoHandle (scope) {
      console.log('打开申报明细管理弹窗', scope.row)
      this.currentPackageScope = scope
      this.declareDialogVisible = true
      this.declareActiveTab = 'existing'

      // 加载其他箱的申报明细数据
      this.loadExistingDeclareList()

      // 重置新增表单
      this.resetNewDeclareForm()
    },

    // 加载其他箱的申报明细数据
    loadExistingDeclareList () {
      const currentPackageNo = this.currentPackageScope.row.packageNo

      // 调用API获取当前运单下其他箱的申报明细
      this.$http.get(`co/orderdeclare/page/${this.dataForm.orderId}`).then(({ data: res }) => {
        if (res.code === 0 && res.data) {
          // 过滤掉当前箱的申报明细，只显示其他箱的
          this.existingDeclareList = res.data.filter(item =>
            item.packageCustomerNo && item.packageCustomerNo !== currentPackageNo
          )
          console.log('加载的其他箱申报明细:', this.existingDeclareList)
        } else {
          this.existingDeclareList = []
          console.log('未找到其他箱的申报明细数据')
        }
      }).catch(error => {
        console.error('加载申报明细失败:', error)
        this.existingDeclareList = []
        this.$message.error('加载申报明细数据失败')
      })
    },

    // 重置新增申报明细表单
    resetNewDeclareForm () {
      this.newDeclareForm = {
        chineseName: '',
        englishName: '',
        quantity: '',
        unitDeclarePriceD: '',
        hsCode: '',
        unitNetWeightD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: '',
        picUrl: ''
      }
      this.$nextTick(() => {
        if (this.$refs.newDeclareForm) {
          this.$refs.newDeclareForm.clearValidate()
        }
      })
    },

    // 标签页切换处理
    handleTabClick (tab) {
      console.log('切换到标签页:', tab.name)
      if (tab.name === 'new') {
        this.resetNewDeclareForm()
      } else if (tab.name === 'existing') {
        this.selectedExistingDeclares = []
      }
    },

    // 已有明细选择变化处理
    handleExistingSelectionChange (selection) {
      this.selectedExistingDeclares = selection
      console.log('选中的已有明细:', selection)
    },

    // 保存申报明细
    saveDeclareInfo () {
      const packageNo = this.currentPackageScope.row.packageNo

      if (this.declareActiveTab === 'existing') {
        // 保存选中的已有明细
        if (this.selectedExistingDeclares.length === 0) {
          this.$message.warning('请选择要填充的申报明细')
          return
        }

        this.declareLoading = true

        // 将选中的明细数据暂存到前端
        const declareData = this.selectedExistingDeclares.map(item => ({
          ...item,
          packageCustomerNo: packageNo, // 更新为当前箱号
          id: '', // 清空ID，作为新数据
          isFromExisting: true // 标记来源
        }))

        this.tempDeclareData[packageNo] = declareData

        this.$message.success(`已选择 ${declareData.length} 条申报明细，将在保存箱信息时一并提交`)
        this.closeDeclareDialog()
      } else if (this.declareActiveTab === 'new') {
        // 保存新增的申报明细
        this.$refs.newDeclareForm.validate((valid) => {
          if (!valid) {
            this.$message.error('请检查表单输入')
            return
          }

          this.declareLoading = true

          // 将新增的明细数据暂存到前端
          const declareData = [{
            ...this.newDeclareForm,
            packageCustomerNo: packageNo,
            id: '',
            isFromNew: true // 标记来源
          }]

          // 如果已有暂存数据，则追加；否则创建新数组
          if (this.tempDeclareData[packageNo]) {
            this.tempDeclareData[packageNo].push(...declareData)
          } else {
            this.tempDeclareData[packageNo] = declareData
          }

          this.$message.success('申报明细已添加，将在保存箱信息时一并提交')
          this.closeDeclareDialog()
        })
      }
    },

    // 关闭申报明细弹窗
    closeDeclareDialog () {
      this.declareLoading = false
      this.declareDialogVisible = false
      this.currentPackageScope = null
      this.selectedExistingDeclares = []
      this.resetNewDeclareForm()
    },
    saveRow (scope, action) {
      if (action === 'update') {
        let arr = [
          'dataList.' + scope.$index + '.id',
          'dataList.' + scope.$index + '.waybillId',
          'dataList.' + scope.$index + '.packageNo',
          'dataList.' + scope.$index + '.lengthD',
          'dataList.' + scope.$index + '.widthD',
          'dataList.' + scope.$index + '.heightD',
          'dataList.' + scope.$index + '.weightD'
        ]
        let validatedMsgList = []
        this.$refs.packageForm.validateField(arr, (error) => {
          if (error) {
            validatedMsgList.push(error)
          }
        })

        if (validatedMsgList.every((item) => item === '')) {
          let reqObj = Object.assign(scope.row, { waybillId: this.dataForm.waybillId })
          if (!reqObj.weightD) {
            return this.$message.error('实重必填')
          }

          // 验证申报明细：对于新增的箱信息，必须有申报明细
          const packageNo = reqObj.packageNo
          const hasDeclareInfo = this.tempDeclareData[packageNo] && this.tempDeclareData[packageNo].length > 0

          if (!reqObj.id && !hasDeclareInfo) {
            return this.$message.error('需填写申报明细')
          }

          // 如果有暂存的申报明细数据，一并提交
          if (hasDeclareInfo) {
            reqObj.declareInfoList = this.tempDeclareData[packageNo]
            console.log('提交的申报明细数据:', reqObj.declareInfoList)
          }

          let method = reqObj.id ? 'updatePackageInfo' : 'addPackageInfo'
          this.$http.post(`/ws/cominsubwaybill/${method}`, reqObj).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }

            // 保存成功后清除暂存的申报明细数据
            if (this.tempDeclareData[packageNo]) {
              delete this.tempDeclareData[packageNo]
            }

            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.$nextTick(() => {
                  this.$set(scope.row, 'update', !scope.row.update)
                })
              }
            })
          }).catch(() => {
          })
        }
      } else {
        this.$nextTick(() => {
          this.$set(scope.row, 'update', !scope.row.update)
        })
      }
    },
    // 取消
    cancelFn () {
      // 清理暂存的申报明细数据
      this.tempDeclareData = {}
      this.closeDeclareDialog()
      this.visible = false
      this.$emit('modifyPackageInfoAfterHandle')
    },

    // 检查箱是否有暂存的申报明细
    hasTemporaryDeclareInfo (packageNo) {
      return this.tempDeclareData[packageNo] && this.tempDeclareData[packageNo].length > 0
    },

    // 获取暂存申报明细数量
    getTemporaryDeclareCount (packageNo) {
      return this.hasTemporaryDeclareInfo(packageNo) ? this.tempDeclareData[packageNo].length : 0
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    notAddRedStar (h, { column }) {
      return [h('span', ' ' + column.label)]
    },
    indexMethod (index) {
      if (!this.dataList || this.dataList.length <= 0) {
        return index
      }
      return (this.page - 1) * this.limit + index + 1
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    numberFormat,
    timestampFormat
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep #declareTable .el-form-item  {
    margin-bottom: 0px !important;
  }

  // 申报明细弹窗样式
  .declare-tip {
    margin-bottom: 15px;
  }

  .no-data-tip {
    text-align: center;
    padding: 40px 0;
  }

  .dialog-footer {
    text-align: right;

    .el-button + .el-button {
      margin-left: 12px;
    }
  }

  // 申报明细表单样式优化
  ::v-deep .el-tabs__content {
    padding-top: 20px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 18px;
  }

  ::v-deep .el-form-item__label {
    font-weight: 600;
    color: #303133;
  }

  // 表格样式优化
  ::v-deep .el-table {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #303133;
        font-weight: 600;
      }
    }
  }

  // 申报明细徽章样式
  .declare-badge {
    ::v-deep .el-badge__content {
      background-color: #67C23A;
      border-color: #67C23A;
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    ::v-deep .el-form {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
</style>
