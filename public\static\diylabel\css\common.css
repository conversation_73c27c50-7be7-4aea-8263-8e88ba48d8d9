/*
* @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
* @Date:   2016-05-25 20:54:41
* @Last Modified by:   Administrator
* @Last Modified time: 2016-12-16 10:30:30
*/

/*reset&global*/
html, body, div, span, iframe, ul, li, ol, li, form, img
h1, h2, h3, h4, h5, h6, p, i {margin:0;padding:0;border:0;outline:none;list-style:none;font-style:normal;}
body,html{font-family: 'Microsoft YaHei','arial';font-size:12px;}
a,a:hover,a:active,a:visited,a:focus{text-decoration:none!important;outline:none;}
h1, h2, h3, h4, h5, h6{color: #000;font-family: inherit;font-weight: 500;line-height: 1.5;}

.clear:after {
	content:'';
	display:block;
	height:0;
	visibility:hidden;
	clear:both;
}
.clear {
	zoom:1;
}
.font-w {
	font-weight:bold;
}
.p-e-none {
	pointer-events:none;
}
.bor-n {
	border:none!important;
}
.bor-t-n {
	border-top:none!important;
}
.bor-r-n {
	border-right:none!important;
}
.bor-b-n {
	border-bottom:none!important;
}
.bor-l-n {
	border-left:none!important;
}
.out-n {
	outline:none!important;
}
.fl {
	float:left!important;
}
.fr {
	float:right!important;
}
.fn {
	float:none!important;
}
.oh {
	overflow:hidden!important;
}
.disb {
	display:block!important;
}
.disn {
	display:none!important;
}
.disib {
	display:inline-block!important;
}
.tal {
	text-align:left!important;
}
.tar {
	text-align:right!important;
}
.tac {
	text-align:center!important;
}
.taj {
	text-align:justify!important;
}
.vat {
	vertical-align:top!important;
}
.vam {
	vertical-align:middle!important;
}
.vab {
	vertical-align:bottom!important;
}
.ti2em {
	text-indent:2em!important;
}
.dashed {
	border-style:dashed;
}
.nowrap {
	white-space:nowrap!important;
	word-break:normal!important;
}
.cur-p {
	cursor:pointer!important;
}
.cur-d {
	cursor:default!important;
}
.posr {
	position:relative!important;
}
.posa {
	position:absolute!important;
}
.posf {
	position:fixed!important;
}
.m0 {
	margin:0!important;
}
.m5 {
	margin:5px!important;
}
.m10 {
	margin:10px!important;
}
.m15 {
	margin:15px!important;
}
.m20 {
	margin:20px!important;
}
.m25 {
	margin:25px!important;
}
.mt0 {
	margin-top:0px!important;
}
.mt5 {
	margin-top:5px!important;
}
.mt10 {
	margin-top:10px!important;
}
.mt15 {
	margin-top:15px!important;
}
.mt20 {
	margin-top:20px!important;
}
.mt25 {
	margin-top:25px!important;
}
.mt30 {
	margin-top:30px!important;
}
.mt50 {
	margin-top:50px!important;
}
.mt75 {
	margin-top:75px!important;
}
.mt85 {
	margin-top:85px!important;
}
.mt100 {
	margin-top:100px!important;
}
.ml0 {
	margin-left:0px!important;
}
.ml5 {
	margin-left:5px!important;
}
.ml10 {
	margin-left:10px!important;
}
.ml15 {
	margin-left:15px!important;
}
.ml20 {
	margin-left:20px!important;
}
.ml25 {
	margin-left:25px!important;
}
.ml30 {
	margin-left:30px!important;
}
.ml35 {
	margin-left:35px!important;
}
.ml45 {
	margin-left:45px!important;
}
.mb0 {
	margin-bottom:0px!important;
}
.mb5 {
	margin-bottom:5px!important;
}
.mb10 {
	margin-bottom:10px!important;
}
.mb15 {
	margin-bottom:15px!important;
}
.mb20 {
	margin-bottom:20px!important;
}
.mb25 {
	margin-bottom:25px!important;
}
.mb50 {
	margin-bottom:50px!important;
}
.mr0 {
	margin-right:0px!important;
}
.mr5 {
	margin-right:5px!important;
}
.mr10 {
	margin-right:10px!important;
}
.mr15 {
	margin-right:15px!important;
}
.mr20 {
	margin-right:20px!important;
}
.mr25 {
	margin-right:25px!important;
}
.mr60 {
	margin-right:60px!important;
}
.mr5_ {
	margin-right:5%!important;
}
.p0 {
	padding:0px!important;
}
.p5 {
	padding:5px!important;
}
.p10 {
	padding:10px!important;
}
.p15 {
	padding:15px!important;
}
.p20 {
	padding:20px!important;
}
.p25 {
	padding:25px!important;
}
.pt0 {
	padding-top:0px!important;
}
.pt3 {
	padding-top:3px!important;
}
.pt5 {
	padding-top:5px!important;
}
.pt10 {
	padding-top:10px!important;
}
.pt15 {
	padding-top:15px!important;
}
.pt20 {
	padding-top:20px!important;
}
.pt25 {
	padding-top:25px!important;
}
.pt40 {
	padding-top:40px!important;
}
.pl0 {
	padding-left:0px!important;
}
.pl5 {
	padding-left:5px!important;
}
.pl10 {
	padding-left:10px!important;
}
.pl15 {
	padding-left:15px!important;
}
.pl20 {
	padding-left:20px!important;
}
.pl25 {
	padding-left:25px!important;
}
.pl30 {
	padding-left:30px!important;
}
.pl50 {
	padding-left:50px!important;
}
.pl60 {
	padding-left:60px!important;
}
.pb0 {
	padding-bottom:0px!important;
}
.pb5 {
	padding-bottom:5px!important;
}
.pb10 {
	padding-bottom:10px!important;
}
.pb15 {
	padding-bottom:15px!important;
}
.pb20 {
	padding-bottom:20px!important;
}
.pb25 {
	padding-bottom:25px!important;
}
.pb40 {
	padding-bottom:40px!important;
}
.pr0 {
	padding-right:0px!important;
}
.pr5 {
	padding-right:5px!important;
}
.pr10 {
	padding-right:10px!important;
}
.pr15 {
	padding-right:15px!important;
}
.pr20 {
	padding-right:20px!important;
}
.pr25 {
	padding-right:25px!important;
}
.t10 {
	top:10px;
}
.lh20 {
	line-height:20px!important;
}
.lh30 {
	line-height:30px!important;
}
.fs-12 {
	font-size:12px!important;
}
.fs-13 {
	font-size:13px!important;
}
.fs-14 {
	font-size:14px!important;
}
.fs-15 {
	font-size:15px!important;
}
.fs-16 {
	font-size:16px!important;
}
.fs-17 {
	font-size:17px!important;
}
.fs-18 {
	font-size:18px!important;
}
.fs-19 {
	font-size:19px!important;
}
.fs-20 {
	font-size:20px!important;
}
.fs-24 {
	font-size:24px!important;
}
.lo-w-5 {
	width:5%!important;
}
.lo-w-10 {
	width:10%!important;
}
.lo-w-15 {
	width:15%!important;
}
.lo-w-20 {
	width:20%!important;
}
.lo-w-25 {
	width:25%!important;
}
.lo-w-30 {
	width:30%!important;
}
.lo-w-35 {
	width:35%!important;
}
.lo-w-40 {
	width:40%!important;
}
.lo-w-45 {
	width:45%!important;
}
.lo-w-50 {
	width:50%!important;
}
.lo-w-55 {
	width:55%!important;
}
.lo-w-60 {
	width:60%!important;
}
.lo-w-65 {
	width:65%!important;
}
.lo-w-70 {
	width:70%!important;
}
.lo-w-75 {
	width:75%!important;
}
.lo-w-80 {
	width:80%!important;
}
.lo-w-85 {
	width:85%!important;
}
.lo-w-90 {
	width:90%!important;
}
.lo-w-95 {
	width:95%!important;
}
.lo-w-100 {
	width:100%!important;
}
.lo-wpx-150 {
	width:150px!important;
}
.lo-wpx-200 {
	width:200px!important;
}
.lo-wpx-250 {
	width:250px!important;
}
.lo-wpx-345 {
	width:345px!important;
}
.lo-h-35 {
	height:35px!important;
}
.lo-h-75 {
	height:75px!important;
}
.lo-h-80 {
	height:80px!important;
}
.lo-h-85 {
	height:85px!important;
}
.lo-h-95 {
	height:95px!important;
}
.lo-h-100 {
	height:100px!important;
}
.lo-h-200 {
	height:200px!important;
}
.lo-lh-20 {
	line-height: 20px!important;
}
.lo-lh-25 {
	line-height: 25px!important;
}
.lo-lh-30 {
	line-height: 30px!important;
}
.lo-lh-35 {
	line-height: 35px!important;
}
.lo-minh-1100 {
	min-height:1100px!important;
}
.b-rad {
	border-radius:100%;
}
.bor-n {
	border:none;
}
.select-none {
	-webkit-user-select:none;
	-moz-user-select:none;
	-o-user-select:none;
	-ms-user-select:none;
	user-select:none;
}
/*******************bootstro**********/
.bootstro-noclick.bootstro-highlight {
	pointer-events:none;
}
/*********************link********************/
.lo-a {
	color: #333 !important;
	-webkit-transition: all 0.15s;
	-moz-transition: all 0.15s;
	-o-transition: all 0.15s;
	-ms-transition: all 0.15s;
	transition: all 0.15s;
}
.lo-a-white {
	color:#fff!important;
}
.lo-a-primary {
	color: #2D9FFF !important;
}
.lo-a-default {
	color:#333!important;
	-webkit-transition:none!important;
	-moz-transition:none!important;
	-o-transition:none!important;
	-ms-transition:none!important;
	transition:none!important;
}
.lo-a:hover,
.lo-a:active {
	color: #2D9FFF !important;
}
.lo-a-radio {
	position: relative;
	text-align:center;
	padding:5px 8px;
}
.lo-a-radio .radio {
	position:absolute;
	left:0;
	right:0;
	bottom:0;
	top:0;
	width:100%;
	height:100%;
	z-index:2;
	cursor:pointer;
	opacity:0;
}
.lo-a-radio .radio:checked + .radio-title {
	color: #2D9FFF;
}
.lo-a-radio .radio-title {
	display: inline-block;
	color:#333;
}
.lo-return {
	width:60px;
	height:60px;
	line-height:60px;
	background:rgba(45,159,255,.1);
	border-radius:100%!important;
	position:fixed;
	top:130px;
	right:40px;
	font-size:12px;
	font-weight: bold;
	color:#2D9FFF!important;
	text-align:center;
	box-shadow: 0 0 10px rgba(45,159,255,.4);
}
.lo-return:hover {
	background:rgba(45,159,255,.13);
}
.lo-return:active {
	top:131px;
}
.lo-well {
	padding: 15px 0;
	border: 1px solid #ddd;
	min-height: 100px;
}
.lo-position {
	padding-top: 20px;
	margin-bottom: 10px;
}
.lo-type-group {
	font-size: 12px;
	margin-bottom: 10px;
}
.lo-type-group .item {
	position: relative;
	display: inline-block;
}
.lo-type-group .item .radio {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
}
.lo-type-group .item .radio:checked + .text {
	background-color: #546478;
	color: #fff;
}
.lo-type-group .item .text {
	display: inline-block;
	width: 115px;
	height: 32px;
	line-height: 32px;
	background-color: #D9DEE4;
	color: #333;
	text-align: center;
}
.lo-search-fields {
	font-size: 12px;
}
.lo-search-fields .min-w-130 {
	min-width: 130px;
}
.lo-search-fields .max-w-70 {
	max-width: 70px;
}
.lo-search-fields label {
	font-weight:normal;
}
.lo-search-fields .more-search {
	position:relative;
	margin-left:15px;
}
.lo-search-fields .more-search .title {
	display:inline-block;
	height:35px;
	line-height:35px;
	vertical-align:top;
}
.lo-search-fields .more-search .title:after {
	margin-left:5px;
	content:"\f107";
	font:bold normal normal 14px/1 FontAwesome;
	color:#333;	
}
.lo-search-fields .more-search .checkbox {
	position:absolute;
	left:0; 
	top:0;
	width:100%;
	height:35px;						
	opacity:0;
	cursor:pointer;
}
.lo-search-fields .more-search .checkbox:checked ~ .title:after {
	content:"\f106";
}
.lo-search-fields .more-search .checkbox:checked ~ .content {
	display:block;
}
.lo-search-fields .more-search .content {
	margin-top:15px;
	padding:10px 0;
	border-top:1px dashed #ddd;
	border-bottom:1px dashed #ddd;
	display:none;
	width:868px;
}
.lo-search-fields .hlsAreaPanel {
	max-height: 350px;
}

.lo-batch-handle {
	font-size: 12px;
	padding: 15px 0;
}
.lo-batch-handle:after {
	content:'';
	display:block;
	height:0;
	visibility:hidden;
	clear:both;
}
.lo-section-title {
	padding: 12px 0;
	border-bottom: 1px solid #ddd;
	margin-bottom: 25px;
	font-size:16px;
}
.lo-section-title .handle {
	font-weight: normal;
	font-size:12px;
}
.lo-step {
	text-align: center;
	font-size: 0;
	padding: 50px 0;
}
.lo-step .item {
	display: inline-block;
	width: 15vw;
	text-align: center;
}
.lo-step .item .line {
	background-color: #DDDDDD;
	height: 7px;
	margin-bottom: 15px;
	position: relative;
	border-right: 1px solid #fff;
}
.lo-step .item .number {
	position: absolute;
	left: 50%;
	top: 50%;
	width: 28px;
	height: 28px;
	line-height: 28px;
	border-radius: 100%;
	background-color: #DDDDDD;
	margin-left: -14px;
	margin-top: -14px;
	text-align: center;
	color: #fff;
	font-size: 14px;
	font-weight: bold;
	box-shadow: 0 0 3px;
}
.lo-step .item .text {
	font-size: 13px;
	color: #DDDDDD;
}
.lo-step .active .line,
.lo-step .active .number {
	background-color: #2D9FFF;
}
.lo-step .active .text {
	color: #2D9FFF;
}
.lo-gather .item {
	width:30%;
	height:5.5vw;
	min-height:70px;
	margin-right:5%;
	box-sizing:border-box;
	box-shadow:1px 1px 5px rgba(0,0,0,.1);
	margin-bottom:15px;
}
.lo-gather .item:nth-last-child(1) {
	margin-right:0;
}
.lo-gather .item>div {
	box-sizing:border-box;
	height:100%;
}
.lo-gather .item .icon {
	width:40%;
	background-color: #7C97F5;
	border:1px solid #7C97F5;
	font-size:25px;
	font-weight:bold;
	color:#fff;
	text-align:center;
	position: relative;
	border-radius:2px 0 0 2px;
	line-height: 5.5vw;
	vertical-align: middle;
}
.lo-gather .item:nth-child(2) .icon {
	background-color: #3CC9FB;
	border:1px solid #3CC9FB;
}
.lo-gather .item:nth-child(3) .icon {
	background-color: #68D1C7;
	border:1px solid #68D1C7;
}
.lo-gather .item:nth-child(4) .icon {
	background-color: #f15b6c;
	border:1px solid #f15b6c;
}
.lo-gather .item:nth-child(5) .icon {
	background-color: #826858;
	border:1px solid #826858;
}
.lo-gather .item:nth-child(6) .icon {
	background-color: #2D9FFF;
	border:1px solid #2D9FFF;
}
.lo-gather .item:nth-child(2) .num {
	border-color:#3CC9FB;
}
.lo-gather .item:nth-child(3) .num {
	border-color:#68D1C7;
}
.lo-gather .item:nth-child(4) .num {
	border-color:#f15b6c;
}
.lo-gather .item:nth-child(5) .num {
	border-color:#826858;
}
.lo-gather .item:nth-child(6) .num {
	border-color:#2D9FFF;
}
.lo-gather .item:nth-child(3n) {
	margin-right:0;
}
.lo-gather .item .num {
	width:60%;
	background:#fff;
	border:1px solid #7C97F5;
	border-left:none;
	border-radius:0 2px 2px 0;
	padding:5px;
	text-align:center;
}
.lo-gather .item .num p {
	color:#999;
	text-align:left;
	margin-bottom:5px;
}
.lo-gather .item .num span {
	color:#333;
	font-weight:bold;
	font-size:20px;
}
.lo-message + .popover {
	font-size:12px;
	color:#333;
	min-width:300px;
	min-height:70px;
	box-shadow:0 2px 5px rgba(0, 0, 0, .1);
	border-color:#ddd;
}
.lo-message + .popover.bottom > .arrow {
	border-bottom-color:#ddd;
}
#lo-tooltip {
	position:fixed;
	right:5px;
	bottom:30%;
	width:40px;
}
#lo-tooltip .item {
	width:40px;
	height:40px;
	margin-bottom:5px;
	position:relative;
	cursor:pointer;
	box-shadow:0 0 5px #2D9FFF;
}
#lo-tooltip .item.to-top {
	display:none;
}
#lo-tooltip .item:hover .title {
	z-index: 3;
}
#lo-tooltip .item>span {
	position:absolute;
	width:40px;
	height:40px;
	text-align:center;
	background-color:#2D9FFF;
	color:#fff;
}
#lo-tooltip .item .icon {
	z-index: 2;
	line-height:40px;
	font-size:15px;
}
#lo-tooltip .item .title {
	z-index: 1;
	width:40px;
	height:40px;
	font-size:12px;
	font-weight:bold;
	border:1px solid #2D9FFF;
	background-color:#fff;
	color:#2D9FFF;
	padding:3px;
	box-sizing:border-box;
}
.lo-news .lo-news-item {
	display:block;
	font-size:12px;
	line-height:35px;
	border-bottom:1px dashed #eee;
}
.lo-news .read {
	font-weight: bold;
}
.lo-news .noRead {
	position: relative;
}
.lo-news .noRead:before {
	content:'';
	position: absolute;
	left:-10px;
	top:50%;
	margin-top:-2.5px;
	width:5px;
	height:5px;
	border-radius:100%;
	background-color: #2D9FFF;
	box-shadow:0 0 5px #2D9FFF;
}

.lo-bootstro-open {
	position:absolute;
	right:10px;
	top:12px;
	font-size:12px;
	border-radius:3px;
	padding:3px!important;
	background-color:#2D9FFF;
	color:#fff;
	cursor:pointer;
	box-shadow:1px 1px 5px rgba(45,159,255,.3);
}

/**********************button******************/
.lo-btn {
	padding: 6px 12px;
	height:40px;
	margin-bottom: 0;
	font-size: 13px;
	font-weight: normal;
	display: inline-block;
	text-align: center;
	vertical-align: middle;
	background-color: #ccc;
	border: 1px solid transparent;
	color: #333;
	cursor: pointer;
	outline: none;
	white-space: nowrap;
	border-radius:1px!important;
}
.lo-btn:active {
	position:relative;
	top:1px;
}
.lo-btn-sm {
	padding: 4px 8px;
	font-size: 12px;
	line-height: 1.5;
	height:30px;
}
.lo-btn-xs {
	padding:2px 3px;
	font-size:12px;
	height:24px;
}
.lo-btn-m {
	height:35px;
}
.lo-btn-lg {
	padding: 10px 40px;
	font-size: 15px;
	height:46px;
	line-height: 1.5;
}
.lo-btn-hollow {
	border:1px solid #2D9FFF;
	color:#2D9FFF;
	background:#fff;
}
.lo-btn-default {
	background-color: #fff;
	border: 1px solid #ddd;
	color: #333;
}
.lo-btn-default:hover {
	background-color: #f5f5f5;
	border: 1px solid #ccc;
}
.lo-btn.lo-btn-primary {
	background-color: #2D9FFF;
	border: 1px solid #198beb;
	color: #fff;
}
.lo-btn-primary:hover {
	background-color: #198beb;
	border: 1px solid #006dcd;
}
.lo-btn.lo-btn-danger {
	background-color:#d9534f;
	border-color:#d43f3a;
	color:#fff;
}
.lo-btn-danger:hover {
	background-color:#c9302c;
	border-color:#ac2925;
	color:#fff;
}
.lo-btn.disabled {
	cursor: not-allowed;
	opacity: .65;
}
/**********************form*******************/
.lo-form input,
.lo-form select,
.lo-form textarea {
	font-size: 12px;
	border-radius: 2px !important;
	outline:none;
	border:1px solid #ddd;
}
.lo-form input[type='text']:focus,
.lo-form select:focus,
.lo-form textarea:focus {
	border-color: #2D9FFF !important;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.065), 0 0 8px rgba(102, 175, 233, 0.5) !important;
}
.lo-form label {
	font-size: 12px;
}
/********************table***********************/
.lo-table,
.lo-table-p {
	border: 1px solid #ddd;
	font-size: 12px;
}
.lo-table>tbody>tr>td {
	padding:12px 8px!important;
	vertical-align: middle!important;
}
.lo-table-p {
	border-color:#eee;
}
.lo-table-p:hover {
	border-color:#ccc;
	box-shadow: 1px 1px 5px rgba(0,0,0,.05);
}
.lo-table-p>tbody>tr>td {
	border-color:#eee!important;
	vertical-align: middle!important;
}
.lo-table-p>tbody>tr:nth-child(1)>td,
.lo-table>tbody>tr>th {
	background-color: #f5f5f5;
}
.lo-table-p:hover>tbody>tr:nth-child(1)>td {
	border-top-color:#ccc!important;
}
.lo-table-p.checked-bg {
	border-color:#ccc;
	box-shadow: 1px 1px 5px rgba(0,0,0,.05);
}
.lo-table-p.checked-bg>tbody>tr:nth-child(1)>td {
	background:#ddd;
	border-color:#ccc!important;
}
.lo-table .checked-bg-tr {
	background:#eee;
}
.lo-table>tbody>tr>th,
.lo-table-nb>tbody>tr>th {
	vertical-align: middle!important;
}
.lo-table-nb {
	border:none;
	font-size: 12px;
	margin-top:-1px!important;
	vertical-align: middle!important;
}
.lo-table-nb>tbody>tr>th {
	font-weight:normal;
	background:#f5f5f5;
}
.lo-table-nb>tbody>tr>td {
	border:none!important;
	vertical-align: middle!important;
}
.lo-table-bg>tbody>tr>th {
	background: #2D9FFF;
	color:#fff;
	vertical-align: middle!important;
}
.lo-no-tableList {
	text-align:center;
	font-size:15px;
	font-weight: bold;
	padding:50px 0;
}
.lo-table .handle-td {
	border-left:1px solid #fff!important;
	border-right:1px solid #fff!important;
	padding:0px!important;
}
/********************pagination***********************/
.lo-pagination .pagination a {
	color: #333 !important;
	font-size: 12px !important;
	outline: none;
}
.lo-pagination .pagination .active a {
	color: #fff !important;
	background-color: #2D9FFF !important;
	border-color: #2D9FFF !important;
}
.lo-pagination .pagination span {
	height: 31px;
	line-height: 31px;
	padding-top: 0;
	padding-bottom: 0;
}
/********************font-color****************/
.lo-cp {
	color: #2D9FFF!important;
}  
.lo-ch {
	color: #B8B8B8!important;
}
.lo-3 {
	color:#333!important;
}
.lo-white {
	color:#fff!important;
}
/*******************avatar*********************/
.lo-avatar {
	padding:3px;
	box-shadow:0 0 10px rgba(0,0,0,.4);
	border-radius:100%;
	border:none;
	outline:none;
}
.lo-avatar-sm {
	box-shadow:0 0 2px rgba(0,0,0,.8);
	border-radius: 100%;
	border:none;
	outline:none;
}
/*****************top*******************/
.lo-top,
.lo-top-white {
	padding-top:20px!important;
	padding-bottom:20px!important;
	font-size:0;
	background:#2D9FFF;
	color:#fff;
}
.lo-top-white {
	background:#fff;
	color:#333;
}
.lo-top .cover,
.lo-top-white .cover {
	padding:0 10px;
}
.lo-top span,
.lo-top-white span {
	font-size:12px;
	vertical-align:middle;
}
.lo-top a {
	font-size:12px;
	color:#fff!important;
	vertical-align: middle;
}
.lo-top a:hover {
	color:#fff!important;
}
.lo-top-white a {
	font-size:12px;
	color:#333!important;
	vertical-align: middle;
}
/********************nav******************/
.lo-navbar {
	background-color: #2D9FFF!important;
	border-color:#2D9FFF!important;
	box-sizing: border-box;
	height: 62px;
	border-radius:0!important;
}
.lo-navbar .navbar-header {
	line-height:60px;
	color:#fff;
	font-size:0;
	padding-left:0;
	width:20%;
}
.lo-navbar .navbar-collapse {
	padding:0;
}
.lo-navbar .navbar-nav {
	background-color:#2d9fff;
}
.lo-navbar .navbar-nav > li > a {
	height:60px;
	line-height:60px;
	color:#fff!important;
	padding:0 20px;
	background-color:#2d9fff;
}
.lo-navbar .navbar-nav > li > a:hover {
	background-color:rgba(0,0,0,.1)!important;
}
.lo-navbar .brand {
	width: 140px;
	height: 35px;
	vertical-align: middle;
	display: inline-block;
}
.lo-navbar .l-wr {
	line-height: 62px;
	color: #fff;
	font-size: 0;
}
.lo-navbar .nav-wr {
	margin-left:30px;
	margin-bottom:0;
}
/*****************modal****************/
.modalCover {
	-webkit-filter: blur(2px);
	filter: blur(2px);
}
#modalAlert {
	width:100%;
}
.modal .modal-body {
	overflow-x:hidden;
}
/******************csshake**************/
