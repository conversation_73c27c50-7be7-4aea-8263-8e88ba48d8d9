<template>
  <el-dialog :title="$t('wsComBatchOrder.addItem') + '-' + this.dataForm.batchNo" :visible.sync="dialogVisible"
             :close-on-click-modal="false" :close-on-press-escape="false"  :lock-scroll="true" top="30px" width="800px" height="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="108px">
      <el-row v-show="false">
        <el-col :span="24">
          <el-form-item  :label="$t('wsComBatchItem.batchId')" prop="batchId">
            <el-input v-model="dataForm.batchId" clearable ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item  :label="$t('wsComBatchItem.hadInBatch')" prop="hadInBatch">
            <el-input type="textarea" v-model="existsItemList" clearable :rows="5" readonly></el-input>
          </el-form-item>
        </el-col>
        <el-link :underline="false" style="float: right;margin-right: 20px;" type="warning" icon="el-icon-copy-document" @click="clip(existsItemList, $event)" >复制已有单号</el-link>
        <el-link :underline="false" style="float: right;margin-right: 20px;" type="danger" icon="el-icon-delete"  :loading="deleteItemsLoading" v-if="$hasPermission('ws:comBatchOrder:deleteItem')"  @click="deleteItemS(dataForm.batchId)">{{$t('deleteItemsInBatchOrder')}}</el-link>
      </el-row>
      <el-row type="flex">
        <el-col :span="24">
          <el-form-item :label="$t('wsComBatchOrder.addItemType')" prop="addItemType">
            <el-radio-group  v-model="dataForm.addItemType" style="width: 100%">
              <el-radio
                v-for="item in addItemTypeList"
                :key="item.dictValue"
                :label="item.dictValue">
                {{item.dictName}}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" type="flex">
        <el-col :span="24">
          <el-form-item  :label="$t('wsComWaybill.deliveryNo')" prop="deliveryNoS">
            <el-input type="textarea" v-model="dataForm.deliveryNoS" clearable :rows="16"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancelFn">{{this.$t('cancel')}}</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveOrder">{{ $t('confirm') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import clip from '@/utils/clipboard'
export default {
  data () {
    return {
      dialogVisible: false,
      dataForm: {
        addItemType: '',
        batchNo: '',
        batchId: '',
        deliveryNoS: ''
      },
      // 已加入批次的尾程单号列表
      existsItemList: '',
      saveLoading: false,
      deleteItemsLoading: false,
      addItemTypeList: [{ dictValue: 10, dictName: '覆盖' }, { dictValue: 20, dictName: '追加' }]
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
  },
  methods: {
    clip, // 复制函数
    init () {
      this.dialogVisible = true
      this.getBatchOrderItems(this.dataForm.batchId).then(res => {
        this.existsItemList = res
      })
      this.$nextTick(() => {
      })
    },
    /**
     * 查询批次单下的订单信息
     */
    getBatchOrderItems (batchId) {
      let url = '/ws/combatchitem/deliveryNoHadInBatch'
      return new Promise((resolve, reject) => {
        this.$http
          .get(url, {
            params: {
              batchId: batchId
            }
          })
          .then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            res.data = res.data || ''
            resolve(res.data)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    async getBaseData () {
    },
    cancelFn () {
      this.$refs.dataForm.resetFields()
      this.dialogVisible = false
    },
    // 删除明细
    deleteItemS (batchId) {
      this.deleteItemsLoading = true
      this.$confirm(this.$t('prompt.infoMore', { 'handle': this.$t('deleteItemsInBatchOrder'), 'moreinfo': this.$t('deleteItemsInBatchOrderTip') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.post('/ws/combatchorder/deleteItemsByBatchId', 'batchId=' + batchId).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500
          })
          // this.$emit('addItemDialogAfterHandle')
          // this.cancelFn()
        }).catch(() => {
        }).finally(() => {
          this.deleteItemsLoading = false
        })
      }).catch(() => {
        this.deleteItemsLoading = false
      }).finally(() => {
        this.deleteItemsLoading = false
      })
    },
    saveOrder () {
      this.saveLoading = true
      this.dataForm.deliveryNoS = this.dataForm.deliveryNoS.replace(/\n|\s+/g, ',').trim()
      this.$http.post('/ws/combatchorder/saveItem', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.$emit('addItemDialogAfterHandle')
        this.cancelFn()
      }).catch(() => {
      }).finally(() => { this.saveLoading = false })
    }
  },
  computed: {
    dataRule () {
      return {
        deliveryNoS: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        addItemType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        batchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.selectChannelClass{
  .form-card{
    padding: 15px;
  }
  .el-checkbox__label{
    font-size: 12px;
  }
  .el-form-item__label,
  .el-checkbox,
  .el-radio{
    color: #000!important;
  }
  .el-table .success-row {
    background: #09ff00;
  }
}
</style>
