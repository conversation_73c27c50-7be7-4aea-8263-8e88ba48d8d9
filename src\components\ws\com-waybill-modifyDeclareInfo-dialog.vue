<template>
  <el-dialog :visible.sync="visible"  :title="$t('wsComWaybill.modifyDeclareInfo')"  width="90%"  top="10px" :append-to-body="false" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true" class="location_model">
<!--    <div class=" flex_wrap" >-->
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm"  class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="120px">
          <el-row :gutter="2" type="flex">
            <el-col >
              <el-row :gutter="2">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.chineseName')" prop="chineseName">
                    <el-input v-model="dataForm.chineseName" :placeholder="$t('coOrderDeclare.chineseName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.englishName')" prop="englishName">
                    <el-input v-model="dataForm.englishName" :placeholder="$t('coOrderDeclare.englishName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.packageCustomerNo')" prop="packageCustomerNo">
                    <el-input v-model="dataForm.packageCustomerNo" :placeholder="$t('coOrderDeclare.packageCustomerNo')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" prop="goodsBarcode">
                    <el-input v-model="dataForm.goodsBarcode" :placeholder="$t('coOrderDeclare.goodsBarcode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.sku')" prop="sku">
                    <el-input v-model="dataForm.sku" :placeholder="$t('coOrderDeclare.sku')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.hsCode')" prop="hsCode">
                    <el-input v-model="dataForm.hsCode" :placeholder="$t('coOrderDeclare.hsCode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn">
              <el-row>
                <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
                <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
              </el-row>
            </div>
          </el-row>
        </el-form>
      </el-card>
      <el-form :model='declareForm' style="margin-top: 10px"  ref="declareForm" key='9' :inline-message='true'>
      <el-table id="declareTable"  v-loading="dataListLoading" :data="declareForm.dataList"  border @selection-change="dataListSelectionChangeHandle" max-height="410px">
        <!-- 动态显示表格 -->
        <el-table-column :label="this.$t('label.serialNo')" type="index" width='50' :index="indexMethod" fixed="left"></el-table-column>
        <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" :render-header="['quantity','englishName'].indexOf(item.prop) > -1 ? addRedStar: notAddRedStar" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
          <template slot-scope="scope">
              <el-form-item :prop="'dataList.' + scope.$index + '.'+item.prop"  :rules='declareRule[item.prop]'>
                <div v-show='!scope.row.update' >
                  <div v-if="item.prop === 'picUrl'">
                    <el-image v-if='scope.row.picUrl' style="width: 5vw; height: 5vh;" fit="scale-down"  :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" >
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                    <el-image v-else-if='scope.row.picBase64' style="width: 5vw; height: 5vh;" fit="scale-down" :src="scope.row.picBase64" :preview-src-list="[scope.row.picBase64]" >
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                  <span v-else v-text="scope.row[item.prop]"></span>
                </div>
                <div v-show='scope.row.update' >
                  <div v-if="item.prop === 'picUrl'">
                    <div  v-if='scope.row.picUrl'>
                      <el-image style="width: 5vw; height: 5vh;" fit="scale-down"  :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" >
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i  class='el-icon-close ' style='position:absolute; top: 0; color: red;cursor:pointer' @click='picRemoveHandle(scope.row)'></i>
                    </div>
                    <div v-else-if='scope.row.picBase64'>
                      <el-image  style="width: 5vw; height: 5vh;" fit="scale-down" :src="scope.row.picBase64" :preview-src-list="[scope.row.picBase64]" >
                        <div slot="placeholder" class="image-slot">
                          加载中<span class="dot">...</span>
                        </div>
                        <div slot="error" class="image-slot">
                          <i class="el-icon-picture-outline"></i>
                        </div>
                      </el-image>
                      <i  class='el-icon-close ' style='position:absolute; top: 0; color: red;cursor:pointer' @click='picRemoveHandle(scope.row)'></i>
                    </div>
                    <el-upload  action="#" :limit="1" accept='.jpg,.img,.jpeg,.bmp,.png,.gif' :on-change="(file) => picUploadHandle(file, scope.row)"
                                :on-error="errorHandle" :auto-upload='false' :show-file-list='false' :file-list="picFileList" style='display: inline-flex;margin-top: 2px'>
                      <el-button type="text" size="mini" class='margin_left10 margin_right10 ' style='color: #1890ff' slot="trigger" >{{ $t('fba.uploadPic') }}</el-button>
                    </el-upload>
                  </div>
                  <el-input v-else  v-model="scope.row[item.prop]" :disabled="item.prop === 'packageCustomerNo'"></el-input>
                </div>
              </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
          <template slot="header" slot-scope="scope">
            <span>{{$t('handle')}}</span>
            <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-link  :underline='false' @click="saveRow(scope, scope.row.update ? 'update' : 'save' )" :class="scope.row.update ? 'el-link--warning' : 'el-link--default'">
<!--              <label :class="scope.row.update ? 'warning' : 'primary'">-->
              {{ scope.row.update ? $t('save') : $t('update') }}
<!--              </label>-->
            </el-link>
            <el-link v-if="declareForm.dataList.length > 1" :underline='false' type="danger" @click="deleteRow(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      </el-form>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import {
  isOverLength,
  isPlusInteger2,
  isPlusFloat,
  isDecimal3, isURL
} from '@/utils/validate'
export default {
  mixins: [mixinViewModule, listPage],
  data () {
    return {
      visible: false,
      tableHeight2: '',
      declareDataList: [],
      picFileList: [],
      dataForm: {
        orderId: '',
        waybillId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        packageCustomerNo: '',
        unitNetWeightD: '',
        unitDeclarePriceD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: ''
      },
      orderDataForm: {
      },
      declareForm: {
        dataList: []
      },
      mixinViewModuleOptions: {
        getDataListURL: '/co/orderdeclare/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      tableColumns: [
        { type: '', width: '200', prop: 'packageCustomerNo', label: this.$t('coOrderDeclare.packageCustomerNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'chineseName', label: this.$t('coOrderDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'englishName', label: this.$t('coOrderDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'quantity', label: this.$t('coOrderDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'unitDeclarePriceD', label: this.$t('coOrderDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '160', prop: 'hsCode', label: this.$t('coOrderDeclare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitNetWeightD', label: this.$t('coOrderDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'brand', label: this.$t('coOrderDeclare.brand'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'goodsBarcode', label: this.$t('coOrderDeclare.goodsBarcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'sku', label: this.$t('coOrderDeclare.sku'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'productModel', label: this.$t('coOrderDeclare.productModel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'material', label: this.$t('coOrderDeclare.material'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'purpose', label: this.$t('coOrderDeclare.purpose'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'origin', label: this.$t('coOrderDeclare.origin'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'pickingRemark', label: this.$t('coOrderDeclare.pickingRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'productUrl', label: this.$t('coOrderDeclare.productUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'picUrl', label: this.$t('wsComWaybill.declare.picUrl'), align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        if (item.prop === 'unitNetWeightD') {
          item.label = this.$t('fba.unitNetWeight') + '(' + (this.orderDataForm.standardUnit === 1 ? 'LB' : 'KG') + ')'
        }
        if (item.prop === 'unitDeclarePriceD' && this.orderDataForm.declareCurrency) {
          item.label = this.$t('wsComWaybill.declare.unitDeclarePrice') + '(' + this.orderDataForm.declareCurrency + ')'
        }
        return item.isShow
      })
      return arr
    },
    declareRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isLength2 = (rule, value, callback) => {
        if (value && !isOverLength(value, 2)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 2 })))
        }
        callback()
      }
      const isLength255 = (rule, value, callback) => {
        if (value && !isOverLength(value, 255)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 255 })))
        }
        callback()
      }
      const isLength32 = (rule, value, callback) => {
        if (value && !isOverLength(value, 32)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 32 })))
        }
        callback()
      }
      const isLength64 = (rule, value, callback) => {
        if (value && !isOverLength(value, 64)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 64 })))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        if (value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        if (value && value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      const picURL = (rule, value, callback) => {
        if (value && !isURL(value)) {
          return callback(new Error('不是合法的图片地址'))
        }
        callback()
      }
      // const repeatBoxNoSKU = (rule, value, callback) => {
      //   if (!value) {
      //     return callback()
      //   }
      //   let sameBoxNoDiffSKUList = this.declareForm.dataList.filter(item => item.packageCustomerNo === value)
      //     .map(item => item.sku).filter(item => typeof item !== 'undefined' && item !== '')
      //   if (sameBoxNoDiffSKUList.length === 1) {
      //     return callback()
      //   }
      //   let sameBoxNoDiffSKUSet = new Set(sameBoxNoDiffSKUList)
      //   if (sameBoxNoDiffSKUList.length > 1 && sameBoxNoDiffSKUList.length !== sameBoxNoDiffSKUSet.size) {
      //     return callback(new Error('已有相同箱和相同SKU'))
      //   }
      //   callback()
      // }
      // const repeatSKUBoxNo = (rule, value, callback) => {
      //   if (!value) {
      //     callback()
      //   }
      //   let sameSKUDiffBoxNoMap = new Map()
      //   this.declareForm.dataList.filter(item => item.sku === value)
      //     .filter(item => typeof item !== 'undefined')
      //     .map(item => {
      //       if (sameSKUDiffBoxNoMap.has(item.sku)) {
      //         let arr = sameSKUDiffBoxNoMap.get(item.sku)
      //         arr.push(item.packageCustomerNo)
      //         sameSKUDiffBoxNoMap.set(item.sku, arr)
      //       } else {
      //         sameSKUDiffBoxNoMap.set(item.sku, [item.packageCustomerNo])
      //       }
      //     })
      //   console.log('sameSKUDiffBoxNoMap', sameSKUDiffBoxNoMap)
      //   sameSKUDiffBoxNoMap.forEach((val, key) => {
      //     let sameSKUDiffBoxNoSet = new Set(val)
      //     if (val.length !== sameSKUDiffBoxNoSet.size) {
      //       return callback(new Error('已有相同箱和相同SKU'))
      //     }
      //   })
      //   callback()
      // }
      const isValidBoxNo = (rule, value, callback) => {
        if (!value) {
          callback()
        }
        let sameBoxNoList = this.declareForm.dataList.filter(item => item.packageCustomerNo === value)
        if (!sameBoxNoList || sameBoxNoList.length === 0) {
          return callback(new Error('请先录入该箱信息'))
        }
        callback()
      }
      return {
        packageCustomerNo: [
          { required: false, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' },
          { validator: isValidBoxNo, trigger: 'blur' }
          // { validator: repeatBoxNoSKU, trigger: 'blur' }
        ],
        sku: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
          // { validator: repeatSKUBoxNo, trigger: 'blur' }
        ],
        englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        chineseName: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isInteger, trigger: 'blur' }
        ],
        unitDeclarePriceD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        unitNetWeightD: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        hsCode: [
          { validator: isLength36, trigger: 'blur' }
        ],
        origin: [
          { validator: isLength2, trigger: 'blur' }
        ],
        material: [
          { validator: isLength64, trigger: 'blur' }
        ],
        productModel: [
          { validator: isLength32, trigger: 'blur' }
        ],
        purpose: [
          { validator: isLength64, trigger: 'blur' }
        ],
        brand: [
          { validator: isLength32, trigger: 'blur' }
        ],
        productUrl: [
          { validator: isLength255, trigger: 'blur' }
        ],
        picUrl: [
          { validator: picURL, trigger: 'blur' }
        ],
        pickingRemark: [
          { validator: isLength255, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    init (orderId, waybillId) {
      this.visible = true
      this.dataForm.orderId = orderId
      this.dataForm.waybillId = waybillId
      this.$nextTick(() => {
        new Promise(this.getOrder).then(() => this.queryPageByParam()).finally(() => {
          this.$refs.declareForm.clearValidate()
        })
      })
    },
    getOrder (resolve, reject) {
      this.$http.get(`/co/order/${this.dataForm.orderId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return reject(this.$message.error(res.msg))
        }
        this.orderDataForm = {
          ...this.orderDataForm,
          ...res.data
        }
      }).catch(() => {}).finally(() => resolve())
    },
    $getDataListCallback () {
      // 查询列表的回调方法
      this.dataList.forEach((item) => {
        this.$set(item, 'update', false)
      })
      this.declareForm.dataList = this.dataList
      return false
    },
    async getBaseData () {
    },
    picRemoveHandle (row) {
      this.picFileList = []
      this.$nextTick(() => {
        this.$set(row, 'picBase64', '')
        this.$set(row, 'picUrl', '')
      })
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    picUploadHandle (file, row) {
      this.picFileList = []
      console.log('file.name', file.name)
      let regStr = '(.jpg)$|(.img)$|(.jpeg)$|(.bmp)$|(.png)$|(.gif)$'
      let formatTip = '.jpg,.img,.jpeg,.bmp,.png,.gif'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        return this.$message.error(this.$t('upload.tip', { 'format': `${formatTip}` }))
      }
      console.log('file.size', file.size)
      const sizeLimit = file.size / 1024 / 1024 > 1
      if (sizeLimit) {
        return this.$message.error(this.$t('upload.sizeMsg', { 'size': `${1}` }))
      }
      console.log('picUploadHandle row', row)
      console.log('picUploadHandle file', file)
      let fileReader = new FileReader()
      fileReader.readAsDataURL(file.raw)
      let that = this
      fileReader.onloadend = ev => {
        that.$nextTick(() => {
          that.$set(row, 'picUrl', '')
          that.$set(row, 'picBase64', ev.target.result)
        })
      }
    },
    saveRow (scope, action) {
      if (action === 'update') {
        let arr = [
          'dataList.' + scope.$index + '.chineseName',
          'dataList.' + scope.$index + '.englishName',
          'dataList.' + scope.$index + '.quantity',
          'dataList.' + scope.$index + '.packageCustomerNo',
          'dataList.' + scope.$index + '.unitNetWeightD',
          'dataList.' + scope.$index + '.unitDeclarePriceD',
          'dataList.' + scope.$index + '.brand',
          'dataList.' + scope.$index + '.goodsBarcode',
          'dataList.' + scope.$index + '.sku',
          'dataList.' + scope.$index + '.hsCode',
          'dataList.' + scope.$index + '.productModel',
          'dataList.' + scope.$index + '.material',
          'dataList.' + scope.$index + '.purpose',
          'dataList.' + scope.$index + '.origin',
          'dataList.' + scope.$index + '.pickingRemark',
          'dataList.' + scope.$index + '.productUrl',
          'dataList.' + scope.$index + '.picUrl'
        ]
        let validatedMsgList = []
        this.$refs.declareForm.validateField(arr, (error) => {
          if (error) {
            validatedMsgList.push(error)
          }
        })
        console.log('c', validatedMsgList)
        if (validatedMsgList.every((item) => item === '')) {
          let reqObj = Object.assign(scope.row, { waybillId: this.dataForm.waybillId })
          this.$http.put('/co/orderdeclare/', reqObj).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.$nextTick(() => {
                  this.$set(scope.row, 'update', !scope.row.update)
                })
              }
            })
          }).catch(() => {
          })
        }
      } else {
        this.$nextTick(() => {
          this.$set(scope.row, 'update', !scope.row.update)
        })
      }
    },
    deleteRow (declareItem) {
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('delete') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        declareItem.waybillId = this.dataForm.waybillId
        this.$http.post('/co/order/deleteDeclare', declareItem).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$nextTick(() => {
                this.init(this.dataForm.orderId, this.dataForm.waybillId)
              })
            }
          })
        }).catch(() => {
        })
      })
    },
    // 取消
    cancelFn () {
      this.visible = false
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    notAddRedStar (h, { column }) {
      return [h('span', ' ' + column.label)]
    },
    indexMethod (index) {
      if (!this.dataList || this.dataList.length <= 0) {
        return index
      }
      return (this.page - 1) * this.limit + index + 1
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  }
}
</script>

<style lang="scss" scoped>
::v-deep #declareTable .el-form-item  {
  margin-bottom: 0px !important;
}
</style>
