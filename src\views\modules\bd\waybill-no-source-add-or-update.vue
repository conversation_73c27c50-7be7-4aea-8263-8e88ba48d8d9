<template>
  <div class="add-body panel_body">
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" inline :rules="dataRule" ref="waybillNoSourceDataFrom" @keyup.enter.native="dataFormSubmitHandle()">
        <el-row type="flex" justify="center">
          <el-col>
            <el-row>
                <el-col  v-if="isLogisticsProduct" :span="8">
                  <el-form-item :label="$t('bdLogisticsProductOther.returnDeliveryNo')" prop="returnDeliveryNo" label-width="135px">
                    <el-radio-group class="width100" v-model="dataForm.returnDeliveryNo" >
                      <el-radio v-for="item in yesOrNoList" :key="item.dictName" :label="item.dictValue">{{item.dictName}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-form-item :label="$t('bdLogisticsChannel.waybillNoSource')" prop="waybillNoSource" label-width="110px">
                  <el-radio-group v-model="dataForm.waybillNoSource" @change="changeApiType()" style="width: 100%">
                    <template v-for="(item, index) in waybillNoSourceList">
                      <el-popover :key="'popover-' + index"  placement="top" trigger="hover" content="暂时不适配小包的物流产品"
                                  :disabled="isLogisticsProduct || Number(item.dictValue) !== 0">
                        <el-radio class='waybillNoSourceLabel' style='margin-right: 20px' :key="item.dictValue" slot="reference" :label="item.dictValue">{{ item.dictName }}</el-radio>
                      </el-popover>
                    </template>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <div v-show="trackingNoShow">
<!--              <el-row :gutter="20">-->
<!--                <el-col :span="8">-->
<!--                  <el-form-item :label="$t('bdLogisticsProductOther.earlyWarningInventory')" prop="earlyWarningInventory" >-->
<!--                    <el-input v-model="dataForm.earlyWarningInventory" :placeholder="$t('bdLogisticsProductOther.earlyWarningInventory')"></el-input>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
              <el-row :gutter="40" v-show="!isLogisticsProduct">
                <el-col :span="10">
                    <div class="warning fontWeightBolder fontSize16">{{$t('bdLogisticsChannelOther.geFromGroupTip')}}</div>
                </el-col>
              </el-row>
              <el-row :gutter="20" v-show="isLogisticsProduct">
                <el-col :span="24">
                  <el-form-item :label="$t('bdLogisticsProductOther.waybillNoGroupId')"
                                :rules="[{ required: trackingNoShow && isLogisticsProduct, message: this.$t('validate.required'), trigger: 'blur' }]"
                                 prop="waybillNoGroupId" label-width="120px">
                    <el-select filterable v-model="dataForm.waybillNoGroupId" :placeholder="$t('bdLogisticsProductOther.waybillNoGroupId')" clearable>
                      <el-option v-for="(item, index) in waybillNoGroupList" :key="index"
                                :label="item.groupName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-card v-show="pushApiTipsShow" style="margin-bottom: 10px">
              <el-row :gutter="40" >
                <el-col :span="20">
                  <div class="warning fontWeightBolder fontSize16">{{$t('bdLogisticsChannelOther.pushCustomerToProvderApiTip')}}</div>
                </el-col>
              </el-row>
              <el-row :gutter="40" style="margin-top: 5px">
                <el-col :span="12">
                  <div class="fontWeightBolder fontSize16">推送到供应商操作：客服管理-》外部单据交互-》同步到供应商</div>
                </el-col>
                <el-col :span="12">
                  <el-button @click="apiShow=!apiShow">{{(apiShow ? '隐藏' : '显示') + 'API参数设置'}}</el-button>
                </el-col>
              </el-row>
            </el-card>
            <div v-show="apiShow">
            <el-row :gutter="20">
              <el-col :span="10">
                <el-form-item  v-show="isShow" :label="$t('bdTargetHandoverChannel.apiTypeId')"
                               :rules="[{ required: apiShow && isShow, message: this.$t('validate.required'), trigger: 'blur' }]"
                               prop="apiTypeId" label-width="120px">
                  <el-select :disabled="updateApiTypeIdDisable"  filterable v-model="dataForm.apiTypeId" @change="changeApiTypeId()"
                         :placeholder="$t('bdTargetHandoverChannel.apiTypeId')" clearable>
                      <el-option v-for="(item, index) in logisticsChannelApiTypeList" :key="index"
                           :label="item.name" :value="item.id"></el-option>
                 </el-select>
              </el-form-item>
              <el-form-item>
                <el-button v-show="updateApiTypeIdShow" @click="updateApiType()">{{ $t('update') }}</el-button>
              </el-form-item>
              </el-col>
            </el-row>
              <el-row :gutter="20" v-show="!pushApiTipsShow">
                <el-col :span="24">
                  <el-form-item :label="$t('bdLogisticsChannel.apiRelatedSettings')" prop="apiRelatedSettings" label-width="120px">
                    <el-checkbox-group v-model="dataForm.apiRelatedSettingsArray">
                      <el-checkbox v-for="item in apiRelatedSettingsList" :label="item.dictValue" name="specialItem" :key="item.dictValue">{{item.dictName}}</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
            <el-row :gutter="20">
              <el-col :span="24" v-show="!isLogisticsProduct">
                <el-form-item :label="$t('bdLogisticsChannel.pushApiNoType')" prop="pushApiNoType" label-width="120px">
                  <el-radio-group v-model="dataForm.pushApiNoType" style="width: 100%">
                    <el-radio
                      v-for="item in pushApiNoTypeList"
                      :key="item.dictValue"
                      :label="item.dictValue">
                      {{item.dictName}}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-popover
                  placement="right-end"
                  width="400"
                  trigger="hover">
                  <div class="popover-content">1、该参数是指:指定系统中的某一个单号作为参考号推送到供应商的系统</div>
                  <div class="popover-content">2、该参数默认使用运单号作为供应商系统的参考号</div>
                  <div class="popover-content warning">3、由于供应商系统会判断参考号唯一,为了适应不同供应商系统的要求,请合理指定单号类型</div>
                  <el-button type="text" slot="reference">
                    <i class="el-icon-info el-icon--right warning">{{$t('bdLogisticsChannel.pushApiNoType')}}说明</i>
                  </el-button>
                </el-popover>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-show="!isLogisticsProduct">
<!--              物流产品不展示其他推送节点-->
              <el-col :span="24" >
                  <el-form-item   prop="apiPushNode" :label="$t('bdLogisticsChannel.apiPushNode')" label-width="120px">
                    <el-select style="width: 600px" filterable v-model="dataForm.apiPushNodeArray"  multiple clearable>
                      <el-option v-for="(item, index) in apiPushNodeList" :key="index" :label="item.dictName"
                                 :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                <el-popover
                  placement="right-end"
                  width="300"
                  trigger="hover">
                  <div class="popover-content">如代理要求其他节点推送数据,请选择。</div>
                  <div class="popover-content warning">否则,请不要选择如下推送接口,会影响操作流程。</div>
                  <el-button type="text" slot="reference">
                    <i class="el-icon-info el-icon--right warning">其他API推送节点说明</i>
                  </el-button>
                </el-popover>
              </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-row>
                <el-col :span="12">
                  <h3>API参数</h3>
                </el-col>
                <el-col :span="12">
                  <!--  Fedex无纸化-上传标签和Logo图片 -->
                  <el-link v-if="[ 144, 1 ].includes(dataForm.apiValue)" style="float:right; margin-top: 15px;" @click="upload4Fedex">Fedex无纸化-上传标签和Logo图片</el-link>
                  <!--  Fedex无纸化-上传标签和Logo图片 -->
                  <el-link v-if="[ 151, 12 ].includes(dataForm.apiValue)" style="float:right; margin-top: 15px;" @click="upload4Ups">UPS无纸化-上传签名图片</el-link>
                </el-col>
              </el-row>
              <el-table  :data="dataForm.apiDetailList" style="width: 100%;">
                <el-table-column prop="paramKey" :label="$t('bdLogisticsChannelApiTypeDetail.paramKey')"
                                header-align="center" align="center" width="200px">
                  <template slot-scope="scope">
                    <span v-text="scope.row.paramKey" v-show="!scope.row.editing" :class="scope.row.id ? '':'danger'"></span>
                    <el-input :disabled="true" v-show="scope.row.editing" :prop="'dataList.' + scope.$index + '.paramKey'"
                              v-model="scope.row.paramKey" :autofocus="true"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="paramValue" :label="$t('bdLogisticsChannelApiTypeDetail.paramValue')"
                                header-align="center" align="center" width="480px">
                  <template slot-scope="scope">
                    <span v-text="scope.row.paramValue" v-show="!scope.row.editing"></span>
                    <el-input v-show="scope.row.editing" :prop="'dataList.' + scope.$index + '.paramValue'"
                              v-model="scope.row.paramValue" :autofocus="true"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="remark" :label="$t('bdLogisticsChannelApiTypeDetail.remark')" header-align="center"
                                align="center" min-width="300px">
                  <template slot-scope="scope">
                    <span v-html="scope.row.remark" v-show="!scope.row.editing"></span>
                    <el-input :disabled="true" v-show="scope.row.editing" :prop="'dataList.' + scope.$index + '.remark'"
                              v-model="scope.row.remark" :autofocus="true"></el-input>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
                  <template slot-scope="scope">
                    <el-button type="text" ref="operation" size="small"
                              @click="rowHandleEdit(scope.row.editing,scope.$index,scope.row)">{{
                      $t(scope.row.buttonLabel) }}
                    </el-button>
                    <!--                  <el-button type="text" size="small" @click="deleteHandle(scope.$index,scope.row.id)">{{ $t('delete') }}</el-button>-->
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <!-- <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div> -->
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import comMixins from '@/mixins/comMixins'
import api from '@/api'
import baseData from '@/api/baseData'
const noGet = 0 // 手工输入
const trackingNo = 1 // 运单号库
const channelApi = 2 // API获取
const autoGenerate = 3 // 系统自动生成
const pushByCustomer = 4 // 客户预报时推送
const specifiedTrackNo = 5 // 指定单号库
const getFromTrackingNoLibAndApiPush = 6 // 运单号库获取并API预报
const pushByCustomerAndApiPush = 7 // 客户预报时推送并API预报
const isEdit = 'Y'
export default {
  mixins: [dictTypeMixins, comMixins],
  data () {
    return {
      dataForm: {
        id: '',
        waybillNoSource: 3,
        apiRelatedSettingsArray: [],
        logisticsChannelId: '',
        earlyWarningInventory: '',
        earlyWarningContact: '',
        waybillNoGroupId: '',
        returnDeliveryNo: 1,
        apiTypeId: '',
        apiValue: '',
        apiPushNodeArray: [],
        logisticsType: '',
        pushApiNoType: '',
        businessType: '',
        paramKey: '',
        paramValue: '',
        remark: '',
        creator: '',
        apiDetailList: []
      },
      updateApiTypeIdDisable: false,
      updateApiTypeIdShow: false,
      apiIsEdit: false,
      apiRelatedSettingsList: [],
      earlyWarningContactList: [],
      isLogisticsProduct: true,
      apiShow: false,
      pushApiTipsShow: false,
      trackingNoShow: false,
      isShow: false,
      isShowApiTable: false,
      waybillNoSourceList: [],
      waybillNoGroupList: [],
      logisticsChannelApiTypeList: [],
      apiPushNodeList: [],
      pushApiNoTypeList: [],
      yesOrNoList: [],
      userList: [],
      user: {},
      loading: false
    }
  },
  computed: {
    dataRule () {
      return {
        logisticsChannelId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        paramKey: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        paramValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    updateApiType () {
      this.$confirm(this.$t('修改API类型会将已维护的API参数重置，您确认要修改吗？', { 'handle': this.$t('charging') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.updateApiTypeIdDisable = false
      }).catch(() => {
      })
    },
    validated () {
      let ret = false
      this.$refs['waybillNoSourceDataFrom'].validate((valid) => {
        if (valid) {
          ret = true
        }
      })
      if (ret) {
        this.$emit('onWaybillNoSourceData', this.dataForm)
      }
      return ret
    },
    changeWaybillNoSource () {
      if (noGet === this.dataForm.waybillNoSource || specifiedTrackNo === this.dataForm.waybillNoSource) {
        this.apiShow = false
        this.trackingNoShow = false
        this.pushApiTipsShow = false
      } else if (trackingNo === this.dataForm.waybillNoSource) {
        this.apiShow = false
        this.trackingNoShow = true
        this.pushApiTipsShow = false
      } else if (channelApi === this.dataForm.waybillNoSource || getFromTrackingNoLibAndApiPush === this.dataForm.waybillNoSource || pushByCustomerAndApiPush === this.dataForm.waybillNoSource) {
        this.apiShow = true
        this.trackingNoShow = false
        this.pushApiTipsShow = false
      } else if (pushByCustomer === this.dataForm.waybillNoSource || autoGenerate === this.dataForm.waybillNoSource) {
        this.apiShow = false
        this.pushApiTipsShow = true
        this.trackingNoShow = false
      }
    },
    // 获取数据字典
    async getDict () {
      this.apiRelatedSettingsList = await this.getDictTypeList('apiRelatedSettings')
      this.waybillNoSourceList = await this.getDictTypeList('waybillNoSource')
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
      this.apiPushNodeList = await this.getDictTypeList('apiPushNode') // API推送类型
      this.pushApiNoTypeList = await this.getDictTypeList('ChannelPushApiNoType') // 推送API单号类型
    },
    // 同步通过接口获取基础数据
    async getBaseData () {
      this.logisticsChannelApiTypeList = await baseData(api.logisticsChannelApiTypeList, { status: 1 }).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      this.waybillNoGroupList = await baseData(api.waybillNoGroupList, { status: 0 }).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        // this.$refs['waybillNoSourceDataFrom'].resetFields()
        if (!this.dataForm.id) {
          this.$refs['waybillNoSourceDataFrom'].resetFields()
          this.updateApiTypeIdDisable = false
          this.updateApiTypeIdShow = false
        }
        this.changeApiType()
        if (this.dataForm.apiTypeId) {
          this.changeApiTypeId(isEdit)
          this.apiIsEdit = true
          this.updateApiTypeIdDisable = true
          this.updateApiTypeIdShow = true
        }

        this.changeWaybillNoSource()
        // 设置运单号来源的显示
        this.setWaybillNoSource()
      })
    },
    // 设置运单号来源的显示
    setWaybillNoSource () {
      // 物流产品
      if (this.isLogisticsProduct) {
        // 如果是物流产品,单号来源删除为 客户预报时推送选项
        this.waybillNoSourceList = this.waybillNoSourceList
          .filter(item => item.dictValue !== pushByCustomer && item.dictValue !== channelApi && item.dictValue !== getFromTrackingNoLibAndApiPush && item.dictValue !== pushByCustomerAndApiPush)
      }
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/bd/logisticschannelapi/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.changeApiType()
        if (this.dataForm.apiTypeId) {
          this.changeApiTypeId(isEdit)
          this.apiIsEdit = true
          this.updateApiTypeIdDisable = true
        }

        this.changeWaybillNoSource()
      }).catch(() => {})
    },

    changeApiTypeId (flag) {
      if (this.dataForm.apiTypeId) {
        this.logisticsChannelApiTypeList.find(item => {
          if (this.dataForm.apiTypeId === item.id) {
            this.dataForm.apiValue = item.apiValue
            return true
          }
        })
        this.isShowApiTable = true
        if (flag === isEdit) {
          this.dataForm.apiDetailList.forEach((v, i) => {
            this.$set(this.dataForm.apiDetailList[i], 'editing', false)
            this.$set(this.dataForm.apiDetailList[i], 'buttonLabel', 'update')
          })
        } else {
          this.getDetailList()
        }
      } else {
        this.dataForm.apiValue = ''
        this.isShowApiTable = false
      }
    },
    getDetailList () {
      this.$http.get(`/bd/logisticschannelapitypedetail/listByApiTypeId?apiTypeId=${this.dataForm.apiTypeId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.apiDetailList = res.data
      }).catch(() => {
      })
    },
    changeApiType () {
      if (noGet === this.dataForm.waybillNoSource || specifiedTrackNo === this.dataForm.waybillNoSource) {
        this.apiShow = false
        this.isShow = false
        this.trackingNoShow = false
        this.updateApiTypeIdShow = false
        this.pushApiTipsShow = false
      } else if (trackingNo === this.dataForm.waybillNoSource) {
        this.apiShow = false
        this.isShow = false
        this.trackingNoShow = true
        this.updateApiTypeIdShow = false
        this.pushApiTipsShow = false
      } else if (pushByCustomer === this.dataForm.waybillNoSource || autoGenerate === this.dataForm.waybillNoSource) {
        this.apiShow = false
        this.isShow = true
        this.trackingNoShow = false
        this.updateApiTypeIdShow = true
        this.pushApiTipsShow = true
      } else {
        this.apiShow = true
        this.isShow = true
        this.isShowApiTable = true
        /* if (this.apiIsEdit) {
          this.updateApiTypeIdShow = true
        } */
        this.updateApiTypeIdShow = true
        this.trackingNoShow = false
        this.pushApiTipsShow = false
      }

      // let apiType = this.dataForm.apiType
      // if (apiType === 1) {
      //   this.isShow = true
      //   if (this.dataForm.id) {
      //     this.updateApiTypeIdShow = true
      //     this.isShowApiTable = true
      //   }
      // } else {
      //   if (!this.dataForm.id) {
      //     this.dataForm.apiTypeId = ''
      //   }
      //   this.isShowApiTable = false
      //   this.isShow = false
      //   this.updateApiTypeIdShow = false
      // }
      this.$emit('onApiTypeChange', this.dataForm.waybillNoSource)
    },
    // Fedex无纸化-上传标签和Logo图片
    upload4Fedex () {
      // developerKey: '',
      //   password: '',
      //   accountNumber: '',
      //   meterNumber: ''
      let apiDetailList = this.dataForm.apiDetailList
      let routeName = `fedex-upload`
      this.$router.push({ name: routeName,
        params: {
          developerKey: apiDetailList.filter(item => item.paramKey === 'developerKey')[0].paramValue,
          password: apiDetailList.filter(item => item.paramKey === 'password')[0].paramValue,
          accountNumber: apiDetailList.filter(item => item.paramKey === 'accountNumber')[0].paramValue,
          meterNumber: apiDetailList.filter(item => item.paramKey === 'meterNumber')[0].paramValue
        }
      })
    },
    // UPS无纸化-上传签名图片
    upload4Ups () {
      let apiDetailList = this.dataForm.apiDetailList
      let routeName = `ups-upload`
      this.$router.push({ name: routeName,
        params: {
          logisticsChannelId: this.dataForm.id,
          accountNumber: apiDetailList.filter(item => item.paramKey === 'accountNumber')[0].paramValue,
          clientId: apiDetailList.filter(item => item.paramKey === 'clientId')[0].paramValue,
          clientSecret: apiDetailList.filter(item => item.paramKey === 'clientSecret')[0].paramValue,
          version: apiDetailList.filter(item => item.paramKey === 'version')[0].paramValue
        }
      })
    },
    submitHandle (index, data) {
      this.$set(this.dataForm.apiDetailList[index], 'editing', false)
      this.$set(this.dataForm.apiDetailList[index], 'buttonLabel', 'update')
      // this.$message.success(this.$t('prompt.success'))
    },
    rowHandleEdit (editing, index, data) {
      if (!editing) {
        this.$set(this.dataForm.apiDetailList[index], 'editing', true)
        this.$set(this.dataForm.apiDetailList[index], 'buttonLabel', 'save')
      } else {
        // 点击保存按钮，将数据提交到后台进行保存或修改操作
        this.submitHandle(index, data)
      }
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/bd/logisticschannelapi/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
<style lang='scss' scoped>
.waybillNoSourceLabel /deep/ .el-radio__label{
  padding-left: 5px !important;
}
</style>
