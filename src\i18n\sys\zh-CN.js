
let t = {
  system: {
    id: 'ID',
    companyId: '所属公司',
    creator: '创建人',
    createDate: '创建时间',
    updater: '更新人',
    updateDate: '更新时间',
    status: '状态',
    index: '序号',
    warehouseId: '仓库编号',
    warehouseName: '仓库名称',
    startTime: '开始时间',
    endTime: '结束时间',
    yes: '是',
    no: '否',
    to: '至',
    startDate: '起始日期',
    endDate: '结束日期',
    start: '起始',
    end: '结束',
    serialNumber: '序号'
  },
  loading: '加载中...',
  brand: {
    lg: 'ITS',
    mini: 'TH'
  },
  noType: '单号类型',
  guidPrefix: 'GUID前缀',
  downloadLastmileLabel: '下载尾程面单',
  loadCache: '刷新缓存',
  detailMantain: '明细维护',
  customer: '客户',
  zoneFor: '分区区间',
  addOrUpdate: '新增/修改',
  preview: '预览',
  selected: '已选择',
  setReturnAddr: '设为回邮地址',
  baseInfo: '基础信息',
  pushData: '数据推送',
  status: '状态',
  add: '新增',
  pushCustomerBills: '推送账单',
  pushCustomerFee: '推送费用单',
  smartAdd: '快录',
  recharge: '充值',
  save: '保存',
  noSave: '不保存',
  clearAndCannel: '清空并返回',
  forecast: '预报',
  viewMore: '更多',
  fillMoreInfo: '点击此处,填写更多信息',
  packUp: '点击此处,收起',
  confirmInWarehouse: '确认入库',
  saveAndConfirm: '保存并确认',
  printProductBarcode: '打印商品条码',
  delete: '删除',
  deleteBatch: '删除',
  trackByHand: '手动轨迹跟踪',
  update: '修改',
  query: '查询',
  import: '导入',
  trackingImport: '轨迹导入',
  importChangeLabelData: '导入换单数据',
  getChangeLabelTask: '获取换标任务',
  scanAndPrintForBatch: '批量扫描打印',
  scanAndPrint: '单个扫描打印',
  scanHistory: '扫描历史',
  uploadPdf: '上传PDF文件',
  import_audit: '导入审核',
  import_log_info: '导入日志',
  import_module: '导入模板',
  import_module1: '导入模板1',
  import_module2: '导入模板2',
  import_module3: '导入模板3',
  export: '导出',
  exportStandard: '标准导出',
  exportDetails: '详细导出',
  publish: '发布',
  cancelPublish: '取消发布',
  preTrackingSet: '预上网轨迹推送配置',
  handle: '操作',
  confirm: '确定',
  submit: '提交',
  cancel: '取消',
  logout: '退出',
  enable: '启用',
  no_use: '未启用',
  disable: '停用',
  follow: '跟进',
  hangup: '挂起',
  close: '关闭',
  select: '请选择',
  showWxBindQrCode: '微信小程序',
  selectImportTemplate: '模板',
  notAllowModifyAfterSet: '设置后不允许更改,请谨慎设置',
  twoCharToSelectForCustomer: '请输入2个字符以上的客户编码或者名称',
  allCustomer: '所有客户',
  allCompanyUser: '所有公司员工',
  seniorSearch: '更多条件',
  seniorSearch2: '收起',
  view: '查看',
  scanAndSort: '扫描分拣',
  sort: '分拣',
  exportItem: '导出明细',
  modifyConsigneeAddress: '修改收件地址',
  modifyOrderBaseInfo: '修改订单基础信息',
  importToForecast: '导入尾程单号',
  firstSubDeliveryNoToMainDeliveryNo: '以第一个子派送单号作为主派送单号',
  oneToOneCorrespondence: '箱号(FBA唛头号)与派送单号一一对应',
  copy: '复制',
  log: '日志',
  resets: '重置',
  detail: '详情',
  goBack: '返回上一级',
  noData: '暂无数据',
  printLocationBarcode: '打印库位条码',
  createLocation: '库位初始化',
  audit: '审核',
  batchAudit: '千条批量审核',
  previewAudit: '预览审核',
  no_approved: '不通过',
  approved: '通过',
  antiReview: '反审',
  init: '制单',
  preAudit: '预收',
  invalid: '作废',
  settleAccounts: '结账',
  chooseSettleFee: '选择对账费用',
  syncFeeBySettleBill: '同步到应收',
  pushBill: '推送账单',
  recoil: '弃审',
  finish: '完成',
  tableConfigTooltip: '自定义列显示',
  errHandle: '异常处理',
  details: '明细',
  chargingBatch: '批量计费',
  charging: '计费',
  reCharging: '重新计费',
  priceCharging: '指定单价重新计费',
  reCalcFeeByPage: '搜索结果重新计费',
  receivable: '应收',
  receivabled: '实收',
  payable: '应付',
  payabled: '实付',
  writeOff: '核销',
  bankReconciliation: '对账',
  batchDownloadBill: '批量下载对账单',
  check: '核对',
  reconciliationSuccessful: '认同差异',
  reconciliationBack: '退回差异',
  reconciliationBatchSuccessful: '批量认同',
  reconciliationBatchBack: '批量退回',
  successfulNotUpdate: '认同差异后,同单号再次对账导入调整的新费用会忽视，仅以本次认同为准。',
  importAdjust: '导入调整',
  updateOptDate: '修改业务发生时间',
  updateCorrection: '覆盖导入',
  redCorrection: '差异导入',
  updateCorrectionTips: '直接修改已有金额',
  redCorrectionTips: '将金额以红冲蓝补形式添加到系统',
  addFee: '新增费用',
  addInvoice: '新增发票信息',
  invoice: '开发票',
  buildDoload: '生成下载单',
  downloadBills: '下载对账单',
  bankReconciliationResult: '对账结果',
  apportion: '分摊',
  orderDetail: '分摊明细',
  adjustment: '调整',
  adjustmentFee: '调整费用',
  updateRemark: '修改备注',
  confirmFee: '确认费用',
  adjustmentSubFee: '调整子单费用',
  confirmPayBill: '确认账单',
  sum: '合计',
  company: '公司',
  uploadStamp: '上传印章',
  provider: '供应商',
  length: '长',
  width: '宽',
  height: '高',
  updateBatch: '批量编辑',
  addMemo: '添加备注',
  sytemLabel: '系统内置标签',
  comfirmPay: '确认支付',
  exportTemplate: '编辑模板',
  syncAdditionTooltip: '先计算加收比例，再计算加收金额',
  notSettle: '暂停对账',
  canSettle: '允许对账',
  pushInfo: '主单推送信息',
  pushDetails: '推送明细',
  mawbInfo: '提单信息',
  modifyStaContain: '打板信息',
  modifyStaBatch: '打板批次',
  printCn38: '打印CN38文件',
  uploadMawbFile: '上传提单文件',
  pushMawbFile: 'API推送提单文件',
  generatePush: '生成主单推送单',
  staContain: '航空打板',
  hoairLine: '交航',
  lineArrival: '到港',
  pushProvider: '大包交接',
  parcelApiUpdate: '包裹API更新',
  clippingRegion1: '1、点击“请选择要上传的图片”按钮，选择需要裁剪的图片，',
  clippingRegion2: '2、logo最终裁剪的尺寸为178*80，可拖动裁剪框，自由移动需要裁剪的区域，也可以按住Ctrl+鼠标滚轮调整图片大小，',
  clippingRegion3: '3、建议使用透明背景的png图片，整体显示效果更佳。',
  uploadDiyLogo: '上传预设图',
  diyimportFullRequired: '生成必填列',
  waybill: '运单',
  prompt: {
    title: '提示',
    info: '确定进行[ {handle} ]操作?',
    success: '操作成功',
    failed: '操作失败',
    uploadEmpty: '请上传付款水单',
    auditBatch: '请选择审核项',
    deleteBatch: '请选择删除项',
    abandonBatch: '请选择弃审项',
    finishBatch: '请选择完成项',
    warehouse: '请先选择一个仓库',
    actionStatusBatch: '请选择操作项',
    export: '请选择导出模板',
    download: '下载',
    single: '只能选择一条数据操作'
  },
  validate: {
    volDiv: '请选择材积除!',
    bubbleRate: '请设置分泡比例',
    nonnegativeNumber: '只能输入非负数',
    maxLength: '长度不能大于{max}位',
    minLength: '长度不能小于{min}位',
    existSpaces: '存在空格',
    errorEmail: '邮箱格式错误',
    decimal3: '只能输入正数,且小数位不能超过{number}位',
    letterAndNumber: '只能输入字母和数字组合',
    upCaseLetter: '只能输大写字母',
    integer: '只能输入正整数',
    max: '不能大于{max}',
    min: '不能笑{max}',
    format: '{attr}-格式错误',
    required: '必填项不能为空',
    limitOneDecimal: '{attr}最多只能保留一位小数',
    number: '只能输入3位以内数字',
    endWeight: '结束重量必须大于起始重量',
    minMaxTime: '最长耗时不能小于最短耗时时间',
    beforeAfterDate: '失效时间不能早于或等于生效时间',
    badSumDGeNoWriteOffSumD: '坏账金额不能大于未核销金额',
    balanceIsZero: '余额为0不能进行核销.{attr}必填',
    receivableLeZero: '{attr}不能小于0',
    fuelRate: '{attr}格式错误,只能输入正数，最多保留4位小数位',
    weight: '{attr}格式错误,只能输入正数，最多保留3位小数位',
    zoneValidate: '{attr}格式错误,只能输入正数',
    discountValidate: '{attr}格式错误,只能输入正数，最多保留2位小数位',
    lengthValidate: '{attr}格式错误,只能输入正数，最多保留1位小数位',
    postCodeValidate: '{attr}格式错误,最多32位',
    errorValidate: '只能输入最多两位小数的非负数',
    declareExportTemplate: '请选择导出模板'
  },
  upload: {
    attachment: '附件',
    text: '将文件拖到此处，或<em>点击上传</em>',
    recharge: '将付款水单拖到此处，或<em>点击上传</em>',
    tip: '只支持{format}格式文件！',
    button: '点击上传'
  },
  bdAttachments: {
    name: '文件名',
    file: '文件',
    attachmentUrl: '附件地址'
  },
  datePicker: {
    range: '至',
    start: '开始日期',
    end: '结束日期',
    selectd: '请选择时间'
  },
  fullscreen: {
    prompt: '您的浏览器不支持此操作'
  },
  updatePassword: {
    title: '修改密码',
    reset: '重置密码',
    username: '账号',
    password: '原密码',
    newPassword: '新密码',
    newPasswordIs: '初始密码为{initPass}',
    confirmPassword: '确认密码',
    validate: {
      confirmPassword: '确认密码与新密码输入不一致',
      rule: '新密码必须由 6-30位字母、数字、特殊符号组成.'
    }
  },
  contentTabs: {
    closeCurrent: '关闭当前标签页',
    closeOther: '关闭其它标签页',
    closeAll: '关闭全部标签页'
  },
  notFound: {
    desc: '抱歉！您访问的页面<em>失联</em>啦...',
    back: '上一页',
    home: '首页'
  },
  login: {
    title: '登录',
    adminTitle: '管理员登录',
    username: '用户名',
    password: '密码',
    captcha: '验证码',
    systemName: 'ITS',
    copyright: 'Copyright © 2025 深圳市互联通科技有限公司',
    saasCopyright: 'Copyright © 2025 捷链ITS',
    forgetPassword: '忘记密码?',
    clientLogin: '客户端入口',
    mail: '邮箱'
  },
  forget: {
    title: '忘记密码',
    email: '邮箱地址',
    send: '发送邮件',
    illegalEmail: '请输入正确的邮箱格式',
    sendSuccess: '重置密码的邮件已经发送到您的邮箱，请注意查收...'
  },
  reset: {
    title: '重置密码',
    passwordFirst: '请输入你的新密码',
    passwordSecond: '请再次输入',
    successMsgPerfix: '恭喜你，密码重置成功！你现在可以使用新密码登陆系统了',
    successMsgSuffix: '秒后将自动跳转至登陆页面...',
    passwordDiff: '请输入相同的密码'
  },
  home: {
    desc: {
      title: '项目介绍',
      list: [
        'WOMS'
      ]
    }
  },
  process: {
    name: '名称',
    key: '标识',
    deployFile: '部署流程文件',
    id: '流程ID',
    deploymentId: '部署ID',
    version: '版本',
    resourceName: 'XML',
    diagramResourceName: '图片',
    deploymentTime: '部署时间',
    active: '激活',
    suspend: '挂起',
    convertToModel: '转换为模型'
  },
  running: {
    id: '实例ID',
    definitionKey: '定义Key',
    processDefinitionId: '定义ID',
    processDefinitionName: '定义名称',
    activityId: '当前环节',
    suspended: '是否挂起',
    suspended0: '否',
    suspended1: '是'
  },
  schedule: {
    beanName: 'bean名称',
    beanNameTips: ',spring bean名称, 如: testTask',
    pauseBatch: '暂停',
    resumeBatch: '恢复',
    runBatch: '执行',
    log: '日志列表',
    params: '参数',
    cronExpression: 'cron表达式',
    cronExpressionTips: '如: 0 0 12 * * ?',
    remark: '备注',
    status: '状态',
    status0: '暂停',
    status1: '正常',
    statusLog0: '失败',
    statusLog1: '成功',
    pause: '暂停',
    resume: '恢复',
    run: '执行',
    jobId: '任务ID',
    times: '耗时(ms)',
    createDate: '执行时间'
  },
  mail: {
    name: '名称',
    config: '邮件配置',
    subject: '主题',
    createDate: '创建时间',
    send: '发送邮件',
    content: '内容',
    smtp: 'SMTP',
    port: '端口号',
    username: '邮箱账号',
    password: '邮箱密码',
    mailTo: '收件人',
    mailCc: '抄送',
    params: '模板参数',
    paramsTips: '如：{"code": "123456"}',
    templateId: '模版ID',
    status: '状态',
    status0: '发送失败',
    status1: '发送成功',
    mailFrom: '发送者',
    sendDate: '发送时间'
  },
  sms: {
    mobile: '手机号',
    status: '状态',
    status0: '发送失败',
    status1: '发送成功',
    config: '短信配置',
    send: '发送短信',
    platform: '平台类型',
    platform1: '阿里云',
    platform2: '腾讯云',
    params: '参数',
    paramsTips: '如：{"code": "123456"}',
    params1: '参数1',
    params2: '参数2',
    params3: '参数3',
    params4: '参数4',
    createDate: '发送时间',
    aliyunAccessKeyId: 'Key',
    aliyunAccessKeyIdTips: '阿里云AccessKeyId',
    aliyunAccessKeySecret: 'Secret',
    aliyunAccessKeySecretTips: '阿里云AccessKeySecret',
    aliyunSignName: '短信签名',
    aliyunTemplateCode: '短信模板',
    aliyunTemplateCodeTips: '短信模板CODE',
    qcloudAppId: 'AppId',
    qcloudAppIdTips: '腾讯云AppId',
    qcloudAppKey: 'AppKey',
    qcloudAppKeyTips: '腾讯云AppKey',
    qcloudSignName: '短信签名',
    qcloudTemplateId: '短信模板',
    qcloudTemplateIdTips: '短信模板ID'
  },
  oss: {
    config: '云存储配置',
    upload: '上传文件',
    url: 'URL地址',
    createDate: '创建时间',
    type: '类型',
    type1: '七牛',
    type2: '阿里云',
    type3: '腾讯云',
    type4: 'FastDFS',
    type5: '本地上传',
    qiniuDomain: '域名',
    qiniuDomainTips: '七牛绑定的域名',
    qiniuPrefix: '路径前缀',
    qiniuPrefixTips: '不设置默认为空',
    qiniuAccessKey: 'AccessKey',
    qiniuAccessKeyTips: '七牛AccessKey',
    qiniuSecretKey: 'SecretKey',
    qiniuSecretKeyTips: '七牛SecretKey',
    qiniuBucketName: '空间名',
    qiniuBucketNameTips: '七牛存储空间名',
    aliyunDomain: '域名',
    aliyunDomainTips: '阿里云绑定的域名，如：http://cdn.renren.io',
    aliyunPrefix: '路径前缀',
    aliyunPrefixTips: '不设置默认为空',
    aliyunEndPoint: 'EndPoint',
    aliyunEndPointTips: '阿里云EndPoint',
    aliyunAccessKeyId: 'AccessKeyId',
    aliyunAccessKeyIdTips: '阿里云AccessKeyId',
    aliyunAccessKeySecret: 'AccessKeySecret',
    aliyunAccessKeySecretTips: '阿里云AccessKeySecret',
    aliyunBucketName: 'BucketName',
    aliyunBucketNameTips: '阿里云BucketName',
    qcloudDomain: '域名',
    qcloudDomainTips: '腾讯云绑定的域名',
    qcloudPrefix: '路径前缀',
    qcloudPrefixTips: '不设置默认为空',
    qcloudAppId: 'AppId',
    qcloudAppIdTips: '腾讯云AppId',
    qcloudSecretId: 'SecretId',
    qcloudSecretIdTips: '腾讯云SecretId',
    qcloudSecretKey: 'SecretKey',
    qcloudSecretKeyTips: '腾讯云SecretKey',
    qcloudBucketName: 'BucketName',
    qcloudBucketNameTips: '腾讯云BucketName',
    qcloudRegion: '所属地区',
    qcloudRegionTips: '请选择',
    qcloudRegionBeijing1: '北京一区（华北）',
    qcloudRegionBeijing: '北京',
    qcloudRegionShanghai: '上海（华东）',
    qcloudRegionGuangzhou: '广州（华南）',
    qcloudRegionChengdu: '成都（西南）',
    qcloudRegionChongqing: '重庆',
    qcloudRegionSingapore: '新加坡',
    qcloudRegionHongkong: '香港',
    qcloudRegionToronto: '多伦多',
    qcloudRegionFrankfurt: '法兰克福',
    localDomain: '域名',
    localDomainTips: '绑定的域名，如：http://cdn.renren.io:8084',
    fastdfsDomain: '域名',
    fastdfsDomainTips: '绑定的域名，如：http://cdn.renren.io',
    localPrefix: '路径前缀',
    localPrefixTips: '不设置默认为空',
    localPath: '存储目录',
    localTips: '注意事项',
    localPathTips: '如：D:/upload'
  },
  dept: {
    name: '名称',
    parentName: '上级部门',
    sort: '排序',
    parentNameDefault: '一级部门'
  },
  dict: {
    dictName: '名称',
    dictType: '类型',
    dictValue: '值',
    sort: '排序',
    remark: '备注',
    createDate: '创建时间'
  },
  news: {
    title: '标题',
    pubDate: '发布时间',
    createDate: '创建时间',
    content: '内容'
  },
  logError: {
    module: '模块名称',
    requestUri: '请求URI',
    requestMethod: '请求方式',
    requestParams: '请求参数',
    ip: '操作IP',
    userAgent: '用户代理',
    createDate: '创建时间',
    errorInfo: '异常信息'
  },
  logLogin: {
    creatorName: '用户名',
    status: '状态',
    status0: '失败',
    status1: '成功',
    status2: '账号已锁定',
    operation: '操作类型',
    operation0: '登录',
    operation1: '退出',
    ip: '操作IP',
    userAgent: 'User-Agent',
    createDate: '创建时间'
  },
  logOperation: {
    module: '模块名称',
    status: '状态',
    status0: '失败',
    status1: '成功',
    creatorName: '用户名',
    operation: '用户操作',
    requestUri: '请求URI',
    requestMethod: '请求方式',
    requestParams: '请求参数',
    requestTime: '请求时长',
    ip: '操作IP',
    userAgent: 'User-Agent',
    createDate: '创建时间'
  },
  menu: {
    name: '名称',
    icon: '图标',
    type: '类型',
    type0: '菜单',
    type1: '按钮',
    sort: '排序',
    url: '路由',
    helpUrl: '帮助手册地址',
    helpUrlPlaceholder: '不用包含文件集的地址，例如：http://doc.goto56.com/project-3/doc-225，只填 doc-225',
    permissions: '授权标识',
    permissionsTips: '如: sys:menu:save',
    parentName: '上级菜单',
    parentNameDefault: '一级菜单',
    resource: '授权资源',
    resourceUrl: '资源URL',
    resourceMethod: '请求方式',
    resourceAddItem: '添加一项'
  },
  params: {
    paramCode: '编码',
    paramValue: '值',
    remark: '备注',
    readOnly: '只读',
    companyEditable: '公司可编辑'
  },
  template: {
    detail: '模板明细',
    add: '新增模板',
    editdetail: '编辑模板明细',
    type: '模板名称',
    edit: '编辑模板'
  },
  logo: {
    chooseImgFile: '请选择要上传的图片'
  }
}

t.role = {}
t.role.name = '名称'
t.role.remark = '备注'
t.role.createDate = '创建时间'
t.role.menuList = '菜单授权'
t.role.deptList = '数据授权'

t.user = {}
t.user.username = '用户名'
t.user.deptName = '所属部门'
t.user.companyName = '所属公司'
t.user.companyNamePlaceholder = '所属公司，留空表示属于总公司'
t.user.email = '邮箱'
t.user.mobile = '手机号'
t.user.status = '状态'
t.user.status0 = '停用'
t.user.status1 = '正常'
t.user.apiSecurityKey = '安全密钥'
t.user.createDate = '创建时间'
t.user.password = '密码'
t.user.confirmPassword = '确认密码'
t.user.realName = '真实姓名'
t.user.gender = '性别'
t.user.gender0 = '男'
t.user.gender1 = '女'
t.user.gender2 = '保密'
t.user.jobs = '岗位'
t.user.roleIdList = '角色配置'
t.user.jobDictList = '岗位'
t.user.wxlinkAuth = '小程序权限'
t.user.validate = {}
t.user.validate.confirmPassword = '确认密码与密码输入不一致'

t.sysCompany = {}
t.sysCompany.id = '公司编号'
t.sysCompany.type = '类型'
t.sysCompany.code = '代号'
t.sysCompany.name = '名称'
t.sysCompany.enName = '英文名称'
t.sysCompany.country = '国家'
t.sysCompany.province = '省/州'
t.sysCompany.city = '市'
t.sysCompany.district = '区'
t.sysCompany.street = '地址'
t.sysCompany.stamp = '印章'
t.sysCompany.postcode = '邮编'
t.sysCompany.contact = '联系人'
t.sysCompany.phone = '电话'
t.sysCompany.memo = '备注'
t.sysCompany.parentId = '所属公司'
t.sysCompany.selectType = '请选择公司类型'
t.sysCompany.settlementPeriod = '结算周期'
t.sysCompany.salesman = '业务员'
t.sysCompany.waiter = '客服员'
t.sysCompany.regenerate = '重新生成'
t.sysCompany.ipmsregister = 'IPMS注册'
t.sysCompany.logisticsTypeList = '物流类型'

/* 地区 BdRegion */
t.bdRegion = {}
t.bdRegion.level = '级别'
t.bdRegion.code = '代码'
t.bdRegion.name = '名称'
t.bdRegion.enName = '英文名称'
t.bdRegion.postcode = '邮编'
t.bdRegion.path = '级联路径'
t.bdRegion.parentCode = '父级代码'
t.bdRegion.cname = '常用名称'

/* 运单号库 BdTrackingNo */
t.bdTrackingNo = {}
t.bdTrackingNo.id = 'ID'
t.bdTrackingNo.productOrChannel = '运单号库类型'
t.bdTrackingNo.productOrChannelId = '产品/渠道/号段组'
t.bdTrackingNo.waybillNo = '运单号'
t.bdTrackingNo.usedMode = '领用方式'
t.bdTrackingNo.user = '领用人'
t.bdTrackingNo.usedDate = '领用时间'
t.bdTrackingNo.customerId = '客户'
t.bdTrackingNo.customerName = '客户简称'
t.bdTrackingNo.status = '状态'

/* 费用项 BdFeeType */
t.bdFeeType = {}
t.bdFeeType.id = 'ID'
t.bdFeeType.feeCategory = '费用项类别'
t.bdFeeType.name = '费用项名称'
t.bdFeeType.internationalTransportMode = '干线运输方式'
t.bdFeeType.divideMethod = '默认分摊方式'
t.bdFeeType.currency = '默认币种'

/* 物流产品外部编码 BdLogisticsProductOuterCode */
t.bdLogisticsProductOuterCode = {}
t.bdLogisticsProductOuterCode.id = 'ID'
t.bdLogisticsProductOuterCode.outerCode = '外部编码'
t.bdLogisticsProductOuterCode.outerName = '外部编码名称'
t.bdLogisticsProductOuterCode.logisticsProductId = '物流产品'
t.bdLogisticsProductOuterCode.source = '来源'
t.bdLogisticsProductOuterCode.memo = '备注'

/* 汇率 BaCurrencyRate */
t.baCurrencyRate = {}
t.baCurrencyRate.id = 'ID'
t.baCurrencyRate.localCurrency = '本位币种'
t.baCurrencyRate.exchangeCurrency = '兑换币种'
t.baCurrencyRate.exchangeRate = '汇率'
t.baCurrencyRate.effectiveDate = '生效时间'

/* 机场 BdAirport */
t.bdAirport = {}
t.bdAirport.id = 'ID'
t.bdAirport.iataCode = 'IATA代码'
t.bdAirport.icaoCode = 'ICAO代码'
t.bdAirport.country = '国家地区'
t.bdAirport.city = '城市'
t.bdAirport.name = '中文名'
t.bdAirport.englishName = '英文名'

t.sysSerialNumber = {}
t.sysSerialNumber.type = '类型'
t.sysSerialNumber.productOrChannel = '物流产品/渠道'
t.sysSerialNumber.productOrChannelId = '物流产品/渠道'
t.sysSerialNumber.serialNumber = '起始序号'
t.sysSerialNumber.version = '版本号'
t.sysSerialNumber.status = '状态'

/* 航空公司 BdAirlineCompany */
t.bdAirlineCompany = {}
t.bdAirlineCompany.id = 'ID'
t.bdAirlineCompany.iataCode = 'IATA代号'
t.bdAirlineCompany.icaoCode = 'ICAO代号'
t.bdAirlineCompany.numericCode = '数字代号'
t.bdAirlineCompany.name = '中文名'
t.bdAirlineCompany.englishName = '英文名'

/* 港口 BdSeaport */
t.bdSeaport = {}
t.bdSeaport.id = 'ID'
t.bdSeaport.code = '港口代号'
t.bdSeaport.country = '国家或地区'
t.bdSeaport.city = '城市'
t.bdSeaport.name = '中文名'
t.bdSeaport.englishName = '英文名'

/* 船公司 BdShippingCompany */
t.bdShippingCompany = {}
t.bdShippingCompany.id = 'ID'
t.bdShippingCompany.englishCode = '英文简称'
t.bdShippingCompany.mainCode = '第一代码'
t.bdShippingCompany.secondCode = '第二代码'
t.bdShippingCompany.countryCode = '国家'
t.bdShippingCompany.name = '中文名'
t.bdShippingCompany.englishName = '英文名'

/* 币种 BdCurrency */
t.bdCurrency = {}
t.bdCurrency.id = 'ID'
t.bdCurrency.code = '币种代号'
t.bdCurrency.name = '币种名称'
t.bdCurrency.customsCode = '海关币制代号'

/* 海运航次 WsVoyageNumber */
t.wsVoyageNumber = {}
t.wsVoyageNumber.id = 'ID'
t.wsVoyageNumber.voyageNo = '航次'
t.wsVoyageNumber.shippingCompany = '船公司'
t.wsVoyageNumber.shippingRoutes = '海运航线'
t.wsVoyageNumber.departurePort = '起运港'
t.wsVoyageNumber.arrivalPort = '目的港'
t.wsVoyageNumber.customsClearanceDate = '截关时间'
t.wsVoyageNumber.sailingDate = '开船时间'
t.wsVoyageNumber.memo = '备注'
/* 银行帐号 BdBankAccount */
t.bdBankAccount = {}
t.bdBankAccount.id = 'ID'
t.bdBankAccount.owner = '账户类型'
t.bdBankAccount.bankCode = '银行名称'
t.bdBankAccount.branchName = '支行'
t.bdBankAccount.name = '账户名'
t.bdBankAccount.accountNumber = '卡号'
t.bdBankAccount.foreignCurrency = '开通外币账户'
t.bdBankAccount.isInner = '仅内部使用'
t.bdBankAccount.status = '状态'
/* 渠道链路成本分析 BdChannelRouteCostAnalysis */
t.bdChannelRouteCostAnalysis = {}
t.bdChannelRouteCostAnalysis.id = 'ID'
t.bdChannelRouteCostAnalysis.logisticsProductId = '物流产品编号'
t.bdChannelRouteCostAnalysis.zone = '分区'
t.bdChannelRouteCostAnalysis.channelRouteId = '渠道链路编号'
t.bdChannelRouteCostAnalysis.weight = '重量'
t.bdChannelRouteCostAnalysis.cost = '成本费用'

/* 物流产品规则 BdLogicsticsProductRule */
t.bdLogicsticsProductRule = {}
t.bdLogicsticsProductRule.id = '物流产品编号'
t.bdLogicsticsProductRule.chargeableWeightRule = '结算重算法'
t.bdLogicsticsProductRule.bubbleRate = '分泡比例(%)'
t.bdLogicsticsProductRule.volDiv = '材积除'
t.bdLogicsticsProductRule.bubbleCountingFormula = '计泡条件'
t.bdLogicsticsProductRule.mismatchingBubbleCountingChargeableWeightRule = '不满足计泡条件结算重算法'
t.bdLogicsticsProductRule.carryRule = '大货界定值'
t.bdLogicsticsProductRule.bigCarry = '大货计量单位'
t.bdLogicsticsProductRule.smallCarry = '小货计量单位'
t.bdLogicsticsProductRule.multiWeightRule = '多件重量算法'
t.bdLogicsticsProductRule.multiWeightCarry = '多件重量进位'
t.bdLogicsticsProductRule.overrunRule = '超限费用算法'
t.bdLogicsticsProductRule.constraintRule = '约束规则'
t.bdLogicsticsProductRule.country = '国家'

/* 物流产品其他信息 BdLogisticsProductOther */
t.bdLogisticsProductOther = {}
t.bdLogisticsProductOther.id = '物流产品编号'
t.bdLogisticsProductOther.earlyWarningInventory = '预警库存'
t.bdLogisticsProductOther.earlyWarningContact = '预警联系人'
t.bdLogisticsProductOther.waybillNoGroupId = '号段组'
t.bdLogisticsProductOther.postcodeRegionConversion = '邮编转省市区'
t.bdLogisticsProductOther.postcodeRegionCheck = '邮编地址库校验'
t.bdLogisticsProductOther.usStateShortCodeConversion = '美国州简码转换'
t.bdLogisticsProductOther.provinceRegionCodeMatch = '省州城市区转换'
t.bdLogisticsProductOther.orderVerifyId = '订单校验模板'
t.bdLogisticsProductOther.returnAddrId = '回邮地址'
t.bdLogisticsProductOther.zoneTemplateId = '分区'
t.bdLogisticsProductOther.remoteZoneTemplateId = '偏远分区'
t.bdLogisticsProductOther.tenXTen = '10x10标签模版'
t.bdLogisticsProductOther.tenXFifteen = '10x15标签模版'
t.bdLogisticsProductOther.tenXEighteen = '10x18标签模版'
t.bdLogisticsProductOther.a4 = 'A4标签模版'
t.bdLogisticsProductOther.labelTemplateId = '标签模板'
t.bdLogisticsProductOther.handoverListId = '交接清单模版'
t.bdLogisticsProductOther.proformaInvoiceId = '形式发票模版'
t.bdLogisticsProductOther.customsDeclarationDocId = '配货单模板'
t.bdLogisticsProductOther.bagCardId = '袋牌模板'
t.bdLogisticsProductOther.returnDeliveryNo = 'OMS显示派送单号'
t.bdLogisticsProductOther.postalTrackingNoAsWaybillNo = '邮政单号做为运单号通过API返回客户'
t.bdLogisticsProductOther.postalTrackingNoAsDeliveryNo = '邮政单号做为派送单号通过API返回客户'
t.bdLogisticsProductOther.moreTypeAddrTitle = '更多类型地址'
t.bdLogisticsProductOther.addrType = '地址类型'
t.bdLogisticsProductOther.addrId = '地址'
t.bdLogisticsProductOther.returnChannelLabel = '额外提供尾程面单'

/* 产品渠道链路维护 BdLogisticsProductChannelRoute */
t.bdLogisticsProductChannelRoute = {}
t.bdLogisticsProductChannelRoute.id = 'ID'
t.bdLogisticsProductChannelRoute.logisticsProductId = '物流产品'
t.bdLogisticsProductChannelRoute.zoneType = '分区类型'
t.bdLogisticsProductChannelRoute.logisticsProductZone = '物流产品分区'
t.bdLogisticsProductChannelRoute.logisticsChannelLinkId = '物流渠道链路'
t.bdLogisticsProductChannelRoute.quickAdd = '快速关联渠道'

/* 物流产品授权 BdLogisticsProductAuthorization */
t.bdLogisticsProductAuthorization = {}
t.bdLogisticsProductAuthorization.id = 'ID'
t.bdLogisticsProductAuthorization.customerType = '客户类型'
t.bdLogisticsProductAuthorization.customerSource = '客户归属'
t.bdLogisticsProductAuthorization.logisticsProductId = '物流产品'
t.bdLogisticsProductAuthorization.objectId = '客户'
t.bdLogisticsProductAuthorization.objectName = '客户名称'

/* 物流产品 BdLogisticsProduct */
t.bdLogisticsProduct = {}
t.bdLogisticsProduct.id = 'ID'
t.bdLogisticsProduct.code = '产品代号'
t.bdLogisticsProduct.codeTip = '若留空，系统会自动编码'
t.bdLogisticsProduct.name = '产品名称'
t.bdLogisticsProduct.newName = '新产品名称'
t.bdLogisticsProduct.businessType = '获取尾程单号节点'
t.bdLogisticsProduct.logisticsType = '订单物流类型'
t.bdLogisticsProduct.logisticsProductType = '产品类型'
t.bdLogisticsProduct.departPortCity = '出发口岸城市'
t.bdLogisticsProduct.specialItem = '特殊物品类型'
t.bdLogisticsProduct.logisticsChannelLinksNode = '渠道链路节点'
t.bdLogisticsProduct.ownLogisticsChannelRoute = '一对一渠道链路'
t.bdLogisticsProduct.sendTime = '时效说明'
t.bdLogisticsProduct.sortNum = '排序'
t.bdLogisticsProduct.waybillNoSource = '运单号来源'
t.bdLogisticsProduct.lableType = '面单类型'
t.bdLogisticsProduct.quotationSource = '报价来源'
t.bdLogisticsProduct.forecastPay = '是否预报计费'
t.bdLogisticsProduct.sellAccount = '是否卖账号'
t.bdLogisticsProduct.forecastLock = '欠费禁止预报'
t.bdLogisticsProduct.qwlCode = '趣物流代码'
t.bdLogisticsProduct.qwlReturnNoField = 'API返回派送单号的取值字段'
t.bdLogisticsProduct.changeLabelService = 'Y2换标服务'

/* 客户面单规则 BdCustomerLables */
t.bdCustomerLables = {}
t.bdCustomerLables.id = 'ID'
t.bdCustomerLables.customerType = '客户类型'
t.bdCustomerLables.customerSource = '客户归属'
t.bdCustomerLables.objectId = '客户'
t.bdCustomerLables.objectName = '客户名称'
t.bdCustomerLables.logisticsProductId = '物流产品'
t.bdCustomerLables.labelTemplateId = '标签模版'
t.bdCustomerLables.handoverListId = '交接清单模版'
t.bdCustomerLables.proformaInvoiceId = '形式发票模版'
t.bdCustomerLables.customsDeclarationDocId = '配货单模板'

/* 干线运输渠道-海运明细 BdTrunkLineChannelOceanShipping */
t.bdTrunkLineChannelOceanShipping = {}
t.bdTrunkLineChannelOceanShipping.id = 'ID'
t.bdTrunkLineChannelOceanShipping.trunkLineId = '干线渠道编号'
t.bdTrunkLineChannelOceanShipping.departurePort = '起运港口'
t.bdTrunkLineChannelOceanShipping.transferPort = '中转港口'
t.bdTrunkLineChannelOceanShipping.arrivalPort = '抵达港口'
t.bdTrunkLineChannelOceanShipping.shippingRoutes = '海运航线'
t.bdTrunkLineChannelOceanShipping.shipCompany = '船公司'

/* 干线运输渠道-空运明细 BdTrunkLineChannelAir */
t.bdTrunkLineChannelAir = {}
t.bdTrunkLineChannelAir.id = 'ID'
t.bdTrunkLineChannelAir.trunkLineId = '干线渠道编号'
t.bdTrunkLineChannelAir.takeoffAirport = '起飞机场'
t.bdTrunkLineChannelAir.transferAirport = '中转机场'
t.bdTrunkLineChannelAir.landingAirport = '降落机场'
t.bdTrunkLineChannelAir.airRoutes = '航空航线'
t.bdTrunkLineChannelAir.airlineCompany = '航空公司'
t.bdTrunkLineChannelAir.isCharged = '是否带电'

/* 尾程派送渠道 BdLogisticsChannel */
t.bdLogisticsChannel = {}
t.bdLogisticsChannel.id = 'ID'
t.bdLogisticsChannel.code = '渠道代号'
t.bdLogisticsChannel.name = '渠道名称'
t.bdLogisticsChannel.newname = '新渠道名称'
t.bdLogisticsChannel.logisticsChannelType = '物流渠道类型'
t.bdLogisticsChannel.providerId = '供应商'
t.bdLogisticsChannel.specialItem = '特殊物品类型'
t.bdLogisticsChannel.shippingAddressSource = '发货地址来源'
t.bdLogisticsChannel.withholdingFee = '下单预扣费'
t.bdLogisticsChannel.mustUseContainer = '出库需装袋/打托'
t.bdLogisticsChannel.targetDispatchHandoverId = '尾程派送交接地'
t.bdLogisticsChannel.beginWeight = '起始重量'
t.bdLogisticsChannel.endWeight = '结束重量'
t.bdLogisticsChannel.weightUnit = '重量单位'
t.bdLogisticsChannel.lengthUnit = '长度单位'
t.bdLogisticsChannel.maxTime = '最长耗时(天)'
t.bdLogisticsChannel.minTime = '最短耗时(天)'
t.bdLogisticsChannel.sortNum = '排序'
t.bdLogisticsChannel.waybillNoSource = '单号来源'
t.bdLogisticsChannel.postalTrackingNoSource = '邮政单号来源'
t.bdLogisticsChannel.trackPlatformType = '轨迹平台类型'
t.bdLogisticsChannel.carrierCode = '尾程服务商代码'
t.bdLogisticsChannel.apiTypeId = 'API接口类型'
t.bdLogisticsChannel.trackNoSource = '轨迹单号来源'
t.bdLogisticsChannel.waybillNo = '运单号'
t.bdLogisticsChannel.deliveryNo = '派送单号'
t.bdLogisticsChannel.postalTrackingNo = '邮政单号'
t.bdLogisticsChannel.providerNo = '供应商单号'
t.bdLogisticsChannel.customerOrderNo = '客户单号'
t.bdLogisticsChannel.pushNode = '触发节点'
t.bdLogisticsChannel.bagNoSource = '袋牌号来源'
t.bdLogisticsChannel.bagLabelSource = '袋牌Label来源'
t.bdLogisticsChannel.postageProofLabelSource = '发运证明来源'
t.bdLogisticsChannel.signProofLabelSource = '签收证明来源'
t.bdLogisticsChannel.apiPushNode = '其他API推送节点'
t.bdLogisticsChannel.pushApiNoType = 'API参考号来源'
t.bdLogisticsChannel.apiRelatedSettings = 'API相关设置'
t.bdLogisticsChannel.apiProductPushTrack = '轨迹推送'
t.bdLogisticsChannel.pushTrackProviderId = '接收轨迹推送的供应商'
t.bdLogisticsChannel.showProviderTransitionNo = '显示供应商中性单号给客户'
t.bdLogisticsChannel.providerTransitionNoRule = '中性单号正则表达式'
t.bdLogisticsChannel.providerTransitionNoRuleRemark = '正则表达式说明'
t.bdLogisticsChannel.getFinalTnoNode = '自动获取最终单号开始节点'
t.bdLogisticsChannel.getFinalTnoDelayTime = '自动获取最终单号延迟时间'
t.bdLogisticsChannel.pushInboundPathNode = '推送预上网轨迹节点'

/* 尾程派送渠道规则 BdLogisticsChannelRule */
t.bdLogisticsChannelRule = {}
t.bdLogisticsChannelRule.id = '渠道编号'
t.bdLogisticsChannelRule.chargeableWeightRule = '结算重算法'
t.bdLogisticsChannelRule.volDiv = '材积除'
t.bdLogisticsChannelRule.bubbleCountingFormula = '计泡条件'
t.bdLogisticsChannelRule.carryFrontier = '大货界定值'
t.bdLogisticsChannelRule.bigCarry = '大货进位值'
t.bdLogisticsChannelRule.smallCarry = '小货进位值'
t.bdLogisticsChannelRule.multiWeightRule = '多件重量算法'
t.bdLogisticsChannelRule.overrunRule = '超限费用算法'

/* 物流渠道API类型 BdLogisticsChannelApiType */
t.bdLogisticsChannelApiType = {}
t.bdLogisticsChannelApiType.id = 'ID'
t.bdLogisticsChannelApiType.channelTypeName = '渠道类型名称'
t.bdLogisticsChannelApiType.remark = '描述'
t.bdLogisticsChannelApiType.sourceType = '接口来源'

/* 物流渠道API类型明细 BdLogisticsChannelApiTypeDetail */
t.bdLogisticsChannelApiTypeDetail = {}
t.bdLogisticsChannelApiTypeDetail.id = 'ID'
t.bdLogisticsChannelApiTypeDetail.apiTypeId = '渠道API类型编号'
t.bdLogisticsChannelApiTypeDetail.paramKey = '参数名'
t.bdLogisticsChannelApiTypeDetail.paramValue = '参数值'
t.bdLogisticsChannelApiTypeDetail.isUserKey = '是否为账号参数'
t.bdLogisticsChannelApiTypeDetail.remark = '备注'

/* 境外清关渠道 BdTargetCustomsChannel */
t.bdTargetCustomsChannel = {}
t.bdTargetCustomsChannel.id = 'ID'
t.bdTargetCustomsChannel.code = '渠道代号'
t.bdTargetCustomsChannel.name = '渠道名称'
t.bdTargetCustomsChannel.country = '国家'
t.bdTargetCustomsChannel.providerId = '供应商'
t.bdTargetCustomsChannel.customsPort = '清关口岸'
t.bdTargetCustomsChannel.airport = '落地机场'
t.bdTargetCustomsChannel.customsMode = '清关模式'
t.bdTargetCustomsChannel.maxTime = '最长耗时(天)'
t.bdTargetCustomsChannel.minTime = '最短耗时(天)'
t.bdTargetCustomsChannel.sortNum = '排序'
t.bdTargetCustomsChannel.addressSource = '发货地址来源'
t.bdTargetCustomsChannel.apiType = 'API接口类型'
t.bdTargetCustomsChannel.apiTypeId = 'API接口名称'

/* 境内清关渠道api参数 BdSourceCustomsChannelApi */
t.bdSourceCustomsChannelApi = {}
t.bdSourceCustomsChannelApi.id = 'ID'
t.bdSourceCustomsChannelApi.sourceCustomsId = '境内清关编号'
t.bdSourceCustomsChannelApi.paramKey = '参数名'
t.bdSourceCustomsChannelApi.paramValue = '参数值'
t.bdSourceCustomsChannelApi.remark = '备注'

/* 境内清关渠道 BdSourceCustomsChannel */
t.bdSourceCustomsChannel = {}
t.bdSourceCustomsChannel.id = 'ID'
t.bdSourceCustomsChannel.code = '渠道代号'
t.bdSourceCustomsChannel.name = '渠道名称'
t.bdSourceCustomsChannel.country = '国家'
t.bdSourceCustomsChannel.providerId = '供应商'
t.bdSourceCustomsChannel.customsPort = '清关口岸'
t.bdSourceCustomsChannel.airport = '起运机场'
t.bdSourceCustomsChannel.customsMode = '清关模式'
t.bdSourceCustomsChannel.tariffDeliveryMode = '关税交付方式'
t.bdSourceCustomsChannel.maxTime = '最长耗时(天)'
t.bdSourceCustomsChannel.minTime = '最短耗时(天)'
t.bdSourceCustomsChannel.sortNum = '排序'
t.bdSourceCustomsChannel.addressSource = '发货地址来源'
t.bdSourceCustomsChannel.apiType = 'API接口类型'
t.bdSourceCustomsChannel.apiTypeId = 'API接口类型'

/* 干线运输渠道api参数 BdTrunkLineChannelApi */
t.bdTrunkLineChannelApi = {}
t.bdTrunkLineChannelApi.id = 'ID'
t.bdTrunkLineChannelApi.trunkLineId = '干线运输编号'
t.bdTrunkLineChannelApi.paramKey = '参数名'
t.bdTrunkLineChannelApi.paramValue = '参数值'
t.bdTrunkLineChannelApi.remark = '备注'

/* 干线运输渠道 BdTrunkLineChannel */
t.bdTrunkLineChannel = {}
t.bdTrunkLineChannel.id = 'ID'
t.bdTrunkLineChannel.code = '渠道代号'
t.bdTrunkLineChannel.name = '渠道名称'
t.bdTrunkLineChannel.trunkTransportWay = '运输方式'
t.bdTrunkLineChannel.providerId = '供应商'
t.bdTrunkLineChannel.deliverCountry = '起运地'
t.bdTrunkLineChannel.arrivalCountry = '目的地'
t.bdTrunkLineChannel.maxTime = '最长耗时(天)'
t.bdTrunkLineChannel.minTime = '最短耗时(天)'
t.bdTrunkLineChannel.elapsedTime = '时效(天)'
t.bdTrunkLineChannel.sortNum = '排序'
t.bdTrunkLineChannel.addressSource = '发货地址来源'
t.bdTrunkLineChannel.apiType = 'API接口类型'
t.bdTrunkLineChannel.apiTypeId = 'API接口类型名称'

/* 物流渠道链路 BdLogisticsChannelRoute */
t.bdLogisticsChannelRoute = {}
t.bdLogisticsChannelRoute.id = 'ID'
t.bdLogisticsChannelRoute.code = '渠道链路代号'
t.bdLogisticsChannelRoute.name = '渠道链路名称'
t.bdLogisticsChannelRoute.sourceCustomsId = '境内清关渠道'
t.bdLogisticsChannelRoute.trunkLineId = '干线运输渠道'
t.bdLogisticsChannelRoute.targetCustomsId = '境外清关渠道'
t.bdLogisticsChannelRoute.targetHandoverId = '尾程交接渠道'
t.bdLogisticsChannelRoute.logisticsChannelId = '尾程派送渠道'
t.bdLogisticsChannelRoute.collectPlaceId = '揽收地'
t.bdLogisticsChannelRoute.handoverPlaceId = '交接地'
t.bdLogisticsChannelRoute.maxTime = '最长耗时(天)'
t.bdLogisticsChannelRoute.minTime = '最短耗时(天)'
t.bdLogisticsChannelRoute.sortNum = '排序'
t.bdLogisticsChannelRoute.remark = '备注'

/* 尾程派送渠道邮编映射 BdLogisticsChannelPostcode */
t.bdLogisticsChannelPostcode = {}
t.bdLogisticsChannelPostcode.id = 'ID'
t.bdLogisticsChannelPostcode.logisticsChannelRouteId = '物流渠道链路编号'
t.bdLogisticsChannelPostcode.whetherInclude = '是否包括'
t.bdLogisticsChannelPostcode.country = '国家'
t.bdLogisticsChannelPostcode.postcode = '邮编'
t.bdLogisticsChannelPostcode.startPostcode = '起始邮编'
t.bdLogisticsChannelPostcode.endPostcode = '结束邮编'

/* 尾程派送渠道其他信息表 BdLogisticsChannelOther */
t.bdLogisticsChannelOther = {}
t.bdLogisticsChannelOther.id = '渠道编号'
t.bdLogisticsChannelOther.orderVerifyId = '订单校验'
t.bdLogisticsChannelOther.returnAddrId = '回邮地址'
t.bdLogisticsChannelOther.zoneTemplateId = '分区'
t.bdLogisticsChannelOther.remoteZoneTemplateId = '偏远分区'
t.bdLogisticsChannelOther.labelTemplateId = '标签模板'
t.bdLogisticsChannelOther.tenXTen = '10x10标签模版'
t.bdLogisticsChannelOther.tenXFifteen = '10x15标签模版'
t.bdLogisticsChannelOther.tenXEighteen = '10x18标签模版'
t.bdLogisticsChannelOther.a4 = 'A4标签模版'
t.bdLogisticsChannelOther.handoverListId = '交接清单模版'
t.bdLogisticsChannelOther.proformaInvoiceId = '形式发票模版'
t.bdLogisticsChannelOther.customsDeclarationDocId = '配货单模板'
t.bdLogisticsChannelOther.geFromGroupTip = '从运单号库中获取,需要将运单号导入到运单号库中。如果是FBA、快递的渠道,同时需要导入子运单号'
t.bdLogisticsChannelOther.pushCustomerToProvderApiTip = 'Y2模式下。派送单号和面单客户预报时无法立刻提供，延时获取并单号面单后需要推送到供应商时，填写以下api信息'

/* 中转交接渠道api参数 BdTransitHandoverChannelApi */
t.bdTransitHandoverChannelApi = {}
t.bdTransitHandoverChannelApi.id = 'ID'
t.bdTransitHandoverChannelApi.transitHandoverChannelId = '中转交接渠道编号'
t.bdTransitHandoverChannelApi.paramKey = '参数名'
t.bdTransitHandoverChannelApi.fieldValue = '参数值'
t.bdTransitHandoverChannelApi.remark = '备注'

/* 尾程派送api参数 BdLogisticsChannelApi */
t.bdLogisticsChannelApi = {}
t.bdLogisticsChannelApi.id = 'ID'
t.bdLogisticsChannelApi.logisticsChannelId = '尾程派送渠道编号'
t.bdLogisticsChannelApi.paramKey = '参数名'
t.bdLogisticsChannelApi.paramValue = '参数值'
t.bdLogisticsChannelApi.remark = '备注'

/* 尾程交接渠道api参数 BdTargetHandoverChannelApi */
t.bdTargetHandoverChannelApi = {}
t.bdTargetHandoverChannelApi.id = 'ID'
t.bdTargetHandoverChannelApi.targetHandoverId = '尾程交接渠道编号'
t.bdTargetHandoverChannelApi.fieldKey = '参数名'
t.bdTargetHandoverChannelApi.fieldValue = '参数值'
t.bdTargetHandoverChannelApi.remark = '备注'

/* 尾程交接渠道 BdTargetHandoverChannel */
t.bdTargetHandoverChannel = {}
t.bdTargetHandoverChannel.id = 'ID'
t.bdTargetHandoverChannel.code = '渠道代号'
t.bdTargetHandoverChannel.name = '渠道名称'
t.bdTargetHandoverChannel.transportWay = '运输方式'
t.bdTargetHandoverChannel.providerId = '供应商'
t.bdTargetHandoverChannel.country = '所在国家'
t.bdTargetHandoverChannel.sourceAddr = '起运地'
t.bdTargetHandoverChannel.targetAddr = '目的地'
t.bdTargetHandoverChannel.maxTime = '最长耗时(天)'
t.bdTargetHandoverChannel.minTime = '最短耗时(天)'
t.bdTargetHandoverChannel.sortNum = '排序'
t.bdTargetHandoverChannel.apiType = 'API接口类型'
t.bdTargetHandoverChannel.apiTypeId = 'API接口名称'

/* 境外清关渠道api参数 BdTargetCustomsChannelApi */
t.bdTargetCustomsChannelApi = {}
t.bdTargetCustomsChannelApi.id = 'ID'
t.bdTargetCustomsChannelApi.targetCustomerId = '境外清关渠道编号'
t.bdTargetCustomsChannelApi.paramKey = '参数名'
t.bdTargetCustomsChannelApi.paramValue = '参数值'
t.bdTargetCustomsChannelApi.remark = '备注'

/* 中转交接渠道表 BdTransitHandoverChannel */
t.bdTransitHandoverChannel = {}
t.bdTransitHandoverChannel.id = 'ID'
t.bdTransitHandoverChannel.code = '渠道代号'
t.bdTransitHandoverChannel.name = '渠道名称'
t.bdTransitHandoverChannel.transportWay = '运输方式'
t.bdTransitHandoverChannel.providerId = '供应商'
t.bdTransitHandoverChannel.sourceAddr = '起运地'
t.bdTransitHandoverChannel.targetAddr = '目的地'
t.bdTransitHandoverChannel.vehicleType = '车型'
t.bdTransitHandoverChannel.transportMode = '运输模式'
t.bdTransitHandoverChannel.maxTime = '最长耗时(天)'
t.bdTransitHandoverChannel.minTime = '最短耗时(天)'
t.bdTransitHandoverChannel.sortNum = '排序'
t.bdTransitHandoverChannel.apiType = 'API接口类型'
t.bdTransitHandoverChannel.truckload = '整车'
t.bdTransitHandoverChannel.bulkCargo = '零担'
t.bdTransitHandoverChannel.baseInfo = '基础信息'
t.bdTransitHandoverChannel.pushData = '数据推送'
t.bdTransitHandoverChannel.noApi = '不获取'
t.bdTransitHandoverChannel.api = 'api'
t.bdTransitHandoverChannel.landTransport = '陆运'

/* 交接地 BdHandoverPlace */
t.bdHandoverPlace = {}
t.bdHandoverPlace.id = 'ID'
t.bdHandoverPlace.code = '代号'
t.bdHandoverPlace.name = '名称'
t.bdHandoverPlace.country = '国家'
t.bdHandoverPlace.province = '省/州'
t.bdHandoverPlace.city = '市'
t.bdHandoverPlace.district = '区/县'
t.bdHandoverPlace.address = '详细地址'

/* 订单校验明细 BdOrderVerificationDetail */
t.bdOrderVerificationDetail = {}
t.bdOrderVerificationDetail.id = 'ID'
t.bdOrderVerificationDetail.orderVerificationTempateId = '订单校验模板编号'
t.bdOrderVerificationDetail.type = '类型'
t.bdOrderVerificationDetail.fieldKey = '字段属性'
t.bdOrderVerificationDetail.country = '收件国家限定'
t.bdOrderVerificationDetail.fieldName = '字段属性名称'
t.bdOrderVerificationDetail.fieldType = '字段类型'
t.bdOrderVerificationDetail.required = '是否必填'
t.bdOrderVerificationDetail.minLength = '最小长度'
t.bdOrderVerificationDetail.maxLength = '最大长度'
t.bdOrderVerificationDetail.minValue = '最小值'
t.bdOrderVerificationDetail.maxValue = '最大值'
t.bdOrderVerificationDetail.includeCn = '是否包含中文'
t.bdOrderVerificationDetail.blacklistType = '黑名单模板类型'
t.bdOrderVerificationDetail.blacklistTemplateId = '黑名单模板'
t.bdOrderVerificationDetail.regularExpression = '正则表达式'
t.bdOrderVerificationDetail.regularExpressionTip = '正则表达式校验提示'
t.bdOrderVerificationDetail.formulaExpression = '公式表达式'
t.bdOrderVerificationDetail.formulaExpressionTip = '公式表达式校验提示'
t.bdOrderVerificationDetail.remark = '备注'

/* 运单截取规则 BdCutRule */
t.bdCutRule = {}
t.bdCutRule.id = 'ID'
t.bdCutRule.cutRule = '截取规则'
t.bdCutRule.cutBeginPosition = '截取开始位置'
t.bdCutRule.cutLength = '截取结束位置'
t.bdCutRule.remark = '备注'

/* 分区模板 BdZoneTemplate */
t.bdZoneTemplate = {}
t.bdZoneTemplate.id = 'ID'
t.bdZoneTemplate.code = '代号'
t.bdZoneTemplate.name = '名称'
t.bdZoneTemplate.tag = '标签'
t.bdZoneTemplate.type = '分区类型'
t.bdZoneTemplate.scope = '地域范围'
t.bdZoneTemplate.specialType = '特殊类型'
t.bdZoneTemplate.qty = '分区数量'
t.bdZoneTemplate.batchAdd = '快速添加分区模版'

/* 分区模板明细 bdZoneTemplateDetail */
t.bdZoneTemplateDetail = {}
t.bdZoneTemplateDetail.id = 'ID'
t.bdZoneTemplateDetail.zoneTemplateId = '分区模板编号'
t.bdZoneTemplateDetail.zone = '分区'
t.bdZoneTemplateDetail.country = '国家'
t.bdZoneTemplateDetail.province = '省州'
t.bdZoneTemplateDetail.city = '城市'
t.bdZoneTemplateDetail.district = '区'
t.bdZoneTemplateDetail.beginPostcode = '起始邮编'
t.bdZoneTemplateDetail.endPostcode = '结束邮编'
t.bdZoneTemplateDetail.postcode = '邮编'
t.bdZoneTemplateDetail.fbaWarehouseCode = 'FBA仓库代码'
t.bdZoneTemplateDetail.fbaWarehouseInfo = 'FBA仓库信息'
t.bdZoneTemplateDetail.allImport = '城市邮编导入'
t.bdZoneTemplateDetail.singleImport = '单列邮编导入'
t.bdZoneTemplateDetail.singleCountryImport = '单列国家导入'
t.bdZoneTemplateDetail.singleFbaWarehouseImport = 'FBA仓库导入'

/* 订单校验模板 BdOrderVerificationTemplate */
t.bdOrderVerificationTemplate = {}
t.bdOrderVerificationTemplate.id = 'ID'
t.bdOrderVerificationTemplate.code = '代号'
t.bdOrderVerificationTemplate.name = '名称'

/* 超限报价 PsExpressBaseQuotationOverstep */
t.psExpressBaseQuotationOverstep = {}
t.psExpressBaseQuotationOverstep.id = 'ID'
t.psExpressBaseQuotationOverstep.baseQuotationId = '基础报价'
t.psExpressBaseQuotationOverstep.parcelType = '包裹类型'
t.psExpressBaseQuotationOverstep.billingMethod = '计费方式'
t.psExpressBaseQuotationOverstep.conditionVal = '超限条件'
t.psExpressBaseQuotationOverstep.byZone = '按分区报价'
t.psExpressBaseQuotationOverstep.price = '金额'
t.psExpressBaseQuotationOverstep.validFormula = '校验公式'
t.psExpressBaseQuotationOverstep.parcelTypeRequired = '第{index}行,包裹类型必填'
t.psExpressBaseQuotationOverstep.byPackageRequired = '第{index}行,按件计费必填'
t.psExpressBaseQuotationOverstep.billingMethodRequired = '第{index}行,计费方式必填'
t.psExpressBaseQuotationOverstep.byZoneRequired = '第{index}行,按分区报价必填'
t.psExpressBaseQuotationOverstep.conditionValRequired = '第{index}行,超限条件必填'
t.psExpressBaseQuotationOverstep.conditionValMaxLength = '第{index}行,超限条件不能大于{max}'
t.psExpressBaseQuotationOverstep.conditionValMaxLength = '第{index}行,超限条件不能大于{max}'
t.psExpressBaseQuotationOverstep.priceRequired = '第{index}行,金额必填'
t.psExpressBaseQuotationOverstep.priceRegex = '第{index}行,最低金额大于0小于99999的4位小数'
t.psExpressBaseQuotationOverstep.validatedFormula = '公式合法有效'
t.psExpressBaseQuotationOverstep.formulaDescription = '公式说明'
t.psExpressBaseQuotationOverstep.variables = '变量说明'
/* 超限费明细 PsExpressBaseQuotationOverstepItem */
t.psExpressBaseQuotationOverstepItem = {}
t.psExpressBaseQuotationOverstepItem.id = 'ID'
t.psExpressBaseQuotationOverstepItem.overstepId = '超限费ID'
t.psExpressBaseQuotationOverstepItem.zone = '分区'
t.psExpressBaseQuotationOverstepItem.addressType = '地址类型'
t.psExpressBaseQuotationOverstepItem.price = '金额'
/* 附加费明细 PsExpressBaseQuotationAdditionItem */
t.psExpressBaseQuotationAdditionItem = {}
t.psExpressBaseQuotationAdditionItem.id = 'ID'
t.psExpressBaseQuotationAdditionItem.additionId = '附加费ID'
t.psExpressBaseQuotationAdditionItem.zone = '分区'
t.psExpressBaseQuotationAdditionItem.beginWeight = '起始重量'
t.psExpressBaseQuotationAdditionItem.endWeight = '结束重量'
t.psExpressBaseQuotationAdditionItem.formula = '公式'
t.psExpressBaseQuotationAdditionItem.price = '金额'

/* 附加费 PsExpressBaseQuotationAddition */
t.psExpressBaseQuotationAddition = {}
t.psExpressBaseQuotationAddition.id = 'ID'
t.psExpressBaseQuotationAddition.baseQuotationId = '基础报价'
t.psExpressBaseQuotationAddition.costItem = '费用项'
t.psExpressBaseQuotationAddition.formula = '计算公式'
t.psExpressBaseQuotationAddition.formulaRequired = '第{index}行,计算公式为空'
t.psExpressBaseQuotationAddition.miniValue = '最低费用'
t.psExpressBaseQuotationAddition.byPackage = '按件计费'
t.psExpressBaseQuotationAddition.fuel = '计算燃油'
t.psExpressBaseQuotationAddition.zoneWeight = '按分区重量段'
t.psExpressBaseQuotationAddition.formulaMaxLength = '第{index}行,计算公式不能大于{max}'
t.psExpressBaseQuotationAddition.miniValueDRequired = '第{index}行,金额必填'
t.psExpressBaseQuotationAddition.miniValueDRegex = '第{index}行,金额大于0小于99999的{max}位小数'
t.psExpressBaseQuotationAddition.costItemRequired = '第{index}行,费用项必填'
t.psExpressBaseQuotationAddition.fuelRequired = '第{index}行,计算燃油必填'
t.psExpressBaseQuotationAddition.zoneWeightRequired = '第{index}行,按分区重量段必填'

/* 供应商报价 PsExpressProviderQuotation */
t.psExpressProviderQuotation = {}
t.psExpressProviderQuotation.id = 'ID'
t.psExpressProviderQuotation.baseQuotationCode = '基础报价'
t.psExpressProviderQuotation.providerId = '供应商'
t.psExpressProviderQuotation.exclusive = '是否特殊报价'
t.psExpressProviderQuotation.effectDate = '生效时间'
t.psExpressProviderQuotation.logisticsChannelCode = '物流渠道'
t.psExpressProviderQuotation.currency = '币种'
t.psExpressProviderQuotation.fuel = '计算燃油'
t.psExpressProviderQuotation.minimumChargePerOrder = '最低消费/票'
t.psExpressProviderQuotation.minimumChargePerPackage = '最低消费/件'
t.psExpressProviderQuotation.weightUnit = '重量单位'
t.psExpressProviderQuotation.lengthUnit = '长度单位'
t.psExpressProviderQuotation.remark = '备注'

/* 加盟商报价 PsExpressAllianceQuotation */
t.psExpressAllianceQuotation = {}
t.psExpressAllianceQuotation.id = 'ID'
t.psExpressAllianceQuotation.exclusive = '是否特殊报价'
t.psExpressAllianceQuotation.logisticsProductCode = '物流产品'
t.psExpressAllianceQuotation.baseQuotationCode = '基础报价'
t.psExpressAllianceQuotation.currency = '币种'
t.psExpressAllianceQuotation.fuel = '计算燃油'
t.psExpressAllianceQuotation.weightUnit = '重量单位'
t.psExpressAllianceQuotation.lengthUnit = '长度单位'
t.psExpressAllianceQuotation.allianceId = '加盟商'
t.psExpressAllianceQuotation.effectDate = '生效时间'

/* 客户专属报价 PsExpressCustomerExclusiveQuotation */
t.psExpressCustomerExclusiveQuotation = {}
t.psExpressCustomerExclusiveQuotation.id = 'ID'
t.psExpressCustomerExclusiveQuotation.baseQuotationCode = '基础报价'
t.psExpressCustomerExclusiveQuotation.exclusive = '是否专属报价'
t.psExpressCustomerExclusiveQuotation.customerId = '客户'
t.psExpressCustomerExclusiveQuotation.customerName = '客户'
t.psExpressCustomerExclusiveQuotation.logisticsProductCode = '物流产品'
t.psExpressCustomerExclusiveQuotation.currency = '币种'
t.psExpressCustomerExclusiveQuotation.fuel = '计算燃油'
t.psExpressCustomerExclusiveQuotation.billingItemType = '运费计费物品类型'
t.psExpressCustomerExclusiveQuotation.minimumChargePerOrder = '最低消费/票'
t.psExpressCustomerExclusiveQuotation.minimumChargePerPackage = '最低消费/件'
t.psExpressCustomerExclusiveQuotation.weightUnit = '重量单位'
t.psExpressCustomerExclusiveQuotation.lengthUnit = '长度单位'
t.psExpressCustomerExclusiveQuotation.effectDate = '生效时间'
t.psExpressCustomerExclusiveQuotation.remark = '备注'
t.psExpressCustomerExclusiveQuotation.hideCustomer = '折叠客户'
t.psExpressCustomerExclusiveQuotation.oldQuotation = '往期启用报价客户'

/* 客户等级报价 PsExpressCustomerClassQuotation */
t.psExpressCustomerClassQuotation = {}
t.psExpressCustomerClassQuotation.id = 'ID'
t.psExpressCustomerClassQuotation.baseQuotationCode = '基础报价'
t.psExpressCustomerClassQuotation.customerId = '客户'
t.psExpressCustomerClassQuotation.effectDate = '生效时间'

/* 偏远费 PsExpressBaseQuotationRemote */
t.psExpressBaseQuotationRemote = {}
t.psExpressBaseQuotationRemote.id = 'ID'
t.psExpressBaseQuotationRemote.baseQuotationId = '基础报价'
t.psExpressBaseQuotationRemote.zone = '分区'
t.psExpressBaseQuotationRemote.price = '单价'
t.psExpressBaseQuotationRemote.fuel = '计算燃油'
t.psExpressBaseQuotationRemote.byPackage = '按件计费'
t.psExpressBaseQuotationRemote.minimumCharge = '最低消费金额'

/* 报价明细 PsExpressBaseQuotationPrice */
t.psExpressBaseQuotationPrice = {}
t.psExpressBaseQuotationPrice.id = 'ID'
t.psExpressBaseQuotationPrice.baseQuotationId = '基础报价'
t.psExpressBaseQuotationPrice.zone = '分区'
t.psExpressBaseQuotationPrice.parcelType = '包裹类型'
t.psExpressBaseQuotationPrice.beginWeight = '起始重量'
t.psExpressBaseQuotationPrice.endWeight = '结束重量'
t.psExpressBaseQuotationPrice.culcMethod = '计算方法'
t.psExpressBaseQuotationPrice.unitWeight = '单位重量'
t.psExpressBaseQuotationPrice.beginPrice = '起价'
t.psExpressBaseQuotationPrice.price = '金额/单价'
t.psExpressBaseQuotationPrice.formula = '计算公式'
t.psExpressBaseQuotationPrice.weight = '重量'

/* 基础报价折扣 PsExpressBaseQuotationDiscount */
t.psExpressBaseQuotationDiscount = {}
t.psExpressBaseQuotationDiscount.id = 'ID'
t.psExpressBaseQuotationDiscount.baseQuotaionId = '基础报价'
t.psExpressBaseQuotationDiscount.parcelType = '包裹类型'
t.psExpressBaseQuotationDiscount.zone = '分区'
t.psExpressBaseQuotationDiscount.beginWeight = '起始重量'
t.psExpressBaseQuotationDiscount.endWeight = '结束重量'
t.psExpressBaseQuotationDiscount.discount = '折扣'

/* 尾程派送基础报价 PsExpressBaseQuotation */
t.psExpressBaseQuotation = {}
t.psExpressBaseQuotation.id = 'ID'
t.psExpressBaseQuotation.code = '编码'
t.psExpressBaseQuotation.publish = '是否公布价'
t.psExpressBaseQuotation.level = '报价等级'
t.psExpressBaseQuotation.type = '类型'
t.psExpressBaseQuotation.logisticsObjectCode = '物流产品/渠道'
t.psExpressBaseQuotation.currency = '币种'
t.psExpressBaseQuotation.fuel = '计算燃油'
t.psExpressBaseQuotation.billingItemType = '运费计费物品类型'
t.psExpressBaseQuotation.minimumChargeType = '低消类型'
t.psExpressBaseQuotation.billingItemType = '运费计费物品类型'
t.psExpressBaseQuotation.minimumChargePerOrder = '最低消费/票'
t.psExpressBaseQuotation.minimumChargePerPackage = '最低消费/件'
t.psExpressBaseQuotation.weightUnit = '重量单位'
t.psExpressBaseQuotation.lengthUnit = '长度单位'
t.psExpressBaseQuotation.effectDate = '生效时间'
t.psExpressBaseQuotation.remark = '备注'
t.psExpressBaseQuotation.desc = '走货说明'
/* 尾程派送基础报价 PsExpressBaseQuotation */
t.psCalculateExpense = {}
// t.psCalculateExpense.customerType = '客户类型'
// t.psCalculateExpense.providerType = '供应商类型'
t.psCalculateExpense.customer = '客户'
t.psCalculateExpense.quotationRemark = '走货说明'
t.psCalculateExpense.alliance = '加盟商'
t.psCalculateExpense.provider = '供应商'
t.psCalculateExpense.logisticsChannel = '物流渠道'
t.psCalculateExpense.customerQuotationType = '报价类型'
t.psCalculateExpense.logisticsProduct = '物流产品'
t.psCalculateExpense.aging = '时效'
t.psCalculateExpense.zone = '分区'
t.psCalculateExpense.country = '国家'
t.psCalculateExpense.city = '城市'
t.psCalculateExpense.district = '区'
t.psCalculateExpense.postcode = '邮编'
t.psCalculateExpense.productType = '货品类型'
t.psCalculateExpense.length = '长(cm)'
t.psCalculateExpense.width = '宽(cm)'
t.psCalculateExpense.height = '高(cm)'
t.psCalculateExpense.weight = '实重(kg)'
t.psCalculateExpense.calculate = '计算'
t.psCalculateExpense.feeType = '费用项'
t.psCalculateExpense.sum = '报价金额'
t.psCalculateExpense.currency = '报价币种'
t.psCalculateExpense.address = '详细地址'
t.psCalculateExpense.city = '城市'
t.psCalculateExpense.province = '省/州'
t.psCalculateExpense.street = '街道'
t.psCalculateExpense.name = '收件人/公司'
t.psCalculateExpense.phone = '电话'
t.psCalculateExpense.weightUnit = '报价重量单位'
t.psCalculateExpense.balanceWeight = '报价结算重'
t.psCalculateExpense.specialItem = '可运物品类型'
t.psCalculateExpense.formula = '计算公式'
t.psCalculateExpense.sysSum = '本位币金额'
t.psCalculateExpense.sysCurrency = '本位币种'
t.psCalculateExpense.sysBalanceWeight = '系统结算重'
t.psCalculateExpense.sysWeightUnit = '系统重量单位'
t.psCalculateExpense.balanceDate = '业务时间'
t.psCalculateExpense.packageQty = '业务时间'
t.psCalculateExpense.declareCurrency = '申报币种'
t.psCalculateExpense.declareSum = '申报金额'

/* 应收结算单 BaReceivableSettleBill */
t.baReceivableSettleBill = {}
t.baReceivableSettleBill.id = '结算单号'
t.baReceivableSettleBill.receivableBillId = '对账单号'
t.baReceivableSettleBill.settlementObjectType = '结算对象类型'
t.baReceivableSettleBill.settlementObjectId = '客户编码'
t.baReceivableSettleBill.settlementObjectName = '客户'
t.baReceivableSettleBill.billDeadline = '帐期结束日期'
t.baReceivableSettleBill.accountingPeriod = '会计期间'
t.baReceivableSettleBill.remark = '核销备注'
t.baReceivableSettleBill.viewRecord = '核销记录'

/* 应付实付费用单 BaPayableBizOrder */
t.baPayableBizOrder = {}
t.baPayableBizOrder.id = '应付费用单号'
t.baPayableBizOrder.payableSettleBillId = '对账单号'
t.baPayableBizOrder.exportErrorMsg = '导出失败:导出不能超过5万条数据'
t.baPayableBizOrder.payable = '应付/实付'
t.baPayableBizOrder.settlementObjectType = '结算对象类型'
t.baPayableBizOrder.settlementObjectId = '供应商编码'
t.baPayableBizOrder.settlementObjectName = '供应商'
t.baPayableBizOrder.settlementObjectBankAccountId = '供应商银行帐号'
t.baPayableBizOrder.orderType = '单据类型'
t.baPayableBizOrder.businessId = '业务单号'
t.baPayableBizOrder.waybillNo = '运单号'
t.baPayableBizOrder.customerVoucherNo = '客户单号'
t.baPayableBizOrder.currency = '币种'
t.baPayableBizOrder.businessType = '业务类型'
t.baPayableBizOrder.sum = '金额'
t.baPayableBizOrder.logisticsChannelCode = '物流渠道'
t.baPayableBizOrder.logisticsProductCode = '物流产品'
t.baPayableBizOrder.balanceWeightD = '结算重'
t.baPayableBizOrder.balanceWeightUnit = '结算重单位'
t.baPayableBizOrder.weightUnit = '实重单位'
t.baPayableBizOrder.weightD = '实重'
t.baPayableBizOrder.lengthUnit = '长度单位'
t.baPayableBizOrder.lengthD = '长*宽*高'
t.baPayableBizOrder.country = '国家'
t.baPayableBizOrder.province = '省/州'
t.baPayableBizOrder.city = '城市'
t.baPayableBizOrder.area = '区'
t.baPayableBizOrder.postCode = '邮编'
t.baPayableBizOrder.optDate = '业务发生时间'
t.baPayableBizOrder.serialNumber = '支付流水号'
t.baPayableBizOrder.billingDate = '计费时间'
t.baPayableBizOrder.billingStatus = '计费状态'
t.baPayableBizOrder.accountingPeriod = '会计期间'
t.baPayableBizOrder.remark = '备注'
t.baPayableBizOrder.auditor = '审核人'
t.baPayableBizOrder.auditDate = '审核时间'

/* 应付结算单 BaPayableSettleBill */
t.baPayableSettleBill = {}
t.baPayableSettleBill.id = '结算单号'
t.baPayableSettleBill.payableBillId = '对账单号'
t.baPayableSettleBill.settlementObjectId = '供应商编码'
t.baPayableSettleBill.settlementObjectName = '供应商'
t.baPayableSettleBill.billDeadline = '帐期结束日期'
t.baPayableSettleBill.accountingPeriod = '会计期间'
t.baPayableSettleBill.memo = '备注'

/* 应付对账单费用明细 BaPayableBillFee */
t.baPayableBillFee = {}
t.baPayableBillFee.id = '应付对账单号'
t.baPayableBillFee.payableBillId = '供应商账单号'
t.baPayableBillFee.feeTypeId = '费用项'
t.baPayableBillFee.businessType = '业务类型'
t.baPayableBillFee.orderType = '单据类型'
t.baPayableBillFee.feeType = '费用类型'
t.baPayableBillFee.businessId = '业务单号'
t.baPayableBillFee.waybillNo = '运单号'
t.baPayableBillFee.deliveryNo = '派送单号'
t.baPayableBillFee.providerNo = '供应商单号'
t.baPayableBillFee.type = '费用归属'
t.baPayableBillFee.currency = '币种'
t.baPayableBillFee.sum = '金额'
t.baPayableBillFee.weightUnit = '重量单位'
t.baPayableBillFee.weightD = '重量(KG)'
t.baPayableBillFee.lengthUnit = '长度单位'
t.baPayableBillFee.lengthD = '长度(CM)'
t.baPayableBillFee.widthD = '宽度(CM)'
t.baPayableBillFee.heightD = '高度(CM)'
t.baPayableBillFee.qty = '数量'
t.baPayableBillFee.country = '国家'
t.baPayableBillFee.postCode = '邮编'
t.baPayableBillFee.checkRemark = '核对备注'
/* 应付计费单 BaBillsPayable */
t.baBillsPayable = {}
t.baBillsPayable.id = 'ID'
t.baBillsPayable.settlementObjectId = '供应商编码'
t.baBillsPayable.settlementObjectName = '供应商'
t.baBillsPayable.customerName = '客户'
t.baBillsPayable.orderType = '单据类型'
t.baBillsPayable.businessId = '业务单号'
t.baBillsPayable.waybillNo = '运单号'
t.baBillsPayable.deliveryNo = '派送单号'
t.baBillsPayable.customerVoucherNo = '客户单号'
t.baBillsPayable.providerNo = '供应商单号'
t.baBillsPayable.logisticsChannelCode = '物流渠道'
t.baBillsPayable.balanceWeight = '结算重'
t.baBillsPayable.balanceWeightUnit = '结算重单位'
t.baBillsPayable.optDate = '业务发生时间'
t.baBillsPayable.receivableRemark = '备注'
t.baBillsPayable.businessOrderTitle = '计费单据'
t.baBillsPayable.orderTypeTitle = '订单信息'
t.baBillsPayable.customerName = '客户名称'
t.baBillsPayable.totalWeight = '总重量(KG)'
t.baBillsPayable.balanceWeight = '结算重(KG)'
t.baBillsPayable.logisticsChannelCode = '物流渠道'
t.baBillsPayable.consigneeCountry = '国家'
t.baBillsPayable.consigneePostcode = '邮编'
t.baBillsPayable.subBills = '子运单明细'
t.baBillsPayable.productName = '货品名称'
t.baBillsPayable.qty = '数量'
t.baBillsPayable.weight = '净重(KG)'
t.baBillsPayable.vol = '体积'
t.baBillsPayable.consigneeCountry = '收件人国家'
t.baBillsPayable.consigneePostcode = '收件人邮编'
t.baBillsPayable.additionFeeType = '偏远超限'

/* 应收计费单 BaBillsReceivable */
t.baBillsReceivable = {}
t.baBillsReceivable.id = 'ID'
t.baBillsReceivable.settlementObjectType = '结算对象类型'
t.baBillsReceivable.settlementObjectId = '客户编码'
t.baBillsReceivable.settlementObjectName = '客户'
t.baBillsReceivable.orderType = '单据类型'
t.baBillsReceivable.businessId = '业务单号'
t.baBillsReceivable.waybillNo = '运单号'
t.baBillsReceivable.deliveryNo = '派送单号'
t.baBillsReceivable.customerVoucherNo = '客户单号'
t.baBillsReceivable.logisticsProductCode = '物流产品'
t.baBillsReceivable.balanceWeight = '结算重'
t.baBillsReceivable.balanceWeightUnit = '结算重单位'
t.baBillsReceivable.optDate = '业务发生时间'
t.baBillsReceivable.consigneeCountry = '收件人国家'
t.baBillsReceivable.consigneePostcode = '收件人邮编'
t.baBillsReceivable.receivableRemark = '备注'
t.baBillsReceivable.billsType = '计费单类型'
t.baBillsReceivable.additionFeeType = '偏远超限'
t.baBillsReceivable.lastFeeSource = '计费依据'
t.baBillsReceivable.zone = '分区'
t.baBillsReceivable.inRemark = '入库备注'

/* 实收款单 BaReceivableSettle */
t.baReceivableSettle = {}
t.baReceivableSettle.id = '实收单号'
t.baReceivableSettle.settlementObjectType = '结算对象类型'
t.baReceivableSettle.settlementObjectId = '客户编码'
t.baReceivableSettle.settlementObjectName = '客户'
t.baReceivableSettle.receivableType = '收款类型'
t.baReceivableSettle.currency = '币种'
t.baReceivableSettle.receivableSum = '应收金额'
t.baReceivableSettle.receivedSum = '实收金额'
t.baReceivableSettle.receivablesDate = '收款时间'
t.baReceivableSettle.bankAccountId = '银行帐号'
t.baReceivableSettle.serialNumber = '支付流水号'
t.baReceivableSettle.recordedPeriod = '会计期间'

/* 实付款单 BaPayableSettle */
t.baPayableSettle = {}
t.baPayableSettle.id = '实付款单号'
t.baPayableSettle.settlementObjectType = '结算对象类型'
t.baPayableSettle.settlementObjectId = '供应商编码'
t.baPayableSettle.settlementObjectName = '供应商'
t.baPayableSettle.sourceType = '来源类型'
t.baPayableSettle.sourceId = '来源ID'
t.baPayableSettle.settlementObjectBankAccountId = '收款银行帐号'
t.baPayableSettle.currency = '币种'
t.baPayableSettle.payableAmount = '应付金额'
t.baPayableSettle.payabledAmount = '实付金额'
t.baPayableSettle.payDate = '付款时间'
t.baPayableSettle.companyBankAccountId = '付款公司银行帐号'
t.baPayableSettle.serialNumber = '支付流水号'
t.baPayableSettle.recordedPeriod = '会计期间'

/* 坏账异常 BaBadDebtAbnormal */
t.baBadDebtAbnormal = {}
t.baBadDebtAbnormal.id = '坏账单号'
t.baBadDebtAbnormal.receiveableSettlementId = '结算单号'
t.baBadDebtAbnormal.settlementObjectType = '结算对象类型'
t.baBadDebtAbnormal.settlementObjectId = '客户编码'
t.baBadDebtAbnormal.settlementObjectName = '客户'
t.baBadDebtAbnormal.badAmount = '坏账金额'
t.baBadDebtAbnormal.noWrittenOffSumD = '未核销金额'
t.baBadDebtAbnormal.writtenOffSumD = '已核销金额'
t.baBadDebtAbnormal.totalWrittenOffSumD = '应核销总额'
t.baBadDebtAbnormal.badWriteOffAmountD = '已核坏账金额'
t.baBadDebtAbnormal.currency = '币种'
t.baBadDebtAbnormal.remark = '备注'
t.baBadDebtAbnormal.auditor = '审核人'
t.baBadDebtAbnormal.auditDate = '审核时间'

/* 应收结算单其他信息 BaReceivableSettleBillOther */
t.baReceivableSettleBillOther = {}
t.baReceivableSettleBillOther.id = 'ID'
t.baReceivableSettleBillOther.receivableSettleBillsId = '应收结算单号'
t.baReceivableSettleBillOther.writeType = '核销类型'
t.baReceivableSettleBillOther.writeAmount = '核销金额'
t.baReceivableSettleBillOther.creditAmount = '入账金额'
t.baReceivableSettleBillOther.currency = '币种'
t.baReceivableSettleBillOther.writeOffOperator = '核销人'
t.baReceivableSettleBillOther.writeOffDate = '核销时间'
t.baReceivableSettleBillOther.remark = '核销备注'

/* 应收费用明细 BaReceivableFee */
t.baReceivableFee = {}
t.baReceivableFee.id = '费用明细单号'
t.baReceivableFee.detail = '查看'
t.baReceivableFee.receivableBizOrderId = '费用主单号'
t.baReceivableFee.createType = '产生方式'
t.baReceivableFee.settlementObjectType = '结算对象类型'
t.baReceivableFee.settlementObjectId = '客户编码'
t.baReceivableFee.settlementObjectName = '客户'
t.baReceivableFee.businessType = '业务类型'
t.baReceivableFee.orderType = '单据类型'
t.baReceivableFee.businessId = '业务单号'
t.baReceivableFee.waybillNo = '运单号'
t.baReceivableFee.deliveryNo = '派送单号'
t.baReceivableFee.customerVoucherNo = '客户单号'
t.baReceivableFee.receivableBillId = '对账单号'
t.baReceivableFee.feeTypeId = '费用项'
t.baReceivableFee.currency = '币种'
t.baReceivableFee.sum = '金额'
t.baReceivableFee.logisticsChannelCode = '物流渠道'
t.baReceivableFee.logisticsProductCode = '物流产品'
t.baReceivableFee.balanceWeightD = '结算重'
t.baReceivableFee.balanceWeightUnit = '结算重单位'
t.baReceivableFee.weightUnit = '实重单位'
t.baReceivableFee.weightD = '实重'
t.baReceivableFee.lengthUnit = '长度单位'
t.baReceivableFee.lengthD = '长*宽*高'
t.baReceivableFee.country = '国家'
t.baReceivableFee.province = '省/州'
t.baReceivableFee.city = '城市'
t.baReceivableFee.area = '区'
t.baReceivableFee.postCode = '邮编'
t.baReceivableFee.formula = '计算公式'
t.baReceivableFee.optDate = '业务发生时间'
t.baReceivableFee.billingDate = '计费时间'
t.baReceivableFee.billingStatus = '计费状态'
t.baReceivableFee.pushStatus = '推送状态'
t.baReceivableFee.errorMessage = '错误信息'
t.baReceivableFee.auditor = '审核人'
t.baReceivableFee.auditDate = '审核时间'
t.baReceivableFee.memo = '备注'
t.baReceivableFee.no_approved = '不同意'
t.baReceivableFee.approved = '确认'

/* 应付对账单 BaPayableBill */
t.baPayableBill = {}
t.baPayableBill.id = '应付账单号'
t.baPayableBill.type = '对账类型'
t.baPayableBill.noType = '对账单号类型'
t.baPayableBill.settlementObjectId = '供应商编码'
t.baPayableBill.settlementObjectName = '供应商'
t.baPayableBill.billDeadline = '账单截至日期'
t.baPayableBill.monthStatementDate = '账单归属月份'
t.baPayableBill.resultStatus = '账单状态'
t.baPayableBill.totalSum = '应付金额'
t.baPayableBill.currency = '应付币种'
t.baPayableBill.remark = '备注'
t.baPayableBill.sumError = '金额误差范围'
t.baPayableBill.sumErrorAndNegative = '金额误差范围 (负)'
t.baPayableBill.positive = '(正)'
t.baPayableBill.positiveError = '正误差'
t.baPayableBill.negativeError = '负误差'
t.baPayableBill.closed = '结账人'
t.baPayableBill.closeDate = '结账时间'
t.baPayableBill.settleStatus = '对账状态'
t.baPayableBill.notSettle = '暂停对账'
t.baPayableBill.isPaid = '已付款'

/* 应收实收费用单 BaReceivableBizOrder */
t.baReceivableBizOrder = {}
t.baReceivableBizOrder.id = '应收费用单号'
t.baReceivableBizOrder.receivableSettleBillId = '对账单号'
t.baReceivableBizOrder.receivable = '应收/实收'
t.baReceivableBizOrder.receivable = '应收/实收'
t.baReceivableBizOrder.receivable = '应收/实收'
t.baReceivableBizOrder.settlementObjectType = '结算对象类型'
t.baReceivableBizOrder.settlementObjectId = '客户编码'
t.baReceivableBizOrder.settlementObjectName = '客户'
t.baReceivableBizOrder.businessType = '业务类型'
t.baReceivableBizOrder.orderType = '单据类型'
t.baReceivableBizOrder.businessId = '业务单号'
t.baReceivableBizOrder.orderId = '客户订单'
t.baReceivableBizOrder.waybillNo = '运单号'
t.baReceivableBizOrder.deliveryNo = '派送单号'
t.baReceivableBizOrder.customerVoucherNo = '客户单号'
t.baReceivableBizOrder.currency = '币种'
t.baReceivableBizOrder.sum = '金额'
t.baReceivableBizOrder.paySum = '应付金额'
t.baReceivableBizOrder.profit = '利润'
t.baReceivableBizOrder.referencePrice = '参考公斤价'
t.baReceivableBizOrder.logisticsProductCode = '物流产品'
t.baReceivableBizOrder.balanceWeightD = '结算重'
t.baReceivableBizOrder.balanceWeightUnit = '结算重单位'
t.baReceivableBizOrder.weightUnit = '实重单位'
t.baReceivableBizOrder.weightD = '实重'
t.baReceivableBizOrder.volD = '体积(CM³)'
t.baReceivableBizOrder.totalQty = '件数'
t.baReceivableBizOrder.volumeWeightD = '材积重'
t.baReceivableBizOrder.volDiv = '材积除'
t.baReceivableBizOrder.lengthUnit = '长度单位'
t.baReceivableBizOrder.lengthD = '长*宽*高'
t.baReceivableBizOrder.country = '国家'
t.baReceivableBizOrder.province = '省/州'
t.baReceivableBizOrder.city = '城市'
t.baReceivableBizOrder.area = '区'
t.baReceivableBizOrder.postCode = '邮编'
t.baReceivableBizOrder.optDate = '业务发生时间'
t.baReceivableBizOrder.billingDate = '计费时间'
t.baReceivableBizOrder.serialNumber = '支付流水号'
t.baReceivableBizOrder.bankAccountId = '银行帐号'
t.baReceivableBizOrder.accountingPeriod = '会计期间'
t.baReceivableBizOrder.remark = '备注'
t.baReceivableBizOrder.auditor = '审核人'
t.baReceivableBizOrder.auditDate = '审核时间'
t.baReceivableBizOrder.billingStatus = '计费状态'
t.baReceivableBizOrder.settleStatus = '对账状态'
t.baReceivableBizOrder.isPaid = '已收款'

/* 其他应付费用单 BaPayableArap */
t.baPayableArap = {}
t.baPayableArap.id = '单据编号'
t.baPayableArap.feeTypeId = '费用项'
t.baPayableArap.settlementObjectId = '供应商编码'
t.baPayableArap.settlementObjectName = '供应商'
t.baPayableArap.currency = '币种'
t.baPayableArap.sum = '金额'
t.baPayableArap.accountingPeriod = '会计期间'
t.baPayableArap.remark = '备注'

/* 应收对账单 BaReceivableBill */
t.baReceivableBill = {}
t.baReceivableBill.id = '对账单号'
t.baReceivableBill.settlementObjectType = '结算对象类型'
t.baReceivableBill.settlementObjectId = '客户编码'
t.baReceivableBill.settlementObjectName = '客户'
t.baReceivableBill.totalSum = '总金额'
t.baReceivableBill.currency = '币种'
t.baReceivableBill.billsType = '计费单类型'
t.baReceivableBill.weightType = '指定重量类型'
t.baReceivableBill.billDeadline = '帐期结束日期'
t.baReceivableBill.monthStatementDate = '账单归属月份'
t.baReceivableBill.recStatement = '导出账龄'
t.baReceivableBill.accountingPeriod = '会计期间'
t.baReceivableBill.remark = '备注'
t.baReceivableBill.pushed = '是否已推送'
t.baReceivableBill.auditor = '对账人'
t.baReceivableBill.auditDate = '对账时间'
t.baReceivableBill.closed = '审核人'
t.baReceivableBill.closeDate = '审核时间'
t.baReceivableBill.bankNameDetails = '账户详情'
t.baReceivableBill.accountId = '收款账户'
t.baReceivableBill.bankName = '收款银行'
t.baReceivableBill.accountNumber = '收款账号'
t.baReceivableBill.customsMethod = '报关方式'
t.baReceivableBill.guidPrefix = 'GUID前缀'
t.baReceivableBill.nameType = '文件名包含'

/* 付款对象账户余额 BaPayableAccount */
t.baPayableAccount = {}
t.baPayableAccount.id = 'ID'
t.baPayableAccount.settlementObjectId = '供应商编码'
t.baPayableAccount.settlementObjectName = '供应商'
t.baPayableAccount.currency = '币种'
t.baPayableAccount.balanceSumD = '余额'
t.baPayableAccount.noWriteOffSumD = '未核销金额'
t.baPayableAccount.usableSumD = '可用金额'

/* 其他应收费用单 BaReceivableArap */
t.baReceivableArap = {}
t.baReceivableArap.id = '收款单号'
t.baReceivableArap.feeTypeId = '费用项'
t.baReceivableArap.settlementObjectType = '结算对象类型'
t.baReceivableArap.settlementObjectId = '客户'
t.baReceivableArap.settlementObjectName = '客户'
t.baReceivableArap.currency = '币种'
t.baReceivableArap.sum = '金额'
t.baReceivableArap.accountingPeriod = '会计期间'
t.baReceivableArap.remark = '备注'

/* 出库包裹明细信息 BaOutOrderCargo */
t.baOutOrderCargo = {}
t.baOutOrderCargo.id = 'ID'
t.baOutOrderCargo.outOrderId = '出库订单ID'
t.baOutOrderCargo.stockStatus = '实物类别'
t.baOutOrderCargo.weightUnit = '重量单位'
t.baOutOrderCargo.unitWeight = '单位重量'
t.baOutOrderCargo.lengthUnit = '长度单位'
t.baOutOrderCargo.lenght = '长'
t.baOutOrderCargo.width = '宽'
t.baOutOrderCargo.height = '高'
t.baOutOrderCargo.qty = '数量'

/* 收款对象账户余额 BaReceivableAccount */
t.baReceivableAccount = {}
t.baReceivableAccount.id = 'ID'
t.baReceivableAccount.settlementObjectType = '结算对象类型'
t.baReceivableAccount.settlementObjectId = '客户编码'
t.baReceivableAccount.settlementObjectName = '客户'
t.baReceivableAccount.currency = '币种'
t.baReceivableAccount.balanceSumD = '余额'
t.baReceivableAccount.noWriteOffSumD = '未核销金额'
t.baReceivableAccount.usableSumD = '可用金额'
t.baReceivableAccount.occupySumD = '占用金额'
t.baReceivableAccount.creditSumD = '授信额度'

/* 出库订单 BaOutOrder */
t.baOutOrder = {}
t.baOutOrder.id = 'ID'
t.baOutOrder.customerVoucherNo = '客户单号'
t.baOutOrder.logisticsChannelCode = '物流渠道代号'
t.baOutOrder.logisticsProductCode = '物流产品代号'
t.baOutOrder.waybillNo = '运单号'
t.baOutOrder.totalQty = '总数量'
t.baOutOrder.vol = '总体积'
t.baOutOrder.weightUnit = '重量单位'
t.baOutOrder.totalWeight = '总重量'
t.baOutOrder.lengthUnit = '长度单位'
t.baOutOrder.lenght = '长'
t.baOutOrder.width = '宽'
t.baOutOrder.height = '高'
t.baOutOrder.consigneeCountry = '国家'
t.baOutOrder.consigneeProvince = '省/州'
t.baOutOrder.consigneeCity = '市'
t.baOutOrder.consigneeDistrict = '区'
t.baOutOrder.consigneePostcode = '邮编'
t.baOutOrder.consigneeAddress = '详细地址'
t.baOutOrder.channelProvider = '渠道供应商'
t.baOutOrder.customerId = '客户'
t.baOutOrder.customerName = '客户名称'
t.baOutOrder.optDate = '业务时间'

/* 预收款单 BaPreReceivableSettle */
t.baPreReceivableSettle = {}
t.baPreReceivableSettle.id = '预收款单号'
t.baPreReceivableSettle.normal = '新增'
t.baPreReceivableSettle.recharge = '充值单'
t.baPreReceivableSettle.feeTypeId = '费用项'
t.baPreReceivableSettle.settlementObjectType = '结算对象类型'
t.baPreReceivableSettle.settlementObjectId = '客户'
t.baPreReceivableSettle.settlementObjectName = '客户'
t.baPreReceivableSettle.currency = '收款币种'
t.baPreReceivableSettle.receivedCurrency = '到账币种'
t.baPreReceivableSettle.sum = '收款金额'
t.baPreReceivableSettle.currencyRate = '兑换汇率'
t.baPreReceivableSettle.receivedSum = '到账金额'
t.baPreReceivableSettle.receivedStatus = '到账状态'
t.baPreReceivableSettle.serialNumber = '支付流水号'
t.baPreReceivableSettle.receivablesDate = '收款时间'
t.baPreReceivableSettle.bankAccountId = '收款账户'
t.baPreReceivableSettle.bankName = '开户行'
t.baPreReceivableSettle.accountNumber = '卡号'
t.baPreReceivableSettle.recordedPeriod = '会计期间'
t.baPreReceivableSettle.bookkeeper = '入账人'
t.baPreReceivableSettle.auditorUser = '审核人'
t.baPreReceivableSettle.auditorDate = '审核时间'
t.baPreReceivableSettle.receivedDate = '到账时间'
t.baPreReceivableSettle.remark = '备注'
t.baPreReceivableSettle.dataSource = '数据来源'
t.baPreReceivableSettle.confirm = '确认到账'

/* 入库单明细 BaInOrderCargo */
t.baInOrderCargo = {}
t.baInOrderCargo.id = 'ID'
t.baInOrderCargo.inOrderId = '入库通知单号'
t.baInOrderCargo.productCode = '货号'
t.baInOrderCargo.productBarcode = '货品条码'
t.baInOrderCargo.productName = '货品名称'
t.baInOrderCargo.stockStatus = '实物类别'
t.baInOrderCargo.qty = '上架数量'
t.baInOrderCargo.inLocationCode = '上架库位'

/* 入库单 BaInOrder */
t.baInOrder = {}
t.baInOrder.id = '入库通知单号'
t.baInOrder.customerVoucherNo = '客户单号'
t.baInOrder.customerId = '客户'
t.baInOrder.customerName = '客户名称'
t.baInOrder.logisticsChannelCode = '物流渠道代号'
t.baInOrder.logisticsProductCode = '物流产品代号'
t.baInOrder.channelProvider = '渠道供应商'

/* 预付款单 BaPrePayableSettle */
t.baPrePayableSettle = {}
t.baPrePayableSettle.id = '预付款单号'
t.baPrePayableSettle.feeTypeId = '费用项'
t.baPrePayableSettle.settlementObjectType = '结算对象类型'
t.baPrePayableSettle.settlementObjectId = '供应商编码'
t.baPrePayableSettle.settlementObjectName = '供应商'
t.baPrePayableSettle.settlementObjectBankAccountId = '供应商银行帐号'
t.baPrePayableSettle.companyBankAccountId = '收款帐号'
t.baPrePayableSettle.currency = '币种'
t.baPrePayableSettle.sum = '金额'
t.baPrePayableSettle.payDate = '付款时间'
t.baPrePayableSettle.recordedPeriod = '会计期间'
t.baPrePayableSettle.serialNumber = '支付流水号'
t.baPrePayableSettle.memo = '备注'
t.baPrePayableSettle.auditor = '审核人'
t.baPrePayableSettle.auditDate = '审核时间'

/* 应付结算单其他信息 BaPayableSettleBillOther */
t.baPayableSettleBillOther = {}
t.baPayableSettleBillOther.id = 'ID'
t.baPayableSettleBillOther.payableSettleBillsId = '应付结算单号'
t.baPayableSettleBillOther.writeOffOperator = '核销人'
t.baPayableSettleBillOther.amount = '核销金额'
t.baPayableSettleBillOther.creditAmount = '入账金额'
t.baPayableSettleBillOther.currency = '币种'
t.baPayableSettleBillOther.remark = '备注'
t.baPayableSettleBillOther.writeOffDate = '核销时间'

/* 应收结算单核销金额信息 BaReceivableSettleBillAmount */
t.baReceivableSettleBillAmount = {}
t.baReceivableSettleBillAmount.id = '账户编号'
t.baReceivableSettleBillAmount.receivableSettleBillId = '应收结算单号'
t.baReceivableSettleBillAmount.currency = '应收币种'
t.baReceivableSettleBillAmount.receivableAmount = '应收金额'
t.baReceivableSettleBillAmount.accountBalance = '账户余额'
t.baReceivableSettleBillAmount.totalAmount = '应核销总金额'
t.baReceivableSettleBillAmount.writeOffAmount = '已核销金额'
t.baReceivableSettleBillAmount.noWriteOffAmount = '未核销金额'
t.baReceivableSettleBillAmount.badWriteOffAmount = '坏账核销金额'
t.baReceivableSettleBillAmount.receivableAccount = '收款账户'
t.baReceivableSettleBillAmount.receivableBankCode = '开户行'
t.baReceivableSettleBillAmount.receivableCardNumber = '卡号'
t.baReceivableSettleBillAmount.creditCurrency = '入账币种'
t.baReceivableSettleBillAmount.creditAmount = '入账金额'
t.baReceivableSettleBillAmount.serialNumber = '流水号'
t.baReceivableSettleBillAmount.payDate = '收款时间'

/* 应付费用明细 BaPayableFee */
t.baPayableFee = {}
t.baPayableFee.id = 'ID'
t.baPayableFee.createType = '计费方式'
t.baPayableFee.settlementObjectId = '供应商编码'
t.baPayableFee.settlementObjectName = '供应商'
t.baPayableFee.businessType = '业务类型'
t.baPayableFee.orderType = '单据类型'
t.baPayableFee.orderId = '业务单号'
t.baPayableFee.waybillNo = '运单号'
t.baPayableFee.customerVoucherNo = '客户单号'
t.baPayableFee.payableBizOrderId = '费用单号'
t.baPayableFee.payableBillId = '对账单号'
t.baPayableFee.payableBilDetailslId = '对账单明细单号'
t.baPayableFee.feeTypeId = '费用项'
t.baPayableFee.sum = '金额'
t.baPayableFee.currency = '币种'
t.baPayableFee.formula = '计算公式'
t.baPayableFee.optDate = '业务发生时间'
t.baPayableFee.billingDate = '计费时间'
t.baPayableFee.billingStatus = '计费状态'
t.baPayableFee.remark = '备注'

/* 应付结算单核销金额信息 BaPayableSettleBillAmount */
t.baPayableSettleBillAmount = {}
t.baPayableSettleBillAmount.id = 'ID'
t.baPayableSettleBillAmount.payableSettleBillsId = '应付结算单号'
t.baPayableSettleBillAmount.currency = '币种'
t.baPayableSettleBillAmount.totalAmount = '应核销总金额'
t.baPayableSettleBillAmount.writeOffAmount = '已核销金额'
t.baPayableSettleBillAmount.noWriteOffAmount = '未核销金额'
t.baPayableSettleBillAmount.payableAmount = '应付金额'
t.baPayableSettleBillAmount.accountBalance = '账户余额'
t.baPayableSettleBillAmount.totalAmount = '应核销总金额'
t.baPayableSettleBillAmount.writeOffAmount = '已核销金额'
t.baPayableSettleBillAmount.noWriteOffAmount = '未核销金额'
t.baPayableSettleBillAmount.receivableAccount = '收款账户'
t.baPayableSettleBillAmount.creditCurrency = '入账币种'
t.baPayableSettleBillAmount.creditAmount = '入账金额'
t.baPayableSettleBillAmount.serialNumber = '流水号'
t.baPayableSettleBillAmount.payDate = '收款时间'

t.baPayableSettleBillAmount.payableAccount = '付款账户'
t.baPayableSettleBillAmount.bankCode = '开户行'
t.baPayableSettleBillAmount.cardNumber = '卡号'

/* 充值单 BaRecharge */
t.baRecharge = {}
t.baRecharge.id = '充值单号'
t.baRecharge.settlementObjectType = '结算对象类型'
t.baRecharge.settlementObjectId = '客户编码'
t.baRecharge.settlementObjectName = '客户'
t.baRecharge.payerContacts = '付款联系人'
t.baRecharge.payerPhone = '付款人电话'
t.baRecharge.currency = '币种'
t.baRecharge.sum = '金额'
t.baRecharge.serialNumber = '支付流水号'
t.baRecharge.feeTypeId = '费用类型'
t.baRecharge.bankAccountId = '收款账户'
t.baRecharge.bankName = '开户行'
t.baRecharge.accountNumber = '卡号'
t.baRecharge.attachmentUrl = '附件'
t.baRecharge.giveUp = '作废'
t.baRecharge.invalidRemark = '作废原因'
t.baRecharge.customerRemark = '客户备注'
t.baRecharge.receivableRemark = '收款备注'
t.baRecharge.bookkeepingTime = '入账时间'
t.baRecharge.bookkeeper = '入账人'
t.baRecharge.rechargeDate = '充值时间'

/* 客户预扣金额 BaCustomerAccountLockAmount */
t.baCustomerAccountLockAmount = {}
t.baCustomerAccountLockAmount.id = 'ID'
t.baCustomerAccountLockAmount.customerId = '客户编码'
t.baCustomerAccountLockAmount.coOutOrderId = '订单编号'
t.baCustomerAccountLockAmount.waybillNo = '运单号'
t.baCustomerAccountLockAmount.preType = '预扣类型'
t.baCustomerAccountLockAmount.currency = '币种'
t.baCustomerAccountLockAmount.sum = '金额'

/* 索赔订单表 BaClaimOrder */
t.baClaimOrder = {}
t.baClaimOrder.id = '索赔单号'
t.baClaimOrder.customerType = '客户类型'
t.baClaimOrder.customerId = '客户名称'
t.baClaimOrder.orderId = '订单号'
t.baClaimOrder.waybillNo = '运单号'
t.baClaimOrder.currency = '币种'
t.baClaimOrder.sum = '金额'
t.baClaimOrder.claimReason = '索赔原因'
t.baClaimOrder.auditor = '审核人'
t.baClaimOrder.auditDate = '审核时间'
t.baClaimOrder.remark = '备注'

/* 运单其他信息 WsComWaybillOther */
t.wsComWaybillOther = {}
t.wsComWaybillOther.id = '运单编号'
t.wsComWaybillOther.customerRealWeight = '客户实际重量(KG)'

/* 出库批次 WsComOutWarehouseBatch */
t.wsComOutWarehouseBatch = {}
t.wsComOutWarehouseBatch.batchInfo = '批次信息'
t.wsComOutWarehouseBatch.id = 'ID'
t.wsComOutWarehouseBatch.batchNo = '批次号'
t.wsComOutWarehouseBatch.warehouseId = '仓库'
t.wsComOutWarehouseBatch.agentType = '代理类型'
t.wsComOutWarehouseBatch.providerId = '供应商'
t.wsComOutWarehouseBatch.carPlateNo = '车牌号'
t.wsComOutWarehouseBatch.destPlace = '目的地'
t.wsComOutWarehouseBatch.remarks = '仓库备注'
t.wsComOutWarehouseBatch.finishStatus = '完结状态'
t.wsComOutWarehouseBatch.finishUser = '完结人'
t.wsComOutWarehouseBatch.finishTime = '完结时间'
t.wsComOutWarehouseBatch.status = '状态'
t.wsComOutWarehouseBatch.createDate = '创建时间'
t.wsComOutWarehouseBatch.batchDate = '发运时间'
t.wsComOutWarehouseBatch.bagCount = '容器总数'
t.wsComOutWarehouseBatch.waybillCount = '运单总数'
t.wsComOutWarehouseBatch.netWeight = '容器总净重'
t.wsComOutWarehouseBatch.cancelFinish = '取消完结'
t.wsComOutWarehouseBatch.pushBatchToCustomerStatus = '推送状态'
t.wsComOutWarehouseBatch.pushBatchToCustomerErrorMessage = '推送错误信息'
t.wsComOutWarehouseBatch.pushToCustomer = '出仓批次推送客户'

/* 运单操作日志 WsComWaybillOperateLog */
t.wsComWaybillOperateLog = {}
t.wsComWaybillOperateLog.id = 'ID'
t.wsComWaybillOperateLog.waybillId = '运单编号'
t.wsComWaybillOperateLog.operateNode = '操作节点'
t.wsComWaybillOperateLog.opeareteDescription = '操作描述'

/* 出库容器运单关系 WsComOutWarehouseBagWaybill */
t.wsComOutWarehouseBagWaybill = {}
t.wsComOutWarehouseBagWaybill.id = 'ID'
t.wsComOutWarehouseBagWaybill.waybillId = '运单编号'
t.wsComOutWarehouseBagWaybill.waybillNo = '运单号'
t.wsComOutWarehouseBagWaybill.deliveryNo = '派送单号'
t.wsComOutWarehouseBagWaybill.bagId = '容器号'
t.wsComOutWarehouseBagWaybill.bagNo = '容器号'
t.wsComOutWarehouseBagWaybill.createDate = '绑定时间'

/* 运单 WsComWaybill */
t.wsComWaybill = {}
t.wsComWaybill.id = 'ID'
t.wsComWaybill.orderId = '订单编号'
t.wsComWaybill.subDeliveryNo = '子单派送单号'
t.wsComWaybill.subPackageNo = '箱号/FBA唛头'
t.wsComWaybill.customerOrderNo = '客户单号'
t.wsComWaybill.customerOrderNo = '客户单号'
t.wsComWaybill.waybillNo = '运单号'
t.wsComWaybill.deliveryNo = '派送单号'
t.wsComWaybill.logisticsProductCode = '物流产品'
t.wsComWaybill.logisticsChannelCode = '物流渠道'
t.wsComWaybill.providerCode = '供应商代码'
t.wsComWaybill.forecastWeight = '预报重量'
t.wsComWaybill.weight = '内部入库实重(KG)'
t.wsComWaybill.volumeWeight = '入库体积重'
t.wsComWaybill.volume = '入库方数'
t.wsComWaybill.inPackageQty = '入库包裹数'
t.wsComWaybill.outPackageQty = '出库包裹数'
t.wsComWaybill.length = '长(CM)'
t.wsComWaybill.width = '宽(CM)'
t.wsComWaybill.height = '高(CM)'
t.wsComWaybill.inAdjustWeight = '入库调节重(KG)'
t.wsComWaybill.outAdjustWeight = '出库调节重(KG)'
t.wsComWaybill.weightUnit = '重量单位'
t.wsComWaybill.storageLocation = '异常上架库位'
t.wsComWaybill.goodsCategory = '货物类别'
t.wsComWaybill.parcelType = '包裹类型'
t.wsComWaybill.electric = '是否带电'
t.wsComWaybill.remote = '是否偏远'
t.wsComWaybill.consigneeCountry = '收货国家'
t.wsComWaybill.customerRemark = '客户备注'
t.wsComWaybill.inRemark = '入库备注'
t.wsComWaybill.serviceId = '专属客服'
t.wsComWaybill.salesmanId = '业务员'
t.wsComWaybill.objectId = '客户对象'
t.wsComWaybill.objectName = '客户名称'
t.wsComWaybill.objectCode = '客户代号'
t.wsComWaybill.objectType = '对象类别'
t.wsComWaybill.warehouseId = '仓库'
t.wsComWaybill.plateformType = '来源平台'
t.wsComWaybill.packageQty = '包裹数'
t.wsComWaybill.exceptionFlag = '是否异常'
t.wsComWaybill.arrearage = '是否欠费'
t.wsComWaybill.masterDeliveryNo = '主派送单号'
t.wsComWaybill.copyDeliveryNo = '请从excel表格中复制出所有派送单号粘贴到此处'
t.wsComWaybill.copyDeliveryNoAndBox = '请从excel表格中复制出所有箱号（FBA唛头号）和派送单号粘贴到此处,左列为箱号（FBA唛头号）,右列为派送单号'

/* 出库容器 WsComOutWarehouseBag */
t.wsComOutWarehouseBag = {}
t.wsComOutWarehouseBag.id = 'ID'
t.wsComOutWarehouseBag.bagNo = '容器号'
t.wsComOutWarehouseBag.warehouseId = '仓库'
t.wsComOutWarehouseBag.deliveryChannelCode = '尾程渠道'
t.wsComOutWarehouseBag.logisticsProductCode = '物流产品'
t.wsComOutWarehouseBag.deliverySupplierCode = '供应商'
t.wsComOutWarehouseBag.deliveryChannelZone = '渠道分区'
t.wsComOutWarehouseBag.destinationCountry = '目的地'
t.wsComOutWarehouseBag.bagStatus = '容器状态'
t.wsComOutWarehouseBag.finishUser = '完结人'
t.wsComOutWarehouseBag.finishTime = '完结时间'
t.wsComOutWarehouseBag.grossWeight = '毛重(KG)'
t.wsComOutWarehouseBag.vol = '体积(m³)'
t.wsComOutWarehouseBag.netWeight = '净重(KG)'
t.wsComOutWarehouseBag.netVolume = '方数'
t.wsComOutWarehouseBag.limitWeight = '限重(KG)'
t.wsComOutWarehouseBag.limitWeightD = '限重'
t.wsComOutWarehouseBag.volumeWeight = '容器体积重(KG)'
t.wsComOutWarehouseBag.length = '容器长'
t.wsComOutWarehouseBag.width = '容器宽'
t.wsComOutWarehouseBag.height = '容器高'
t.wsComOutWarehouseBag.outStatus = '发运状态'
t.wsComOutWarehouseBag.hawbNo = '分单号'
t.wsComOutWarehouseBag.providerBatchNo = '服务商批次号'
t.wsComOutWarehouseBag.providerBagNo = '服务商袋号'
t.wsComOutWarehouseBag.providerFileUrl = '服务商文件URL'
t.wsComOutWarehouseBag.providerOtherInfo = '服务商其他信息'
t.wsComOutWarehouseBag.pushStatus = '组包推送状态'
t.wsComOutWarehouseBag.parcelPushStatus = '内件API推送状态'
t.wsComOutWarehouseBag.pushBigBagToCustomerStatus = '推送状态'
t.wsComOutWarehouseBag.pushBigBagToCustomerErrorMessage = '推送错误信息'
t.wsComOutWarehouseBag.isPrintApiBag = '打印袋牌类型'
t.wsComOutWarehouseBag.waybillCount = '件数'
t.wsComOutWarehouseBag.status = '状态'
t.wsComOutWarehouseBag.createDate = '创建时间'
t.wsComOutWarehouseBag.creator = '创建人'
t.wsComOutWarehouseBag.added = '已添加容器'
t.wsComOutWarehouseBag.notAdded = '未添加容器'
t.wsComOutWarehouseBag.remark = '备注'
t.wsComOutWarehouseBag.pushToCustomer = '容器推送客户'
/* 仓库操作痕迹表 WsComWarehouseOperationTrace */
t.wsComWarehouseOperationTrace = {}
t.wsComWarehouseOperationTrace.id = 'ID'
t.wsComWarehouseOperationTrace.operationNo = '操作单号'
t.wsComWarehouseOperationTrace.nodeCode = '操作代码'
t.wsComWarehouseOperationTrace.nodeName = '操作名称'
t.wsComWarehouseOperationTrace.waybillId = '运单编号'

/* 国内调拨批次容器关系 WsComTransferBatchBag */
t.wsComTransferBatchBag = {}
t.wsComTransferBatchBag.id = 'ID'
t.wsComTransferBatchBag.bagId = '容器号'
t.wsComTransferBatchBag.bagNo = '容器号'
t.wsComTransferBatchBag.batchId = '批次编号'
t.wsComTransferBatchBag.batchNo = '批次号'
t.wsComTransferBatchBag.waybillCount = '运单总数'
t.wsComTransferBatchBag.bagNetWeight = '容器净重(KG)'
t.wsComTransferBatchBag.bagGrossWeight = '容器毛重(KG)'

/* 国内中转调拨批次 WsComTransferBatch */
t.wsComTransferBatch = {}
t.wsComTransferBatch.id = 'ID'
t.wsComTransferBatch.batchNo = '批次号'
t.wsComTransferBatch.warehouseId = '仓库'
t.wsComTransferBatch.destWarehouseId = '目的仓库'
t.wsComTransferBatch.providerId = '供应商'
t.wsComTransferBatch.transferChannelCode = '中转渠道'
t.wsComTransferBatch.carPlateNo = '车牌号'
t.wsComTransferBatch.remarks = '仓库备注'
t.wsComTransferBatch.finishStatus = '完结状态'
t.wsComTransferBatch.finishUser = '完结人'
t.wsComTransferBatch.finishTime = '完结时间'
t.wsComTransferBatch.createDate = '创建时间'
t.wsComTransferBatch.creator = '创建人'
t.wsComTransferBatch.status = '状态'
t.wsComTransferBatch.bagCount = '容器总数'
t.wsComTransferBatch.waybillCount = '运单总数'
t.wsComTransferBatch.bagTotalNetWeight = '容器总净重'
t.wsComTransferBatch.bagTotalGrossWeight = '容器总毛重'

/* 分拣规则 WsComSortingRule */
t.wsComSortingRule = {}
t.wsComSortingRule.id = 'ID'
t.wsComSortingRule.warehouseId = '匹配仓库编号'
t.wsComSortingRule.sortingRules = '分拣规则'
t.wsComSortingRule.rulesValue = '规则值'
t.wsComSortingRule.sortingValue = '分拣文案'

/* 干线主单容器关系 WsComMawbBag */
t.wsComMawbBag = {}
t.wsComMawbBag.id = 'ID'
t.wsComMawbBag.bagId = '容器号'
t.wsComMawbBag.bagNo = '容器号'
t.wsComMawbBag.mawbId = '主单ID'
t.wsComMawbBag.mawbNo = '主单号'
t.wsComMawbBag.batchNo = '批次号'
t.wsComMawbBag.hawbNo = '分单号'
t.wsComMawbBag.waybillCount = '运单总数'
t.wsComMawbBag.bagNetWeight = '容器净重(KG)'
t.wsComMawbBag.logisticsChannelNo = '渠道'
t.wsComMawbBag.createDate = '创建时间'
t.wsComMawbBag.costItem = '请检查费用名称及费用'

/* 干线主单容器关系 WsComMawbBag */
t.wsComMawbPackage = {}
t.wsComMawbPackage.id = 'ID'
t.wsComMawbPackage.bagNo = '容器号'
t.wsComMawbPackage.mawbNo = '主单号'
t.wsComMawbPackage.hawbNo = '分单号'
t.wsComMawbPackage.deliveryNo = '派送单号'
t.wsComMawbPackage.weightD = '重量(KG)'
t.wsComMawbPackage.volD = '体积(M³)'
t.wsComMawbPackage.waybillCount = '运单总数'
t.wsComMawbPackage.bagNetWeight = '容器净重(KG)'
t.wsComMawbPackage.logisticsProductCode = '物流产品'
t.wsComMawbPackage.logisticsChannelCode = '物流渠道'
t.wsComMawbPackage.type = '配载类型'
t.wsComMawbPackage.departureTime = '开航时间'
t.wsComMawbPackage.createDate = '创建时间'

/* 退货出库运单批次关系 WsComReturnOutBatchWaybill */
t.wsComReturnOutBatchWaybill = {}
t.wsComReturnOutBatchWaybill.id = 'ID'
t.wsComReturnOutBatchWaybill.waybillId = '运单编号'
t.wsComReturnOutBatchWaybill.waybillNo = '运单号'
t.wsComReturnOutBatchWaybill.scanNumber = '退货扫描单号'
t.wsComReturnOutBatchWaybill.batchId = '批次编号'
t.wsComReturnOutBatchWaybill.batchNo = '批次号'
t.wsComReturnOutBatchWaybill.deliveryChannelCode = '渠道代码'
t.wsComReturnOutBatchWaybill.logisticsProductCode = '运输方式'
t.wsComReturnOutBatchWaybill.objectCode = '客户'
t.wsComReturnOutBatchWaybill.objectType = '客户类别'

/* 干线主单 WsComMawb */
t.wsComMawb = {}
t.wsComMawb.id = 'ID'
t.wsComMawb.mawbNo = '提单号'
t.wsComMawb.customerNo = '公司提单号'
t.wsComMawb.relationNo = '航班/次号'
t.wsComMawb.relationId = '航班/次号id'
t.wsComMawb.transportWay = '运输方式'
t.wsComMawb.departurePortCode = '起运港/机场'
t.wsComMawb.arrivalPortCode = '目的港/机场'
t.wsComMawb.departureTime = '开航时间'
t.wsComMawb.arrivalTime = '预计到达时间'
t.wsComMawb.confirmTime = '封单/解封时间'
t.wsComMawb.confirmUser = '封单/解封人'
t.wsComMawb.batchCount = '批次总数'
t.wsComMawb.bagCount = '容器数'
t.wsComMawb.packageQty = '件数'
t.wsComMawb.weight = '重量'
t.wsComMawb.vol = '体积'
t.wsComMawb.waybillCount = '运单总数'
t.wsComMawb.status = '状态'
t.wsComMawb.createDate = '创建日期'
t.wsComMawb.bagTotalNetWeight = '容器总净重(KG)'
t.wsComMawb.bagTotalGrossWeight = '容器总毛重(KG)'
t.wsComMawb.totalWeight = '干线总重量(KG)'
t.wsComMawb.totalVolumeWeight = '干线总体积重(KG)'
t.wsComMawb.creator = '创建人'
t.wsComMawb.creatDate = '创建日期'
t.wsComMawb.updateMawbNo = '修改提单号'
t.wsComMawb.hasReturn = '有退货运单'
t.wsComMawb.mawbFileUrl = '提单文件'
t.wsComMawb.uploadMawbFile = '上传提单文件'

/* 退货出库批次 WsComReturnOutBatch */
t.wsComReturnOutBatch = {}
t.wsComReturnOutBatch.id = 'ID'
t.wsComReturnOutBatch.batchNo = '批次号'
t.wsComReturnOutBatch.warehouseId = '仓库'
t.wsComReturnOutBatch.remark = '仓库备注'
t.wsComReturnOutBatch.finishUser = '完结人'
t.wsComReturnOutBatch.finishTime = '完结时间'
t.wsComReturnOutBatch.status = '状态'
t.wsComReturnOutBatch.creator = '创建人'
t.wsComReturnOutBatch.createDate = '创建时间'
t.wsComReturnOutBatch.waybillTotalCount = '运单总数'

/* 入库作业单 WsComInWarehouseJob */
t.wsComInWarehouseJob = {}
t.wsComInWarehouseJob.id = 'ID'
t.wsComInWarehouseJob.waybillId = '运单编号'
t.wsComInWarehouseJob.waybillNo = '运单号'
t.wsComInWarehouseJob.warehouseId = '仓库'
t.wsComInWarehouseJob.operator = '操作人'
t.wsComInWarehouseJob.operationTime = '操作时间'

/* 退货入库运单批次关系 WsComReturnInBatchWaybill */
t.wsComReturnInBatchWaybill = {}
t.wsComReturnInBatchWaybill.id = 'ID'
t.wsComReturnInBatchWaybill.waybillId = '运单编号'
t.wsComReturnInBatchWaybill.waybillNo = '运单号'
t.wsComReturnInBatchWaybill.batchId = '批次编号'
t.wsComReturnInBatchWaybill.batchNo = '批次号'
t.wsComReturnInBatchWaybill.logisticsChannelCode = '渠道代码'
t.wsComReturnInBatchWaybill.deliveryNo = '派送单号'
t.wsComReturnInBatchWaybill.providerCode = '供应商代码'

/* 换单作业单 WsComExchangeJob */
t.wsComExchangeJob = {}
t.wsComExchangeJob.id = 'ID'
t.wsComExchangeJob.waybillId = '运单编号'
t.wsComExchangeJob.waybillNo = '运单号'
t.wsComExchangeJob.warehouseId = '仓库'
t.wsComExchangeJob.operator = '操作人'
t.wsComExchangeJob.operationTime = '操作时间'

/* 退货入库批次 WsComReturnInBatch */
t.wsComReturnInBatch = {}
t.wsComReturnInBatch.id = 'ID'
t.wsComReturnInBatch.batchNo = '批次号'
t.wsComReturnInBatch.warehouseId = '仓库'
t.wsComReturnInBatch.remarks = '仓库备注'
t.wsComReturnInBatch.status = '状态'
t.wsComReturnInBatch.finishUser = '完结人'
t.wsComReturnInBatch.finishTime = '完结时间'
t.wsComReturnInBatch.createDate = '创建时间'
t.wsComReturnInBatch.creator = '创建人'
t.wsComReturnInBatch.waybillTotalCount = '运单总票数'

/* 出库批次容器关系 WsComOutWarehouseBatchBag */
t.wsComOutWarehouseBatchBag = {}
t.wsComOutWarehouseBatchBag.id = 'ID'
t.wsComOutWarehouseBatchBag.bagId = '容器号'
t.wsComOutWarehouseBatchBag.bagNo = '容器号'
t.wsComOutWarehouseBatchBag.bagNoOrDeliveryNo = '容器号 / 派送单号'
t.wsComOutWarehouseBatchBag.batchId = '批次编号'
t.wsComOutWarehouseBatchBag.batchNo = '批次号'
t.wsComOutWarehouseBatchBag.deliveryNo = '派送单号'
t.wsComOutWarehouseBatchBag.waybillTotalCount = '运单总数'
t.wsComOutWarehouseBatchBag.bagTotalNetWeight = '容器总净重(KG)'
t.wsComOutWarehouseBatchBag.totalNetWeight = '总净重(KG)'
t.wsComOutWarehouseBatchBag.bagTotalGrossWeight = '容器总毛重(KG)'
t.wsComOutWarehouseBatchBag.totalGrossWeight = '总毛重(KG)'
t.wsComOutWarehouseBatchBag.exceptionTips = '当前批次中有{exceptionCount}笔运单存在异常！（点击查看）'
t.wsComOutWarehouseBatchBag.addedBill = '已添加包裹'
t.wsComOutWarehouseBatchBag.notAddedBill = '未添加包裹'

/* 异常单明细 WsComExceptionDetail */
t.wsComExceptionDetail = {}
t.wsComExceptionDetail.id = 'ID'
t.wsComExceptionDetail.exceptionBillId = '异常单编号'
t.wsComExceptionDetail.exceptionCode = '异常类型'
t.wsComExceptionDetail.exceptionName = '异常名称'
t.wsComExceptionDetail.creator = '标记人'
t.wsComExceptionDetail.createDate = '标记时间'

/* 异常单 WsComExceptionBill */
t.wsComExceptionBill = {}
t.wsComExceptionBill.id = 'ID'
t.wsComExceptionBill.waybillId = '运单编号'
t.wsComExceptionBill.waybillNo = '运单号'
t.wsComExceptionBill.warehouseId = '仓库'
t.wsComExceptionBill.exceptionDescription = '异常描述'
t.wsComExceptionBill.assignedCustomer = '已分配客服'
t.wsComExceptionBill.processStatus = '处理状态'
t.wsComExceptionBill.processUser = '异常处理人'
t.wsComExceptionBill.processTime = '异常处理时间'
t.wsComExceptionBill.processSchemeCode = '方案代码'
t.wsComExceptionBill.processSchemeName = '处理方案名称'
t.wsComExceptionBill.processRemark = '处理备注'
t.wsComExceptionBill.operationStatus = '操作状态'
t.wsComExceptionBill.operator = '仓库操作人'
t.wsComExceptionBill.operationTime = '仓库操作时间'
t.wsComExceptionBill.delFalg = '删除标记'
t.wsComExceptionBill.deleteUser = '删除人'
t.wsComExceptionBill.deleteTime = '删除时间'
t.wsComExceptionBill.createDate = '创建时间'
t.wsComExceptionBill.creator = '创建人'

/* 容器操作日志表 WsComContainerOperationLog */
t.wsComContainerOperationLog = {}
t.wsComContainerOperationLog.id = 'ID'
t.wsComContainerOperationLog.logType = '日志分类'
t.wsComContainerOperationLog.relationId = '关联ID'
t.wsComContainerOperationLog.logAction = '日志动作'
t.wsComContainerOperationLog.logActionName = '日志动作名称'
t.wsComContainerOperationLog.logDescription = '日志描述'

/* 重量调节策略明细-BaseEntity WsComWeightAdjustDetail */
t.wsComWeightAdjustDetail = {}
t.wsComWeightAdjustDetail.id = 'ID'
t.wsComWeightAdjustDetail.adjustId = '策略编号'
t.wsComWeightAdjustDetail.matchMinWeight = '匹配起始重量(KG)'
t.wsComWeightAdjustDetail.matchMaxWeight = '匹配截止重量(KG)'
t.wsComWeightAdjustDetail.adjustMode = '调整模式'
t.wsComWeightAdjustDetail.percentage = '调整比例'
t.wsComWeightAdjustDetail.fixedWeight = '固定重量(KG)'
t.wsComWeightAdjustDetail.randomStartWeight = '随机起始重量(KG)'
t.wsComWeightAdjustDetail.randomEndWeight = '随机截止重量(KG)'

/* 装袋作业 WsComBagJob */
t.wsComBagJob = {}
t.wsComBagJob.id = 'ID'
t.wsComBagJob.waybillId = '运单编号'
t.wsComBagJob.waybillNo = '运单号'
t.wsComBagJob.warehouseId = '仓库'
t.wsComBagJob.operator = '操作人'
t.wsComBagJob.operationTime = '操作时间'

/* 重量调节策略表 WsComWeightAdjust */
t.wsComWeightAdjust = {}
t.wsComWeightAdjust.id = 'ID'
t.wsComWeightAdjust.adjustName = '策略名称'
t.wsComWeightAdjust.adjustType = '策略类型'
t.wsComWeightAdjust.logisticsProductCode = '物流产品'
t.wsComWeightAdjust.status = '状态'
t.wsComWeightAdjust.updateDate = '更新时间'

/* 供应商 BdProvider */
t.bdProvider = {}
t.bdProvider.id = 'ID'
t.bdProvider.code = '代号'
t.bdProvider.name = '简称'
t.bdProvider.fullName = '全称'
t.bdProvider.type = '类型'
t.bdProvider.openDate = '开户时间'
t.bdProvider.channelCommissionerId = '公司渠道专员'
t.bdProvider.channelCommissionerName = '公司渠道专员'
t.bdProvider.customerServiceStaff = '供应商客服联系人'
t.bdProvider.customerServiceStaffPhone = '供应商客服联系人电话'
t.bdProvider.treasurer = '供应商财务联系人'
t.bdProvider.treasurerPhone = '供应商财务联系人电话'
t.bdProvider.phone = '手机'
t.bdProvider.email = '电子邮件'
t.bdProvider.country = '国家'
t.bdProvider.province = '省/州'
t.bdProvider.city = '市'
t.bdProvider.street = '街道'
t.bdProvider.houseNo = '门牌号'
t.bdProvider.postcode = '邮编'
t.bdProvider.address = '详细地址'
t.bdProvider.taxNo = '税号/营业执照号'
t.bdProvider.idNo = '身份证号'
t.bdProvider.status = '状态'
t.bdProvider.pushPathSetting = '推送轨迹设置'
t.bdProvider.isPushPath = '是否推送轨迹'
t.bdProvider.pushPathType = '推送轨迹类型'
t.bdProvider.apiTypeId = 'API接口类型编号'
/* 供应商文件 BdProviderDocument */
t.bdProviderDocument = {}
t.bdProviderDocument.id = 'ID'
t.bdProviderDocument.providerId = '供应商ID'
t.bdProviderDocument.credentialsFile = '工商执照/身份证文件'
t.bdProviderDocument.text = '工商信息'

/* 客户 BdCustomer */
t.bdCustomer = {}
t.bdCustomer.title = '客户'
t.bdCustomer.id = 'ID'
t.bdCustomer.code = '代号'
t.bdCustomer.codeTip = '若留空，系统会自动编码'
t.bdCustomer.name = '简称'
t.bdCustomer.fullName = '全称'
t.bdCustomer.type = '类型'
t.bdCustomer.openDate = '开户时间'
t.bdCustomer.holdUpPackage = '额度不足扣件'
t.bdCustomer.labelFormat = '面单格式'
t.bdCustomer.returnLabelForApi = '尾程单号接口查询返回面单'
t.bdCustomer.companyCustomerServiceStaffId = '公司客服专员'
t.bdCustomer.companyCustomerServiceStaffName = '公司客服专员'
t.bdCustomer.salesmanId = '公司业务员'
t.bdCustomer.salesmanName = '公司业务员'
t.bdCustomer.customerServiceStaff = '客户客服联系人'
t.bdCustomer.customerServiceStaffPhone = '客户客服联系人电话'
t.bdCustomer.treasurer = '客户财务联系人'
t.bdCustomer.treasurerPhone = '客户财务联系人电话'
t.bdCustomer.phone = '手机'
t.bdCustomer.email = '电子邮件'
t.bdCustomer.country = '国家'
t.bdCustomer.threePartySystemAccess = '三方系统设置'
t.bdCustomer.threePartySystemAccessBindStatus = '三方系统绑定状态'
t.bdCustomer.threePartySystemCustomerImport = '三方系统客户接口参数导入'
t.bdCustomer.bindCustomerApiTypeName = '绑定API类型:{name}'
t.bdCustomer.apiUpdateOrderInfo = '回推API参数设置'
t.bdCustomer.isApiUpdateOrderInfo = '是否API更新'
t.bdCustomer.notBindCustomerApiTypeStatus = '未绑定'
t.bdCustomer.bindedCustomerApiTypeStatus = '已绑定'
t.bdCustomer.bindCustomerApiTypeNull = '系统参数:BIND_CUSTOMER_API_TYPE未配置，请联系管理员处理！'
t.bdCustomer.bindCustomerApiTypeConfigError = '系统参数:BIND_CUSTOMER_API_TYPE配置错误，请联系管理员处理！'
t.bdCustomer.province = '省/州'
t.bdCustomer.city = '市'
t.bdCustomer.street = '街道'
t.bdCustomer.houseNo = '门牌号'
t.bdCustomer.postcode = '邮编'
t.bdCustomer.address = '详细地址'
t.bdCustomer.taxNo = '税号/营业执照号'
t.bdCustomer.idNo = '身份证号'
t.bdCustomer.companyId = '公司编号'
t.bdCustomer.settlementPeriod = '结算周期'
t.bdCustomer.tripartiteSystem = '三方系统客户'
t.bdCustomer.userToken = 'userToken'
t.bdCustomer.pushOrder = '是否API更新'
t.bdCustomer.customerApiParamSetup = '客户回推API参数设置'
t.bdCustomer.apiTypeId = 'API接口类型编号'
t.bdCustomer.clientUrl = '会员系统地址'
t.bdCustomer.username = '用户名'
t.bdCustomer.originPassword = '初始密码'
t.bdCustomer.copyMember = '复制会员登录信息'
t.bdCustomer.copyApi = '复制API对接信息'
t.bdCustomer.memberLogin = '会员登录'
t.bdCustomer.apiUrl = '接口地址根路径'
t.bdCustomer.apiDocUrl = '小包接口文档'
t.bdCustomer.apiDocUrl4Fba = 'FBA接口文档'
t.bdCustomer.apiDocUrl4Express = '快递接口文档'
t.bdCustomer.createUrl = '小包创建接口地址'
t.bdCustomer.cancelUrl = '小包取消接口地址'
t.bdCustomer.printUrl = '小包打印接口地址'
t.validate.idNoAndtaxNoMusthaveOne = '身份证号和税号最少要填写一个'

/* 运单操作日志 WsComWaybillOperateLog */
t.wsComWaybillOperateLog = {}
t.wsComWaybillOperateLog.id = 'ID'
t.wsComWaybillOperateLog.waybillId = '运单编号'
t.wsComWaybillOperateLog.operateNode = '操作节点'
t.wsComWaybillOperateLog.opeareteDescription = '操作描述'

/* 运单 WsComWaybill */
t.wsComWaybill = {}
t.wsComWaybill.id = 'ID'
t.wsComWaybill.info = '运单信息'
t.wsComWaybill.cust = '客户信息'
t.wsComWaybill.exception = '异常单详情'
t.wsComWaybill.log = '操作日志'
t.wsComWaybill.orderId = '订单编号'
t.wsComWaybill.customerOrderNo = '客户单号'
t.wsComWaybill.waybillNo = '运单号'
t.wsComWaybill.postalTrackingNo = '邮政单号'
t.wsComWaybill.deliveryNo = '派送单号'
t.wsComWaybill.logisticsProductCode = '运输方式'
t.wsComWaybill.logisticsChannelCode = '渠道代码'
t.wsComWaybill.providerCode = '供应商代码'
t.wsComWaybill.forecastWeight = '预报重量(KG)'
t.wsComWaybill.weight = '实重(KG)'
t.wsComWaybill.volumeWeight = '体积重(KG)'
t.wsComWaybill.length = '长(CM)'
t.wsComWaybill.width = '宽(CM)'
t.wsComWaybill.height = '高(CM)'
t.wsComWaybill.inAdjustWeight = '入库调节重(KG)'
t.wsComWaybill.outAdjustWeight = '出库调节重(KG)'
t.wsComWaybill.weightUnit = '重量单位'
t.wsComWaybill.storageLocation = '异常上架库位'
t.wsComWaybill.goodsCategory = '货物类别'
t.wsComWaybill.parcelType = '包裹类型'
t.wsComWaybill.electric = '是否带电'
t.wsComWaybill.remote = '是否偏远'
t.wsComWaybill.consigneeCountry = '收货国家'
t.wsComWaybill.consigneePostcode = '收货邮编'
t.wsComWaybill.customerRemark = '客户备注'
t.wsComWaybill.serviceId = '客服'
t.wsComWaybill.salesmanId = '业务员'
t.wsComWaybill.objectId = '客户/加盟'
t.wsComWaybill.objectName = '客户名称'
t.wsComWaybill.objectCode = '客户代号'
t.wsComWaybill.objectType = '对象类别'
t.wsComWaybill.warehouseId = '仓库'
t.wsComWaybill.plateformType = '来源平台'
t.wsComWaybill.packageQty = '包裹数'
t.wsComWaybill.exceptionFlag = '是否异常'
t.wsComWaybill.arrearage = '是否欠费'
t.wsComWaybill.addressInfo = '收寄件人信息'
t.wsComWaybill.declareInfo = '申报商品信息'
t.wsComWaybill.TrackInfo = '运单轨迹信息'
t.wsComWaybill.exceptionInfo = '异常单信息'
t.wsComWaybill.inedQty = '已入库'
t.wsComWaybill.asynNoStatus = '异步单号状态'

/* 仓库物料表 WsComMaterials */
t.wsComMaterials = {}
t.wsComMaterials.id = 'ID'
t.wsComMaterials.code = '物料代码'
t.wsComMaterials.name = '物料名称'
t.wsComMaterials.type = '物料类型'
t.wsComMaterials.status = '状态'
t.wsComMaterials.createDate = '创建时间'
t.wsComMaterials.warehouseId = '仓库'
t.wsComMaterials.weight = '重量(KG)'
t.wsComMaterials.length = '长(CM)'
t.wsComMaterials.width = '宽(CM)'
t.wsComMaterials.height = '高(CM)'
t.wsComMaterials.volume = '体积(CM³)'
t.wsComMaterials.untitled = '成本价格(元)'
t.wsComMaterials.specs = '物料规格尺寸(CM)'

/* 入库运单批次关系-BaseEntity WsFraInBatchWaybill */
t.wsFraInBatchWaybill = {}
t.wsFraInBatchWaybill.id = 'ID'
t.wsFraInBatchWaybill.waybillId = '运单编号'
t.wsFraInBatchWaybill.waybillNo = '运单号'
t.wsFraInBatchWaybill.batchId = '批次编号'
t.wsFraInBatchWaybill.batchNo = '批次号'
t.wsFraInBatchWaybill.scanNo = '单号'

/* 入库批次-BaseEntity WsFraInBatch */
t.wsFraInBatch = {}
t.wsFraInBatch.id = 'ID'
t.wsFraInBatch.batchNo = '批次号'
t.wsFraInBatch.franchisePointId = '加盟点编号'
t.wsFraInBatch.remarks = '加盟点备注'
t.wsFraInBatch.customerCode = '客户代码'
t.wsFraInBatch.customerName = '客户名称'
t.wsFraInBatch.finishUser = '完结人'
t.wsFraInBatch.finishTime = '完结时间'
t.wsFraInBatch.createDate = '创建时间'
t.wsFraInBatch.creator = '创建人'
t.wsFraInBatch.status = '状态'
t.wsFraInBatch.waybillTotalCount = '运单总数'
t.wsFraInBatch.waybillTotalWeight = '运单总重量(KG)'
t.wsFraInBatch.bagNetWeight = '容器净重(KG)'

/* 运单物流节点报表-BaseEntity RpWaybillLogisticsNode */
t.rpWaybillLogisticsNode = {}
t.rpWaybillLogisticsNode.id = 'ID'
t.rpWaybillLogisticsNode.waybillId = '运单编号'
t.rpWaybillLogisticsNode.waybillNo = '运单号'
t.rpWaybillLogisticsNode.deliveryNo = '派送单号'
t.rpWaybillLogisticsNode.nodeCode = '节点代码'
t.rpWaybillLogisticsNode.nodeName = '节点名称'

/* 中心仓基础信息表 WsOmsWarehouseInfo */
t.wsOmsWarehouseInfo = {}
t.wsOmsWarehouseInfo.id = 'ID'
t.wsOmsWarehouseInfo.code = '仓库代码'
t.wsOmsWarehouseInfo.name = '仓库名称'

/* 邮编截取规则 BdPostcodeRole */
t.bdPostcodeRole = {}
t.bdPostcodeRole.id = 'ID'
t.bdPostcodeRole.country = '国家'
t.bdPostcodeRole.cutRole = '截取规则'
t.bdPostcodeRole.postcodeLength = '邮编长度'
t.bdPostcodeRole.memo = '备注'

/* 编码规则 SysNumberRule */
t.sysNumberRule = {}
t.sysNumberRule.id = 'ID'
t.sysNumberRule.name = '名称'
t.sysNumberRule.codeType = '编码类型'
t.sysNumberRule.prefixCode = '编码前缀'
t.sysNumberRule.includedDate = '包含日期'
t.sysNumberRule.includedCheckBit = '包含校验位'
t.sysNumberRule.dateFormat = '日期格式'
t.sysNumberRule.serialNumLen = '流水号长度'
t.sysNumberRule.maxUse = '已用流水号'
t.sysNumberRule.description = '描述'

/* 回邮地址 BdReturnMailingAddress */
t.bdReturnMailingAddress = {}
t.bdReturnMailingAddress.id = 'ID'
t.bdReturnMailingAddress.tag = '标签'
t.bdReturnMailingAddress.contact = '联系人'
t.bdReturnMailingAddress.shippingCompany = '发货公司'
t.bdReturnMailingAddress.phone = '手机号'
t.bdReturnMailingAddress.mailbox = '邮箱'
t.bdReturnMailingAddress.country = '国家'
t.bdReturnMailingAddress.province = '省/州'
t.bdReturnMailingAddress.city = '市'
t.bdReturnMailingAddress.district = '区/县'
t.bdReturnMailingAddress.street = '街道'
t.bdReturnMailingAddress.address = '详细地址'
t.bdReturnMailingAddress.houseNumber = '门牌号'
t.bdReturnMailingAddress.postcode = '邮编'
t.bdReturnMailingAddress.defaultFlag = '是否默认'

/* 仓库基础资料 BdWarehouseInfo */
t.bdWarehouseInfo = {}
t.bdWarehouseInfo.id = 'ID'
t.bdWarehouseInfo.warehouseCode = '仓库代码'
t.bdWarehouseInfo.warehouseName = '仓库名称'
t.bdWarehouseInfo.outerWarehouseCode = '外部仓库代码'
t.bdWarehouseInfo.country = '所在国家'
t.bdWarehouseInfo.province = '所在省份'
t.bdWarehouseInfo.city = '所在城市'
t.bdWarehouseInfo.district = '所在区域'
t.bdWarehouseInfo.addressInfo = '地址详情'
t.bdWarehouseInfo.postCode = '邮编'
t.bdWarehouseInfo.contactPerson = '仓库负责人'
t.bdWarehouseInfo.contactPhone = '仓库联系电话'
t.bdWarehouseInfo.dutyTime = '上班时间'
t.bdWarehouseInfo.status = '状态'
t.bdWarehouseInfo.updateDate = '修改时间'
t.bdWarehouseInfo.companyId = '所属公司'

/* 员工仓库关系 BdUserWarehouseRelation */
t.bdUserWarehouseRelation = {}
t.bdUserWarehouseRelation.id = 'ID'
t.bdUserWarehouseRelation.username = '员工工号'
t.bdUserWarehouseRelation.realName = '员工姓名'
t.bdUserWarehouseRelation.user = '员工'
t.bdUserWarehouseRelation.warehouseId = '当前操作仓库'
t.bdUserWarehouseRelation.operateWarehouseIdList = '操作仓库'
t.bdUserWarehouseRelation.queryWarehouseIdList = '可见仓库'
t.bdUserWarehouseRelation.warehouseCode = '当前操作仓库代码'
t.bdUserWarehouseRelation.warehouseName = '当前操作仓库名称'
t.bdUserWarehouseRelation.curWarehouseName = '当前操作仓库'

/* 订单信息 CoOrder */
t.coOrder = {}
t.coOrder.id = '订单编号'
t.coOrder.customerOrderNo = '客户单号'
t.coOrder.waybillNo = '运单号'
t.coOrder.printProofOfPostage = '打印发货证明'
t.coOrder.waybillNoPlaceHold = '物流产品设置为手工输入才需要填写该单号'
t.coOrder.deliveryNo = '派送单号'
t.coOrder.deliveryNoPlaceHold = '只有线上物流才需要填写该单号'
t.coOrder.logisticsProductCode = '运输方式'
t.coOrder.taxPayMode = '税费模式'
t.coOrder.customsMethod = '报关方式'
t.coOrder.forecastWeight = '预报重量(KG)'
t.coOrder.parcelType = '包裹类型'
t.coOrder.goodsCategory = '货物类别'
t.coOrder.electric = '是否带电'
t.coOrder.magnetized = '是否带磁'
t.coOrder.liquid = '是否液体'
t.coOrder.powder = '是否粉末'
t.coOrder.remote = '是否偏远'
t.coOrder.insuredAmount = '保险金额'
t.coOrder.insuredCurrency = '保险币种'
t.coOrder.codAmount = 'COD金额'
t.coOrder.codCurrency = 'COD币种'
t.coOrder.declareCurrency = '申报币种'
t.coOrder.subCustomerOrderNo = '子客户单号'
t.coOrder.consigneeCountry = '收货国家'
t.coOrder.customerRemark = '客户备注'
t.coOrder.serviceId = '客服'
t.coOrder.salesmanId = '业务员'
t.coOrder.customerId = '客户'
t.coOrder.customerName = '客户名称'
t.coOrder.customerSimpleCode = '客户简码'
t.coOrder.warehouseId = '仓库'
t.coOrder.platformType = '来源平台'
t.coOrder.shopName = '店铺'
t.coOrder.salesUrl = '销售URL'
t.coOrder.packageQty = '包裹件数'
t.coOrder.print = '是否打印'
t.coOrder.status = '订单状态'
t.coOrder.enterDeclareInfo = '录入申报信息'

/* 预报资料导出 forecastDataExport */
t.forecastDataExport = {}
t.forecastDataExport.inputCountry = '请输入国家'
t.forecastDataExport.inputDate = '请输入时间'

/* 订单发货人 CoOrderShipper */
t.coOrderShipper = {}
t.coOrderShipper.id = '订单编号'
t.coOrderShipper.shipperName = '姓名'
t.coOrderShipper.shipperCompany = '公司'
t.coOrderShipper.shipperPhone = '电话'
t.coOrderShipper.shipperEmail = 'Email'
t.coOrderShipper.shipperCountry = '国家'
t.coOrderShipper.shipperProvince = '省/州'
t.coOrderShipper.shipperCity = '城市'
t.coOrderShipper.shipperDistrict = '区'
t.coOrderShipper.shipperAddress = '地址'
t.coOrderShipper.shipperPostcode = '邮编'
t.coOrderShipper.shipperStreet = '街道'
t.coOrderShipper.shipperDoorplate = '门牌'
t.coOrderShipper.iossTaxType = 'IOSS增值税类型'
t.coOrderShipper.iossNo = 'IOSS登记号'
t.coOrderShipper.eoriNo = 'EORI登记号'
t.coOrderShipper.vatNo = 'VAT/发件人税号'
t.coOrderShipper.vatCompanyEnName = 'VAT公司英文名'
t.coOrderShipper.vatRegisterCountry = 'VAT注册国家'
t.coOrderShipper.vatRegisterAddress = 'VAT注册地址'

t.coOrderShipper.shipperName2 = '发件人-姓名'
t.coOrderShipper.shipperCompany2 = '发件人-公司'
t.coOrderShipper.shipperPhone2 = '发件人-电话'
t.coOrderShipper.shipperEmail2 = '发件人-Email'
t.coOrderShipper.shipperCountry2 = '发件人-国家'
t.coOrderShipper.shipperProvince2 = '发件人-省/州'
t.coOrderShipper.shipperCity2 = '发件人-城市'
t.coOrderShipper.shipperDistrict2 = '发件人-区'
t.coOrderShipper.shipperAddress2 = '发件人-地址'
t.coOrderShipper.shipperPostcode2 = '发件人-邮编'
t.coOrderShipper.shipperStreet2 = '发件人-街道'
t.coOrderShipper.shipperDoorplate2 = '发件人-门牌'

/* 订单包裹 CoOrderPackage */
t.coOrderPackage = {}
t.coOrderPackage.id = 'ID'
t.coOrderPackage.orderId = '订单ID'
t.coOrderPackage.packageSerialNo = '包裹序号'
t.coOrderPackage.packageDeliveryNo = '子派送单号'
t.coOrderPackage.packageCustomerNo = '客户包裹号'
t.coOrderPackage.subCustomerOrderNo = '子客户单号'
t.coOrderPackage.packageWeight = '重量(KG)'
t.coOrderPackage.packageLength = '长(CM)'
t.coOrderPackage.packageWidth = '宽(CM)'
t.coOrderPackage.packageHeight = '高(CM)'
t.coOrderPackage.lwd = '三边长(CM)'
t.coOrderPackage.l2wd = '周长(CM)'
t.coOrderPackage.outTime = '出库时间'
t.coOrderPackage.inTime = '入库时间'
t.coOrderPackage.packTime = '打包时间'
t.coOrderPackage.size = '尺寸(CM)'
t.coOrderPackage.channelLabelUrl = '渠道面单URL'
t.coOrderPackage.picList = '入库图片'
t.coOrderPackage.no = '单号'
t.coOrderPackage.noType = '单号类型'
t.coOrderPackage.uploadTemplate = '上传'
t.coOrderPackage.downloadUploadTemplate = '下载上传模版'
t.coOrderPackage.confirmInWarehouse = '确定入库'
t.coOrderPackage.confirmBagging = '确定装入容器'
t.coOrderPackage.addBtn = '增加报关明细'
t.coOrderPackage.packageSize = '尺寸(CM)'
t.coOrderPackage.atLeastOne = '至少填写一个包裹明细'

/* 订单操作日志 CoOrderOperateLog */
t.coOrderOperateLog = {}
t.coOrderOperateLog.id = 'ID'
t.coOrderOperateLog.orderId = '订单编号'
t.coOrderOperateLog.operateNode = '操作节点'
t.coOrderOperateLog.opeareteDescription = '操作描述'

/* 订单其他信息 CoOrderOther */
t.coOrderOther = {}
t.coOrderOther.id = '订单编号'
t.coOrderOther.customerRealWeight = '客户实际重量(KG)'

/* 申报明细 CoOrderDeclare */
t.coOrderDeclare = {}
t.coOrderDeclare.id = 'ID'
t.coOrderDeclare.orderId = '订单编号'
t.coOrderDeclare.chineseName = '中文品名'
t.coOrderDeclare.englishName = '英文品名'
t.coOrderDeclare.quantity = '数量'
t.coOrderDeclare.unitNetWeight = '单位净重(KG)'
t.coOrderDeclare.unitDeclarePrice = '申报单价'
t.coOrderDeclare.brand = '品牌'
t.coOrderDeclare.goodsBarcode = '货物条码'
t.coOrderDeclare.sku = 'SKU'
t.coOrderDeclare.hsCode = '海关编码'
t.coOrderDeclare.productModel = '产品型号'
t.coOrderDeclare.material = '材质'
t.coOrderDeclare.purpose = '用途'
t.coOrderDeclare.origin = '原产地'
t.coOrderDeclare.pickingRemark = '配货备注'
t.coOrderDeclare.productUrl = '商品URL'

/* 报关信息 验证 */
t.coOrderDeclare.isDecimal3 = '1-3位小数'
t.coOrderDeclare.isDecimal2 = '1-2位小数'
t.coOrderDeclare.isPlusInteger2 = '非0正整数'
t.coOrderDeclare.letterAndNumber = '字母和数字组合'
t.coOrderDeclare.isEmail = '邮箱'
t.coOrderDeclare.atLeastOne = '至少填写一个报关明细'

/* 订单收货人 CoOrderConsignee */
t.coOrderConsignee = {}
t.coOrderConsignee.id = '订单编号'
t.coOrderConsignee.consigneeName = '姓名'
t.coOrderConsignee.consigneeCompany = '公司'
t.coOrderConsignee.consigneePhone = '电话'
t.coOrderConsignee.consigneeEmail = 'Email'
t.coOrderConsignee.consigneeCountry = '国家'
t.coOrderConsignee.consigneeProvince = '省/州'
t.coOrderConsignee.consigneeCity = '城市'
t.coOrderConsignee.consigneeDistrict = '区'
t.coOrderConsignee.consigneeAddress = '详细地址'
t.coOrderConsignee.consigneePostcode = '邮编'
t.coOrderConsignee.consigneeDoorplate = '门牌'
t.coOrderConsignee.consigneeStreet = '街道'
t.coOrderConsignee.consigneeIdcard = '证件号'
t.coOrderConsignee.consigneeTaxNo = '税号'
t.coOrderConsignee.consigneeName2 = '收件人-姓名'
t.coOrderConsignee.consigneeCompany2 = '收件人-公司'
t.coOrderConsignee.consigneePhone2 = '收件人-电话'
t.coOrderConsignee.consigneeEmail2 = '收件人-Email'
t.coOrderConsignee.consigneeCountry2 = '收件人-国家'
t.coOrderConsignee.consigneeProvince2 = '收件人-省/州'
t.coOrderConsignee.consigneeCity2 = '收件人-城市'
t.coOrderConsignee.consigneeDistrict2 = '收件人-区'
t.coOrderConsignee.consigneeAddress2 = '收件人-详细地址'
t.coOrderConsignee.consigneePostcode2 = '收件人-邮编'
t.coOrderConsignee.consigneeDoorplate2 = '收件人-门牌'
t.coOrderConsignee.consigneeStreet2 = '收件人-街道'

/* 订单渠道标签 CoOrderChannelLabel */
t.coOrderChannelLabel = {}
t.coOrderChannelLabel.id = 'ID'
t.coOrderChannelLabel.orderId = '订单编号'
t.coOrderChannelLabel.customerOrderNo = '客户单号'
t.coOrderChannelLabel.waybillNo = '运单号'
t.coOrderChannelLabel.deliveryNo = '派送单号'
t.coOrderChannelLabel.channelLabelUrl = '渠道面单地址'

/* 客户发货人信息-常用发货人设置 CoSetCustomerShipper */
t.coSetCustomerShipper = {}
t.coSetCustomerShipper.id = 'ID'
t.coSetCustomerShipper.customerId = '客户编码'
t.coSetCustomerShipper.defaulted = '是否默认'
t.coSetCustomerShipper.shortName = '别名'
t.coSetCustomerShipper.shipperName = '名称'
t.coSetCustomerShipper.shipperCompany = '公司'
t.coSetCustomerShipper.shipperContact = '联系方式'
t.coSetCustomerShipper.shipperEmail = 'Email'
t.coSetCustomerShipper.shipperCountryName = '发货国家名称'
t.coSetCustomerShipper.shipperCountryCode = '国家'
t.coSetCustomerShipper.shipperProvince = '省/州'
t.coSetCustomerShipper.shipperCity = '城市'
t.coSetCustomerShipper.shipperDistrict = '区'
t.coSetCustomerShipper.shipperAddress = '地址'
t.coSetCustomerShipper.shipperPostcode = '邮编'
t.coSetCustomerShipper.shipperDoorplate = '门牌'
t.coSetCustomerShipper.shipperStreet = '街道'

/* 申报明细 */
t.coSetCustomerDeclare = {}
t.coSetCustomerDeclare.setCustomerDeclare = '常用报关'
t.coSetCustomerDeclare.id = 'ID'
t.coSetCustomerDeclare.customerCode = '客户编码'
t.coSetCustomerDeclare.chineseName = '中文品名'
t.coSetCustomerDeclare.englishName = '英文品名'
t.coSetCustomerDeclare.quantity = '数量'
t.coSetCustomerDeclare.unitNetWeight = '单位净重（KG）'
t.coSetCustomerDeclare.unitDeclarePrice = '申报单价'
t.coSetCustomerDeclare.brand = '品牌'
t.coSetCustomerDeclare.goodsBarcode = '货物条码'
t.coSetCustomerDeclare.sku = 'SKU'
t.coSetCustomerDeclare.hsCode = '海关编码'
t.coSetCustomerDeclare.productModel = '产品型号'
t.coSetCustomerDeclare.material = '材质'
t.coSetCustomerDeclare.purpose = '用途'
t.coSetCustomerDeclare.origin = '原产地'
t.coSetCustomerDeclare.pickingRemark = '配货备注'
t.coSetCustomerDeclare.productUrl = '商品URL'

/* 入库取客户重量设置 WsComInboundCustomerWeight */
t.wsComInboundCustomerWeight = {}
t.wsComInboundCustomerWeight.id = 'ID'
t.wsComInboundCustomerWeight.customer = '客户'
t.wsComInboundCustomerWeight.customerId = '客户编码'
t.wsComInboundCustomerWeight.customerCode = '客户代号'
t.wsComInboundCustomerWeight.customerName = '客户名称'

/* 客服工单类型 CsmWorkOrderType */
t.csmWorkOrderType = {}
t.csmWorkOrderType.id = 'ID'
t.csmWorkOrderType.level = '类型级别'
t.csmWorkOrderType.parentId = '一级分类'
t.csmWorkOrderType.name = '二级分类'
t.csmWorkOrderType.description = '类型简述'
t.csmWorkOrderType.autoOverDays = '自动完结天数'
t.csmWorkOrderType.tips = '温馨提示'
t.csmWorkOrderType.status = '状态'
t.csmWorkOrderType.createDate = '创建时间'
t.csmWorkOrderType.updateDate = '更新时间'

/* 客服理赔申请 ClaimsApplication */
t.claimsApplication = {}
t.claimsApplication.customerType = '客户类型'
t.claimsApplication.customerName = '客户名称'
t.claimsApplication.createDate = '理赔时间'
t.claimsApplication.id = '理赔单号'
t.claimsApplication.waybillNo = '运单号'
t.claimsApplication.customerOrderNo = '客户单号'
t.claimsApplication.deliveryNo = '派送单号'
t.claimsApplication.orderId = '订单号'
t.claimsApplication.claimAmount = '理赔金额'
t.claimsApplication.currency = '理赔币种'
t.claimsApplication.claimReason = '理赔原因'
t.claimsApplication.status = '理赔状态'
t.claimsApplication.add = '申请理赔'
t.claimsApplication.batchAdd = '批量申请'
t.claimsApplication.getWaybillInfo = '获取单号信息'
t.claimsApplication.claimsDetail = '理赔信息'
t.claimsApplication.approvalResult = '审核回复'
t.claimsApplication.waybillNoIsNull = '请输入运单号或者派送单号'

/* 工单日志 CsmWorkOrderLog */
t.csmWorkOrderLog = {}
t.csmWorkOrderLog.id = 'ID'
t.csmWorkOrderLog.workOrderId = '工单编号'
t.csmWorkOrderLog.logCode = '日志动作'
t.csmWorkOrderLog.logDescription = '日志描述'
t.csmWorkOrderLog.creator = '操作人'
t.csmWorkOrderLog.createDate = '操作时间'

/* 工单评价-baseEntity CsmWorkOrderEvaluation */
t.csmWorkOrderEvaluation = {}
t.csmWorkOrderEvaluation.id = 'ID'
t.csmWorkOrderEvaluation.workOrderId = '工单编号'
t.csmWorkOrderEvaluation.workOrderNo = '工单号'
t.csmWorkOrderEvaluation.resolved = '问题已解决'
t.csmWorkOrderEvaluation.score = '工单评分'
t.csmWorkOrderEvaluation.evaluationContent = '评价内容'
t.csmWorkOrderEvaluation.createDate = '评价时间'

/* 工单沟通记录明细-baseEntity CsmWorkOrderCommunication */
t.csmWorkOrderCommunication = {}
t.csmWorkOrderCommunication.id = 'ID'
t.csmWorkOrderCommunication.workOrderId = '工单编号'
t.csmWorkOrderCommunication.workOrderNo = '工单号'
t.csmWorkOrderCommunication.communicationType = '沟通类别'
t.csmWorkOrderCommunication.communicationContent = '沟通内容'
t.csmWorkOrderCommunication.ipAddress = 'IP地址'
t.csmWorkOrderCommunication.creatorName = '创建人名称'
t.csmWorkOrderCommunication.creataDate = '时间'

/* 工单关联附件-baseEntity CsmWorkOrderAttachment */
t.csmWorkOrderAttachment = {}
t.csmWorkOrderAttachment.id = 'ID'
t.csmWorkOrderAttachment.workOrderId = '工单编号'
t.csmWorkOrderAttachment.workOrderNo = '工单号'
t.csmWorkOrderAttachment.relationObject = '关联对象'
t.csmWorkOrderAttachment.relationId = '关联编号'
t.csmWorkOrderAttachment.attachmentUrl = '附件地址'

/* 客服工单 CsmWorkOrder */
t.csmWorkOrder = {}
t.csmWorkOrder.id = 'ID'
t.csmWorkOrder.workOrderNo = '工单号'
t.csmWorkOrder.workOrderTypeId = '工单类型'
t.csmWorkOrder.workOrderTypeParentId = '工单类别'
t.csmWorkOrder.waybillNo = '运单编号'
t.csmWorkOrder.customerVoucherNo = '客户单号'
t.csmWorkOrder.problemStatement = '问题描述'
t.csmWorkOrder.contactPerson = '联系人'
t.csmWorkOrder.phone = '联系电话'
t.csmWorkOrder.email = '联系邮箱'
t.csmWorkOrder.urgentLevel = '紧急程度'
t.csmWorkOrder.assignee = '分配人'
t.csmWorkOrder.assignTime = '分配时间'
t.csmWorkOrder.acceptServiceId = '受理客服'
t.csmWorkOrder.startProcessingTime = '开始处理时间'
t.csmWorkOrder.firstReplyTime = '第一次答复时间'
t.csmWorkOrder.lastReplyTime = '最新答复时间'
t.csmWorkOrder.lastConsultationTime = '最新咨询时间'
t.csmWorkOrder.completionTime = '处理完成时间'
t.csmWorkOrder.completionType = '完成类型'
t.csmWorkOrder.closeTime = '关闭时间'
t.csmWorkOrder.closeType = '关闭类型'
t.csmWorkOrder.newNote = '新的答复'
t.csmWorkOrder.newConsultation = '新的咨询'
t.csmWorkOrder.creatorType = '创建人类型'
t.csmWorkOrder.creatorName = '创建人名称'
t.csmWorkOrder.status = '工单状态'
t.csmWorkOrder.creator = '提交人'
t.csmWorkOrder.createDate = '提交时间'
t.csmWorkOrder.createDuration = '创建时长(小时)'
t.csmWorkOrder.companyCustomerServiceStaffId = '专属客服'
t.csmWorkOrder.assignDuration = '分配时长'
t.csmWorkOrder.lastReplyDuration = '最新答复时长'
t.csmWorkOrder.processDuration = '处理时长'
t.csmWorkOrder.export = '导出报表'
t.csmWorkOrder.accept = '答复'
t.csmWorkOrder.handleing = '正在处理'

/* 重量调节用户引用 WsComWeightAdjustCustomer */
t.wsComWeightAdjustCustomer = {}
t.wsComWeightAdjustCustomer.id = 'ID'
t.wsComWeightAdjustCustomer.adjustId = '策略编号'
t.wsComWeightAdjustCustomer.customerCode = '客户代号'
t.wsComWeightAdjustCustomer.customerName = '客户名称'
t.wsComWeightAdjustCustomer.customer = '客户'
t.wsComWeightAdjustCustomer.deleteUser = '删除人'
t.wsComWeightAdjustCustomer.deleteTime = '删除时间'

/* 客户入库核重设置 WsComCustomerWeightCheck */
t.wsComCustomerWeightCheck = {}
t.wsComCustomerWeightCheck.id = 'ID'
t.wsComCustomerWeightCheck.customerCode = '客户代码'
t.wsComCustomerWeightCheck.customerName = '客户名称'
t.wsComCustomerWeightCheck.customer = '客户'
t.wsComCustomerWeightCheck.logisticsProductCode = '物流产品'
t.wsComCustomerWeightCheck.logisticsProduct = '运输方式'
t.wsComCustomerWeightCheck.weightErrorRange = '重量误差范围(KG)'
t.wsComCustomerWeightCheck.weightErrorRangeTitle = '重量误差范围/重量临界值(KG)'
t.wsComCustomerWeightCheck.weightNoRange = '重量临界值(KG)'
t.wsComCustomerWeightCheck.updateDate = '更新时间'
t.wsComCustomerWeightCheck.status = '状态'
t.wsComCustomerWeightCheck.weightStrategy = '重量策略'
t.wsComCustomerWeightCheck.weightType = '校验类型'
t.wsComCustomerWeightCheck.customerWeightSource = '订单重量来源'

/* 渠道燃油 BdLogisticsChannelFuel */
t.bdLogisticsChannelFuel = {}
t.bdLogisticsChannelFuel.id = 'ID'
t.bdLogisticsChannelFuel.channelType = '渠道类型'
t.bdLogisticsChannelFuel.fuelRate = '燃油率'
t.bdLogisticsChannelFuel.beginDate = '生效时间'

/* 物流渠道类型 BdLogisticsChannelType */
t.bdLogisticsChannelType = {}
t.bdLogisticsChannelType.id = 'ID'
t.bdLogisticsChannelType.code = '代号'
t.bdLogisticsChannelType.name = '名称'
t.bdLogisticsChannelType.logisticsType = '物流类型'

/* 陆运核单 BaGroundFreightBill */
t.baGroundFreightBill = {}
t.baGroundFreightBill.id = 'ID'
t.baGroundFreightBill.providerId = '运输公司'
t.baGroundFreightBill.businessId = '作业单号'
t.baGroundFreightBill.logisticsProductCode = '陆运渠道'
t.baGroundFreightBill.sourceAddr = '起运地'
t.baGroundFreightBill.targetAddr = '目的地'
t.baGroundFreightBill.vehicleType = '车型'
t.baGroundFreightBill.transportMode = '运输模式'
t.baGroundFreightBill.driver = '司机'
t.baGroundFreightBill.carNumber = '车牌号'
t.baGroundFreightBill.weight = '重量(KG)'
t.baGroundFreightBill.volWeight = '体积重'
t.baGroundFreightBill.vol = '体积(M³)'
t.baGroundFreightBill.qty = '件数'
t.baGroundFreightBill.amount = '金额'
t.baGroundFreightBill.loadingTime = '装车时间'
t.baGroundFreightBill.loader = '装车员'
t.baGroundFreightBill.memo = '备注'
t.baGroundFreightBill.site = '站点'
t.baGroundFreightBill.apportionmentStatus = '分摊状态'

/* 境外清关核单 BaOverseasClearanceChecklist */
t.baOverseasClearanceChecklist = {}
t.baOverseasClearanceChecklist.id = 'ID'
t.baOverseasClearanceChecklist.providerId = '航空公司'
t.baOverseasClearanceChecklist.businessId = '作业单号'
t.baOverseasClearanceChecklist.logisticsProductCode = '清关渠道'
t.baOverseasClearanceChecklist.customsPort = '清关口岸'
t.baOverseasClearanceChecklist.airport = '落地机场'
t.baOverseasClearanceChecklist.customsMode = '清关模式'
t.baOverseasClearanceChecklist.flight = '航班'
t.baOverseasClearanceChecklist.weight = '重量'
t.baOverseasClearanceChecklist.volWeight = '体积重'
t.baOverseasClearanceChecklist.vol = '体积'
t.baOverseasClearanceChecklist.declaredValue = '申报价值'
t.baOverseasClearanceChecklist.qty = '件数'
t.baOverseasClearanceChecklist.amount = '金额'
t.baOverseasClearanceChecklist.customsClearanceTime = '清关时间'
t.baOverseasClearanceChecklist.site = '站点'
t.baOverseasClearanceChecklist.apportionmentStatus = '分摊状态'

/* 成本计算源出库单 BaCostOutOrder */
t.baCostOutOrder = {}
t.baCostOutOrder.id = 'ID'
t.baCostOutOrder.checklistType = '核单类型'
t.baCostOutOrder.checklistId = '核单ID'
t.baCostOutOrder.billNumber = '提单号'
t.baCostOutOrder.logisticsChannelId = '物流渠道'
t.baCostOutOrder.actualWeight = '实重'
t.baCostOutOrder.qty = '件数'
t.baCostOutOrder.site = '站点'
t.baCostOutOrder.checkoutTime = '出库时间'

/* 成本费用信息 BaCostFeeDetails */
t.baCostFeeDetails = {}
t.baCostFeeDetails.id = 'ID'
t.baCostFeeDetails.checklistType = '核单类型'
t.baCostFeeDetails.checklistId = '核单ID'
t.baCostFeeDetails.feeType = '费用项'
t.baCostFeeDetails.currency = '币种'
t.baCostFeeDetails.feeAmount = '费用金额'

/* 取件核单 BaPickupChecklist */
t.baPickupChecklist = {}
t.baPickupChecklist.id = 'ID'
t.baPickupChecklist.pickupCompany = '取件公司'
t.baPickupChecklist.route = '线路'
t.baPickupChecklist.carModel = '车型'
t.baPickupChecklist.carNumber = '车牌号'
t.baPickupChecklist.driver = '司机'
t.baPickupChecklist.weight = '重量'
t.baPickupChecklist.vol = '体积'
t.baPickupChecklist.declaredValue = '申报价值'
t.baPickupChecklist.qty = '件数'
t.baPickupChecklist.amount = '金额'
t.baPickupChecklist.recipient = '取件员'
t.baPickupChecklist.pickpuTime = '取件时间'
t.baPickupChecklist.site = '站点'
t.baPickupChecklist.apportionmentStatus = '分摊状态'

/* 航空核单 BaAviationBill */
t.baAviationBill = {}
t.baAviationBill.id = 'ID'
t.baAviationBill.airlineCompany = '航空公司'
t.baAviationBill.businessId = '作业单号'
t.baAviationBill.logisticsProductCode = '空运渠道'
t.baAviationBill.flight = '航班'
t.baAviationBill.weight = '重量'
t.baAviationBill.volWeight = '体积重'
t.baAviationBill.vol = '体积'
t.baAviationBill.qty = '件数'
t.baAviationBill.amount = '金额'
t.baAviationBill.departureTime = '起飞时间'
t.baAviationBill.apportionmentStatus = '分摊状态'
t.baAviationBill.billNumber = '提单号'

/* 境内清关核单 BaDomesticClearanceChecklist */
t.baDomesticClearanceChecklist = {}
t.baDomesticClearanceChecklist.id = 'ID'
t.baDomesticClearanceChecklist.businessId = '作业单号'
t.baDomesticClearanceChecklist.providerId = '航空公司'
t.baDomesticClearanceChecklist.logisticsProductCode = '清关渠道'
t.baDomesticClearanceChecklist.flight = '航班'
t.baDomesticClearanceChecklist.weight = '重量'
t.baDomesticClearanceChecklist.volWeight = '体积重'
t.baDomesticClearanceChecklist.vol = '体积'
t.baDomesticClearanceChecklist.declaredValue = '申报价值'
t.baDomesticClearanceChecklist.qty = '件数'
t.baDomesticClearanceChecklist.amount = '金额'
t.baDomesticClearanceChecklist.customsClearanceTime = '清关时间'
t.baDomesticClearanceChecklist.apportionmentStatus = '分摊状态'
t.baDomesticClearanceChecklist.costVoucher = '附件'
t.baDomesticClearanceChecklist.customsPort = '清关口岸'
t.baDomesticClearanceChecklist.airport = '起运机场'
t.baDomesticClearanceChecklist.customsMode = '清关方式'

/* 成本分摊明细 BaCostAllocationDetails */
t.baCostAllocationDetails = {}
t.baCostAllocationDetails.id = 'ID'
t.baCostAllocationDetails.checklistType = '作业单类型'
t.baCostAllocationDetails.costFeeId = '成本费用单ID'
t.baCostAllocationDetails.checklistId = '作业单ID'
t.baCostAllocationDetails.checklistNo = '作业单号'
t.baCostAllocationDetails.waybillNo = '派送单号/委托单号'
t.baCostAllocationDetails.masterWaybillNo = '派送单号'
t.baCostAllocationDetails.waybillNumber = '运单号'
t.baCostAllocationDetails.weight = '重量'
t.baCostAllocationDetails.qty = '件数'
t.baCostAllocationDetails.feeType = '费用项'
t.baCostAllocationDetails.divideMethod = '分摊类型'
t.baCostAllocationDetails.apportionmentUnitQty = '分摊类型单位数'
t.baCostAllocationDetails.apportionmentTotalQty = '分摊类型总数'
t.baCostAllocationDetails.currency = '币种'
t.baCostAllocationDetails.feeAmount = '费用项金额'
t.baCostAllocationDetails.apportionmentAmount = '分摊金额'

/* 客户指定交货仓库 BdCustomerWarehouseAppointment */
t.bdCustomerWarehouseAppointment = {}
t.bdCustomerWarehouseAppointment.id = 'ID'
t.bdCustomerWarehouseAppointment.object = '客户'
t.bdCustomerWarehouseAppointment.objectId = '客户编码'
t.bdCustomerWarehouseAppointment.objectCode = '客户代码'
t.bdCustomerWarehouseAppointment.objectName = '客户名称'
t.bdCustomerWarehouseAppointment.customerType = '客户类别'
t.bdCustomerWarehouseAppointment.warehouseId = '交货仓'
t.bdCustomerWarehouseAppointment.warehouseCode = '仓库代码'
t.bdCustomerWarehouseAppointment.warehouseName = '仓库名称'
t.bdCustomerWarehouseAppointment.updateDate = '更新时间'

/* 信用额度管理表 BdCreditsManagement */
t.bdCreditsManagement = {}
t.bdCreditsManagement.id = 'ID'
t.bdCreditsManagement.object = '客户'
t.bdCreditsManagement.objectId = '客户编码'
t.bdCreditsManagement.objectCode = '客户代码'
t.bdCreditsManagement.objectName = '客户名称'
t.bdCreditsManagement.customerType = '客户类别'
t.bdCreditsManagement.creditQuota = '信用额度'
t.bdCreditsManagement.creditCurrency = '信用币种'
t.bdCreditsManagement.updateDate = '更新时间'
t.bdCreditsManagement.status = '状态'

/* 异常类型基础资料 BdExceptionType */
t.bdExceptionType = {}
t.bdExceptionType.id = 'ID'
t.bdExceptionType.exceptionCode = '异常代码'
t.bdExceptionType.exceptionName = '异常名称'
t.bdExceptionType.exceptionType = '标记类型'
t.bdExceptionType.customerVisible = '客户可见'
t.bdExceptionType.csProcess = '需要客服处理'
t.bdExceptionType.createDate = '创建时间'
t.bdExceptionType.updateDate = '更新时间'
t.bdExceptionType.status = '状态'

/* 轨迹明细表 TksTrackingDetail */
t.tksTrackingDetail = {}
t.tksTrackingDetail.id = 'ID'
t.tksTrackingDetail.waybillNo = '运单号'
t.tksTrackingDetail.trackingType = '轨迹类型'
t.tksTrackingDetail.trackingCode = '轨迹代码'
t.tksTrackingDetail.trackingContentEn = '轨迹描述EN'
t.tksTrackingDetail.trackingContentCn = '轨迹描述CN'
t.tksTrackingDetail.trackingLocation = '轨迹发生地'
t.tksTrackingDetail.trackingTime = '轨迹发生时间'
t.tksTrackingDetail.timeZone = '轨迹时区'
t.tksTrackingDetail.eventCountry = '轨迹所在国家'
t.tksTrackingDetail.eventProvince = '轨迹所在省/州'
t.tksTrackingDetail.eventCity = '轨迹所在城市'
t.tksTrackingDetail.trackingLocation = '轨迹发生地'
t.tksTrackingDetail.goToAutonomousPushStatus = '流转状态'
t.tksTrackingDetail.zipCode = '轨迹所在地邮编'
t.tksTrackingDetail.flightNo = '轨迹航班号'
t.tksTrackingDetail.internalFlag = '内部轨迹标记'

/* 轨迹信息基础资料 TksTrackingCodeInfo */
t.tksTrackingCodeInfo = {}
t.tksTrackingCodeInfo.id = 'ID'
t.tksTrackingCodeInfo.trackingCode = '轨迹代码'
t.tksTrackingCodeInfo.trackingContent = '轨迹内容'
t.tksTrackingCodeInfo.trackingContentMultiple = '轨迹内容，可多选'
t.tksTrackingCodeInfo.trackingContentZh = '轨迹内容中文'
t.tksTrackingCodeInfo.trackingContentEn = '轨迹内容英文'
t.tksTrackingCodeInfo.trackingNodeCode = '轨迹节点段'
t.tksTrackingCodeInfo.seaport = '港口'
t.tksTrackingCodeInfo.airport = '机场'

/* 轨迹业务主数据队列表 TksTrackingQueue */
t.tksTrackingWaybill = {}
t.tksTrackingWaybill.waybillNo = '运单号'
t.tksTrackingWaybill.departureCountry = '起运地国家'
t.tksTrackingWaybill.destinationCountry = '目的地国家'
t.tksTrackingWaybill.consigneePostCode = '收件人邮编'
t.tksTrackingWaybill.customerCode = '客户'
t.tksTrackingWaybill.customerName = '客户'
t.tksTrackingWaybill.customerId = '客户'
t.tksTrackingWaybill.providerId = '供应商'
t.tksTrackingWaybill.customerOrderNo = '客户单号'
t.tksTrackingWaybill.deliveryNo = '派送单号'
t.tksTrackingWaybill.masterDeliveryNo = '主派送单号'
t.tksTrackingWaybill.postalTrackingNo = '邮政单号'
t.tksTrackingWaybill.subDeliveryNo = '子派送单号'
t.tksTrackingWaybill.logisticsProductCode = '物流产品'
t.tksTrackingWaybill.logisticsChannelCode = '物流渠道'
t.tksTrackingWaybill.businessType = '业务类型'
t.tksTrackingWaybill.trackingFlag = '跟踪状态'
t.tksTrackingWaybill.status = '状态'
t.tksTrackingWaybill.createDate = '创建时间'
t.tksTrackingWaybill.planTrackingTime = '下次跟进时间'
t.tksTrackingWaybill.lastTrackingTime = '最新轨迹时间'
t.tksTrackingWaybill.inTime = '入库时间'
t.tksTrackingWaybill.outTime = '出库时间'
t.tksTrackingWaybill.deliveryTime = '签收时间'
t.tksTrackingWaybill.firstDeliveryTrackTime = '上网时间'
t.tksTrackingWaybill.lastTrackingLocation = '最新轨迹地址'
t.tksTrackingWaybill.lastTrackingCode = '轨迹节点'
t.tksTrackingWaybill.lastTrackingContent = '最新轨迹内容'
t.tksTrackingWaybill.collectPackageTimeliness = '揽收时效'
t.tksTrackingWaybill.deliveryPackageTimeliness = '派送时效'
t.tksTrackingWaybill.batchDel = '批量删除'
t.tksTrackingWaybill.batchDelDetail = '批量删除明细'
t.tksTrackingWaybill.batchAdd = '批量添加'
t.tksTrackingWaybill.batchToAutonomousPush = '移至自主推送'
t.tksTrackingWaybill.updatePostCodeForDelivery = '变更签收邮编'
t.tksTrackingWaybill.batchDelTks = '批量删除轨迹'
t.tksTrackingWaybill.trackingFlag = '跟踪状态'
t.tksTrackingWaybill.registerNo = '即刻跟踪轨迹'
t.tksTrackingWaybill.retrace = '重新跟踪轨迹'

/* 号段组 BdWaybillNoGroup */
t.bdWaybillNoGroup = {}
t.bdWaybillNoGroup.id = 'ID'
t.bdWaybillNoGroup.groupName = '分组名称'
t.bdWaybillNoGroup.type = '分组类型'
t.bdWaybillNoGroup.defaultFlag = '是否默认'
t.bdWaybillNoGroup.remarks = '备注'
t.bdWaybillNoGroup.companyId = '所属公司'
t.bdWaybillNoGroup.prefix = '前缀'
t.bdWaybillNoGroup.suffix = '后缀'
t.bdWaybillNoGroup.status = '状态'
t.bdWaybillNoGroup.prefixPlaceHolder = '单号前缀,只允许输入大写字母或者数字'
t.bdWaybillNoGroup.suffixPlaceHolder = '单号后缀,只允许输入大写字母或者数字'

/* 黑名单库 BdBlacklistLibrary */
t.bdBlacklistLibrary = {}
t.bdBlacklistLibrary.id = 'ID'
t.bdBlacklistLibrary.templateId = '模板id'
t.bdBlacklistLibrary.keyValue = '黑名单'
t.bdBlacklistLibrary.creator = '创建人'
t.bdBlacklistLibrary.createDate = '创建时间'

/* 黑名单模板 BdBlacklistTemplate */
t.bdBlacklistTemplate = {}
t.bdBlacklistTemplate.id = 'ID'
t.bdBlacklistTemplate.templateName = '模板名称'
t.bdBlacklistTemplate.blacklistType = '黑名单类型'
t.bdBlacklistTemplate.remarks = '备注'
t.bdBlacklistTemplate.status = '状态'
t.bdBlacklistTemplate.updateDate = '更新时间'
t.bdBlacklistTemplate.import = '黑名单导入'
t.bdBlacklistTemplate.singleImport = '邮编黑名单导入'

/* 邮编区域匹配 BdPostcodeRegionMatch */
t.bdPostcodeRegionMatch = {}
t.bdPostcodeRegionMatch.id = 'ID'
t.bdPostcodeRegionMatch.countryCode = '国家'
t.bdPostcodeRegionMatch.province = '省'
t.bdPostcodeRegionMatch.city = '城市'
t.bdPostcodeRegionMatch.district = '区'
t.bdPostcodeRegionMatch.enProvince = '省(英文）'
t.bdPostcodeRegionMatch.enCity = '城市（英文）'
t.bdPostcodeRegionMatch.enDistrict = '区（英文）'
t.bdPostcodeRegionMatch.postcode = '邮编'

/* 物流产品渠道分拣码 BdLogisticsProductChannelSortCode */
t.bdLogisticsProductChannelSortCode = {}
t.bdLogisticsProductChannelSortCode.id = 'ID'
t.bdLogisticsProductChannelSortCode.setSortCode = '分拣码设置'
t.bdLogisticsProductChannelSortCode.objectType = '对象类型'
t.bdLogisticsProductChannelSortCode.objectId = '对象编号'
t.bdLogisticsProductChannelSortCode.zone = '分区号'
t.bdLogisticsProductChannelSortCode.labelTemplateId = '标签模板'
t.bdLogisticsProductChannelSortCode.sortCode = '分拣码'
t.bdLogisticsProductChannelSortCode.coordinateXAxis = '坐标x轴'
t.bdLogisticsProductChannelSortCode.coordinateYAxis = '坐标y轴'

/* 作业成本费用项明细-BaseEntity WsComOperationCostDetail */
t.wsComOperationCostDetail = {}
t.wsComOperationCostDetail.id = 'ID'
t.wsComOperationCostDetail.operationCostId = '作业成本编号'
t.wsComOperationCostDetail.costItemCode = '费用项'
t.wsComOperationCostDetail.costValue = '金额'
t.wsComOperationCostDetail.currency = '币种'

/* 作业成本维护-BaseEntity WsComOperationCost */
t.wsComOperationCost = {}
t.wsComOperationCost.id = '作业单ID'
t.wsComOperationCost.relationNo = '作业单号'
t.wsComOperationCost.operationNode = '作业发生节点'
t.wsComOperationCost.channelCode = '渠道代码'
t.wsComOperationCost.channel = '渠道'
t.wsComOperationCost.channelName = '渠道名称'
t.wsComOperationCost.providerId = '供应商'
t.wsComOperationCost.transportWay = '运输方式'
t.wsComOperationCost.transportNo = '运输班次号'
t.wsComOperationCost.operationTime = '作业发生时间'
t.wsComOperationCost.totalWeight = '总重量'
t.wsComOperationCost.totalVolumeWeight = '总体积重(KG)'
t.wsComOperationCost.unitWeight = '单箱重(KG)'
t.wsComOperationCost.totalVolume = '总体积(M³)'
t.wsComOperationCost.declareValue = '申报价值'
t.wsComOperationCost.quantity = '总件数'
t.wsComOperationCost.costVoucher = '费用凭证附件'
t.wsComOperationCost.remarks = '备注'
t.wsComOperationCost.totalSum = '总金额'
t.wsComOperationCost.status = '状态'
t.wsComOperationCost.createDate = '创建时间'

/* 作业成本关联表-BaseEntity WsComOperationCostRelationship */
t.wsComOperationCostRelationship = {}
t.wsComOperationCostRelationship.id = 'ID'
t.wsComOperationCostRelationship.operationCostId = '作业成本编号'
t.wsComOperationCostRelationship.relationType = '关联类型'
t.wsComOperationCostRelationship.relationId = '关联ID'
t.wsComOperationCostRelationship.relationNo = '关联编号'

/* 毛利统计报表 RpGrossStatistics */
t.rpGrossStatistics = {}
t.rpGrossStatistics.id = 'ID'
t.rpGrossStatistics.customerId = '客户Id'
t.rpGrossStatistics.customerName = '客户名称'
t.rpGrossStatistics.mawbId = '提单'
t.rpGrossStatistics.inBillId = '入库运单ID'
t.rpGrossStatistics.outBillId = '出库运单ID'
t.rpGrossStatistics.waybillNo = '运单号'
t.rpGrossStatistics.deliveryNo = '派送单号'
t.rpGrossStatistics.customerOrderNo = '客户单号'
t.rpGrossStatistics.logisticsProductCode = '物流产品'
t.rpGrossStatistics.logisticsChannelCode = '物流渠道'
t.rpGrossStatistics.currency = '币种'
t.rpGrossStatistics.receivableSum = '应收金额'
t.rpGrossStatistics.payableSum = '应付金额'
t.rpGrossStatistics.costFee = '成本费用'
t.rpGrossStatistics.commissionFee = '提成费用'
t.rpGrossStatistics.grossprofitSum = '毛利金额'
t.rpGrossStatistics.nocommissionGrossprofitSum = '扣提成毛利金额'
t.rpGrossStatistics.optDate = '业务时间'
t.rpGrossStatistics.packageQty = '件数'
t.rpGrossStatistics.weight = '实重'
t.rpGrossStatistics.volumeWeight = '材重'
t.rpGrossStatistics.balanceWeight = '结算重'
t.rpGrossStatistics.volume = '方数'
t.rpGrossStatistics.inRemark = '入库备注'

/*  RpGrossStatisticsWaybillNoTemp */
t.rpGrossStatisticsWaybillNoTemp = {}
t.rpGrossStatisticsWaybillNoTemp.id = 'ID'
t.rpGrossStatisticsWaybillNoTemp.statisticsType = '统计类型'
t.rpGrossStatisticsWaybillNoTemp.waybillNo = '运单号'

/* 平台轨迹配置 BdPlatformTracking */
t.bdPlatformTracking = {}
t.bdPlatformTracking.id = 'ID'
t.bdPlatformTracking.trackingCode = '轨迹代码'
t.bdPlatformTracking.trackingContentEn = '轨迹描述EN'
t.bdPlatformTracking.trackingContentCn = '轨迹描述CN'
t.bdPlatformTracking.preferredSourceTrackingContent = '优先使用来源轨迹描述'
t.bdPlatformTracking.platformTrackingCode = '平台轨迹代码'
t.bdPlatformTracking.platformTrackingContentEn = '平台轨迹描述EN'
t.bdPlatformTracking.platformTrackingContentCn = '平台轨迹描述CN'
t.bdPlatformTracking.platformCode = '平台代码'
t.bdPlatformTracking.remark = '备注'

/* 轨迹节点推送-EMS tksTrackingNodeEMS */
t.tksTrackingNodeEMS = {}
t.tksTrackingNodeEMS.location = '轨迹发生地'
t.tksTrackingNodeEMS.content = '轨迹内容'

/* 州/省名称匹配 BdProvinceNameMatch */
t.bdProvinceNameMatch = {}
t.bdProvinceNameMatch.id = 'ID'
t.bdProvinceNameMatch.countryCode = '国家代码'
t.bdProvinceNameMatch.province = '省/州简码'
t.bdProvinceNameMatch.provinceEn = '省/州_英文名称'

/* 订单默认值表 BdOrderDefaultValue */
t.bdOrderDefaultValue = {}
t.bdOrderDefaultValue.id = 'ID'
t.bdOrderDefaultValue.logisticsChannelId = '尾程派送渠道'
t.bdOrderDefaultValue.fieldKey = '字段属性'
t.bdOrderDefaultValue.fieldName = '字段属性名称'
t.bdOrderDefaultValue.fieldType = '字段类型'
t.bdOrderDefaultValue.defaultValue = '默认值'
t.bdOrderDefaultValue.defaultValueType = '默认值类型'
t.bdOrderDefaultValue.linkFieldName = '联动字段'
t.bdOrderDefaultValue.fillType = '填充方式'

/* 对账差异明细 BaPayableSettleBillDifferenceFee */
t.baPayableSettleBillDifferenceFee = {}
t.baPayableSettleBillDifferenceFee.id = 'ID'
t.baPayableSettleBillDifferenceFee.type = '类型'
t.baPayableSettleBillDifferenceFee.payableBizOrderId = '费用主单号'
t.baPayableSettleBillDifferenceFee.subPayableBizOrderId = '费用子单号'
t.baPayableSettleBillDifferenceFee.payableFeeId = '费用明细单号'
t.baPayableSettleBillDifferenceFee.inBusinessId = '入库业务单号'
t.baPayableSettleBillDifferenceFee.outBusinessId = '出库业务主单号'
t.baPayableSettleBillDifferenceFee.settleBillId = '对账单号'
t.baPayableSettleBillDifferenceFee.feeTypeId = '费用项'
t.baPayableSettleBillDifferenceFee.currency = '币种'
t.baPayableSettleBillDifferenceFee.differenceSum = '差异金额'
t.baPayableSettleBillDifferenceFee.billingDate = '对账时间'
t.baPayableSettleBillDifferenceFee.remark = '备注'

/* 应收子单费用明细 BaSubReceivableFee */
t.baSubReceivableFee = {}
t.baSubReceivableFee.id = 'ID'
t.baSubReceivableFee.receivableBizOrderId = '费用主单号'
t.baSubReceivableFee.subReceivableBizOrderId = '费用子单号'
t.baSubReceivableFee.receivable = '应收/实收:'
t.baSubReceivableFee.createType = '费用产生方式'
t.baSubReceivableFee.receivableBillId = '对账单号'
t.baSubReceivableFee.subBusinessId = '业务子单号'
t.baSubReceivableFee.zone = '分区'
t.baSubReceivableFee.feeTypeId = '费用项'
t.baSubReceivableFee.currency = '币种'
t.baSubReceivableFee.calculateSum = '计费金额'
t.baSubReceivableFee.sum = '金额'
t.baSubReceivableFee.formula = '计算公式'
t.baSubReceivableFee.optDate = '业务发生时间'
t.baSubReceivableFee.billingDate = '计费时间'
t.baSubReceivableFee.auditor = '审核人'
t.baSubReceivableFee.auditDate = '审核时间'
t.baSubReceivableFee.remark = '备注'

/* 应收费用明细日志 BaReceivableFeeLog */
t.baReceivableFeeLog = {}
t.baReceivableFeeLog.id = 'ID'
t.baReceivableFeeLog.type = '类型'
t.baReceivableFeeLog.receivableBizOrderId = '费用单号'
t.baReceivableFeeLog.receivableBillId = '对账单号'
t.baReceivableFeeLog.businessId = '业务单号'
t.baReceivableFeeLog.createType = '费用产生方式'
t.baReceivableFeeLog.feeTypeId = '费用项'
t.baReceivableFeeLog.calculateSum = '计费金额'
t.baReceivableFeeLog.currency = '币种'
t.baReceivableFeeLog.sum = '金额'
t.baReceivableFeeLog.formula = '计算公式'
t.baReceivableFeeLog.optDate = '业务发生时间'
t.baReceivableFeeLog.billingDate = '计费时间'
t.baReceivableFeeLog.remark = '备注'

/* 应付实付费用子单 BaSubPayableBizOrder */
t.baSubPayableBizOrder = {}
t.baSubPayableBizOrder.id = 'ID'
t.baSubPayableBizOrder.payableBizOrderId = '应付实付费用单编码'
t.baSubPayableBizOrder.deliveryNo = '派送号'
t.baSubPayableBizOrder.packageNo = '箱号/FBA唛头'
t.baSubPayableBizOrder.currency = '币种'
t.baSubPayableBizOrder.sum = '金额'
t.baSubPayableBizOrder.optDate = '业务发生时间'
t.baSubPayableBizOrder.billingDate = '计费时间'
t.baSubPayableBizOrder.remark = '备注'
t.baSubPayableBizOrder.weightUnit = '订单实重单位'
t.baSubPayableBizOrder.weight = '订单实重'
t.baSubPayableBizOrder.balanceWeightUnit = '结算重单位'
t.baSubPayableBizOrder.balanceWeight = '结算重'
t.baSubPayableBizOrder.lengthUnit = '长度单位'
t.baSubPayableBizOrder.length = '长'
t.baSubPayableBizOrder.width = '宽'
t.baSubPayableBizOrder.height = '高'

/* 应收实收费用子单 BaSubReceivableBizOrder */
t.baSubReceivableBizOrder = {}
t.baSubReceivableBizOrder.id = 'ID'
t.baSubReceivableBizOrder.receivableBizOrderId = '应收实收费用单编码'
t.baSubReceivableBizOrder.deliveryNo = '派送号'
t.baSubReceivableBizOrder.packageNo = '箱号/FBA唛头'
t.baSubReceivableBizOrder.currency = '币种'
t.baSubReceivableBizOrder.sum = '金额'
t.baSubReceivableBizOrder.optDate = '业务发生时间'
t.baSubReceivableBizOrder.billingDate = '计费时间'
t.baSubReceivableBizOrder.remark = '备注'
t.baSubReceivableBizOrder.weightUnit = '子运单实重单位'
t.baSubReceivableBizOrder.weight = '子运单实重'
t.baSubReceivableBizOrder.balanceWeightUnit = '结算重单位'
t.baSubReceivableBizOrder.balanceWeight = '结算重'
t.baSubReceivableBizOrder.lengthUnit = '长度单位'
t.baSubReceivableBizOrder.length = '长'
t.baSubReceivableBizOrder.width = '宽'
t.baSubReceivableBizOrder.height = '高'

/* 应收计费子单 BaSubBillsReceivable */
t.baSubBillsReceivable = {}
t.baSubBillsReceivable.id = 'ID'
t.baSubBillsReceivable.billsId = '计费主单编码'
t.baSubBillsReceivable.settlementObjectId = '结算对象编码'
t.baSubBillsReceivable.settlementObjectName = '结算对象名称'
t.baSubBillsReceivable.orderType = '单据类型'
t.baSubBillsReceivable.subBusinessId = '业务子单号'
t.baSubBillsReceivable.deliveryNo = '派送号'
t.baSubBillsReceivable.packageNo = '箱号/FBA唛头'
t.baSubBillsReceivable.weight = '实重'
t.baSubBillsReceivable.length = '长'
t.baSubBillsReceivable.width = '宽'
t.baSubBillsReceivable.height = '高'
t.baSubBillsReceivable.volD = '体积(CM³)'
t.baSubBillsReceivable.girthD = '周长'
t.baSubBillsReceivable.volumeWeightD = '材积重'
t.baSubBillsReceivable.volumeCarryWeightD = '材积进位重'
t.baSubBillsReceivable.weightCarryWeightD = '实重进位重'
t.baSubBillsReceivable.minVolumeWeightD = '低消材积重'
t.baSubBillsReceivable.minWeightD = '低消实重'
t.baSubBillsReceivable.logisticsProductCode = '物流产品'
t.baSubBillsReceivable.balanceWeightUnit = '结算重单位'
t.baSubBillsReceivable.balanceWeight = '结算重'
t.baSubBillsReceivable.optDate = '业务发生时间'
t.baSubBillsReceivable.receivableRemark = '备注'

/* 应付计费子单 BaSubBillsPayable */
t.baSubBillsPayable = {}
t.baSubBillsPayable.id = 'ID'
t.baSubBillsPayable.billsId = '计费主单编码'
t.baSubBillsPayable.settlementObjectId = '结算对象编码'
t.baSubBillsPayable.settlementObjectName = '结算对象名称'
t.baSubBillsPayable.orderType = '单据类型'
t.baSubBillsPayable.subBusinessId = '业务子单号'
t.baSubBillsPayable.inSubBusinessId = '入库业务子单号'
t.baSubBillsPayable.deliveryNo = '派送号'
t.baSubBillsPayable.packageNo = '箱号/FBA唛头'
t.baSubBillsPayable.weight = '重量'
t.baSubBillsPayable.length = '长'
t.baSubBillsPayable.width = '宽'
t.baSubBillsPayable.height = '高'
t.baSubBillsPayable.logisticsChannelCode = '物流渠道'
t.baSubBillsPayable.balanceWeightUnit = '结算重单位'
t.baSubBillsPayable.balanceWeight = '结算重'
t.baSubBillsPayable.optDate = '业务发生时间'
t.baSubBillsPayable.receivableRemark = '备注'

/* 提成对账单 BaCommissionSettleBill */
t.baCommissionSettleBill = {}
t.baCommissionSettleBill.id = '提成单号'
t.baCommissionSettleBill.settlementObjectType = '结算对象类型'
t.baCommissionSettleBill.settlementObjectId = '结算对象编码'
t.baCommissionSettleBill.settlementObjectName = '结算对象名称'
t.baCommissionSettleBill.salesManName = '业务员'
t.baCommissionSettleBill.totalSum = '总金额'
t.baCommissionSettleBill.billDeadline = '帐期结束日期'
t.baCommissionSettleBill.accountingPeriod = '归属月份'
t.baCommissionSettleBill.fileUrl = '导出文件链接'
t.baCommissionSettleBill.remark = '备注'
t.baCommissionSettleBill.auditor = '审核人'
t.baCommissionSettleBill.auditDate = '审核时间'
t.baCommissionSettleBill.closed = '关账人'
t.baCommissionSettleBill.closeDate = '关账时间'

/* 提成费用单 BaCommissionBizOrder */
t.baCommissionBizOrder = {}
t.baCommissionBizOrder.id = 'ID'
t.baCommissionBizOrder.sourceOrderId = '原始计费单号(应收计费单)'
t.baCommissionBizOrder.receivable = '收付类型'
t.baCommissionBizOrder.settlementObjectType = '结算对象类型'
t.baCommissionBizOrder.settlementObjectId = '结算对象编码'
t.baCommissionBizOrder.settlementObjectName = '结算对象名称'
t.baCommissionBizOrder.businessId = '业务单号'
t.baCommissionBizOrder.optDate = '业务发生时间'
t.baCommissionBizOrder.customerId = '客户编号'
t.baCommissionBizOrder.customerName = '结算对象名称'
t.baCommissionBizOrder.customerOrderNo = '客户单号'
t.baCommissionBizOrder.waybillNo = '运单号'
t.baCommissionBizOrder.deliveryNo = '派送单号'
t.baCommissionBizOrder.remark = '备注'
t.baCommissionBizOrder.logisticsProductCode = '运输方式'
t.baCommissionBizOrder.logisticsChannelCode = '渠道代码'
t.baCommissionBizOrder.planElementType = '计费元素类型'
t.baCommissionBizOrder.planElementValue = '计费元素值'
t.baCommissionBizOrder.currency = '币种'
t.baCommissionBizOrder.commissionSum = '提成金额'
t.baCommissionBizOrder.formula = '提成公式'
t.baCommissionBizOrder.receivableSum = '应收金额'
t.baCommissionBizOrder.billingDate = '计费时间'
t.baCommissionBizOrder.accountingPeriod = '会计期间'
t.baCommissionBizOrder.settleBillId = '对账单号'
t.baCommissionBizOrder.sourceSettleBillId = '原始对账单号(应付对账单)'
t.baCommissionBizOrder.auditor = '审核人'
t.baCommissionBizOrder.auditDate = '审核时间'

/* 提成费用明细 BaCommissionBizFee */
t.baCommissionBizFee = {}
t.baCommissionBizFee.id = 'ID'
t.baCommissionBizFee.commissionBizOrderId = '费用单号'
t.baCommissionBizFee.receivable = '应收/实收:'
t.baCommissionBizFee.createType = '费用产生方式'
t.baCommissionBizFee.settleBillId = '对账单号'
t.baCommissionBizFee.feeTypeId = '费用项'
t.baCommissionBizFee.currency = '币种'
t.baCommissionBizFee.receivableSum = '应收金额'
t.baCommissionBizFee.commissionSum = '提成金额'
t.baCommissionBizFee.formula = '提成公式'
t.baCommissionBizFee.billingDate = '计费时间'
t.baCommissionBizFee.auditor = '审核人'
t.baCommissionBizFee.auditDate = '审核时间'
t.baCommissionBizFee.remark = '备注'

/* 应付子单费用明细 BaSubPayableFee */
t.baSubPayableFee = {}
t.baSubPayableFee.id = 'ID'
t.baSubPayableFee.payableBizOrderId = '费用主单号'
t.baSubPayableFee.subPayableBizOrderId = '费用子单号'
t.baSubPayableFee.payable = '应付/实付'
t.baSubPayableFee.createType = '费用产生方式'
t.baSubPayableFee.payableBillId = '对账单号'
t.baSubPayableFee.inSubBusinessId = '入库业务子单号'
t.baSubPayableFee.zone = '分区'
t.baSubPayableFee.feeTypeId = '费用项'
t.baSubPayableFee.currency = '币种'
t.baSubPayableFee.calculateSum = '计费金额'
t.baSubPayableFee.sum = '金额'
t.baSubPayableFee.formula = '计算公式'
t.baSubPayableFee.optDate = '业务发生时间'
t.baSubPayableFee.billingDate = '计费时间'
t.baSubPayableFee.auditor = '审核人'
t.baSubPayableFee.auditDate = '审核时间'
t.baSubPayableFee.remark = '备注'

/* 应付费用明细日志 BaPayableFeeLog */
t.baPayableFeeLog = {}
t.baPayableFeeLog.id = 'ID'
t.baPayableFeeLog.type = '类型'
t.baPayableFeeLog.payableBizOrderId = '费用单号'
t.baPayableFeeLog.payableBillId = '对账单号'
t.baPayableFeeLog.businessId = '业务单号'
t.baPayableFeeLog.createType = '费用产生方式'
t.baPayableFeeLog.feeTypeId = '费用项'
t.baPayableFeeLog.calculateSum = '计费金额'
t.baPayableFeeLog.currency = '币种'
t.baPayableFeeLog.sum = '金额'
t.baPayableFeeLog.formula = '计算公式'
t.baPayableFeeLog.optDate = '业务发生时间'
t.baPayableFeeLog.billingDate = '计费时间'
t.baPayableFeeLog.remark = '备注'

/* 应付对账同步加收比例 BaPayableSettleSyncAddition */
t.baPayableSettleSyncAddition = {}
t.baPayableSettleSyncAddition.id = 'ID'
t.baPayableSettleSyncAddition.settleBillId = '对账单ID'
t.baPayableSettleSyncAddition.feeTypeId = '费用项'
t.baPayableSettleSyncAddition.ratio = '加收比例'
t.baPayableSettleSyncAddition.fixValue = '加收金额'
t.baPayableSettleSyncAddition.status = '是否同步'
t.baPayableSettleSyncAddition.sum = '应付差异金额'
t.baPayableSettleSyncAddition.positiveSum = '金额大于0'
t.baPayableSettleSyncAddition.customer = '客户'

/* 干线分单 WsComHawb */
t.wsComHawb = {}
t.wsComHawb.id = 'ID'
t.wsComHawb.hawbNo = '分单号'
t.wsComHawb.mawbNo = '提单号'
t.wsComHawb.mawbId = '提单'
t.wsComHawb.remark = '备注'
t.wsComHawb.weight = '重量'
t.wsComHawb.vol = '体积'
t.wsComHawb.totalBagNum = '容器数'
t.wsComHawb.packageQty = '件数'
t.wsComHawb.pushStatus = '打板状态'
t.wsComHawb.confirmTime = '封单/解封时间'
t.wsComHawb.confirmUser = '封单/解封人'
t.colsewsComAwb = '封单'
t.unblockComAwb = '解封'
t.staBatch = '打板批次'
t.staContainInfo = '打板信息'
t.colsewsComHawbTips = '封单后不能再添加容器、运单和删除分单，请谨慎封单'
t.colsewsComMawbTips = '封单后不能再添加容器、运单、分单和删除主单，请谨慎封单'

/* 干线主单分单关系 WsComMawbHawb */
t.wsComMawbHawb = {}
t.wsComMawbHawb.id = 'ID'
t.wsComMawbHawb.hawbId = '分提单ID'
t.wsComMawbHawb.hawbNo = '分提单号'
t.wsComMawbHawb.mawbId = '提单ID'
t.wsComMawbHawb.mawbNo = '主单号'
t.wsComMawbHawb.notAdded = '未添加分单'
t.wsComMawbHawb.added = '已添加分单'

/* 作业成本计费单 BaBillsOperatingCost */
t.baBillsOperatingCost = {}
t.baBillsOperatingCost.id = 'ID'
t.baBillsOperatingCost.orderType = '作业单类型'
t.baBillsOperatingCost.operatingNo = '作业单号'
t.baBillsOperatingCost.weightUnit = '重量单位'
t.baBillsOperatingCost.weight = '重量'
t.baBillsOperatingCost.vol = '体积'
t.baBillsOperatingCost.packageQty = '包裹件数'
t.baBillsOperatingCost.orderQty = '作业单数'
t.baBillsOperatingCost.sum = '金额'
t.baBillsOperatingCost.currency = '币种'
t.baBillsOperatingCost.optDate = '业务发生时间'
t.baBillsOperatingCost.remark = '备注'
t.baBillsOperatingCost.importOrder = '导入包裹'

/* 作业成本作业单明细 BaOperatingCostOrder */
t.baOperatingCostOrder = {}
t.baOperatingCostOrder.id = 'ID'
t.baOperatingCostOrder.orderType = '作业单类型'
t.baOperatingCostOrder.orderId = '作业单ID'
t.baOperatingCostOrder.orderNo = '作业单单号'
t.baOperatingCostOrder.billsId = '计费单号'
t.baOperatingCostOrder.optDate = '作业时间'
t.baOperatingCostOrder.weightUnit = '重量单位'
t.baOperatingCostOrder.weight = '重量'
t.baOperatingCostOrder.vol = '体积'
t.baOperatingCostOrder.packageQty = '包裹件数'
t.baOperatingCostOrder.customerOrderNo = '客户订单号'
t.baOperatingCostOrder.logisticsChannelCode = '物流渠道'
t.baOperatingCostOrder.objectId = '客户对象编号'
t.baOperatingCostOrder.objectName = '客户对象名称'
t.baOperatingCostOrder.remark = '备注'

/* 作业成本费用单 BaOperatingCostBizOrder */
t.baOperatingCostBizOrder = {}
t.baOperatingCostBizOrder.id = 'ID'
t.baOperatingCostBizOrder.settlementObjectId = '结算对象编码'
t.baOperatingCostBizOrder.settlementObjectName = '结算对象名称'
t.baOperatingCostBizOrder.billsId = '计费单号'
t.baOperatingCostBizOrder.feeTypeId = '费用项'
t.baOperatingCostBizOrder.currency = '币种'
t.baOperatingCostBizOrder.sum = '金额'
t.baOperatingCostBizOrder.orderType = '作业单类型'
t.baOperatingCostBizOrder.operatingNo = '作业单号'
t.baOperatingCostBizOrder.weightUnit = '重量单位'
t.baOperatingCostBizOrder.weight = '重量'
t.baOperatingCostBizOrder.vol = '体积'
t.baOperatingCostBizOrder.packageQty = '包裹件数'
t.baOperatingCostBizOrder.billingDate = '计费时间'
t.baOperatingCostBizOrder.divideMethod = '分摊方式'
t.baOperatingCostBizOrder.divideStatus = '分摊状态'
t.baOperatingCostBizOrder.settleBillId = '对账单号'
t.baOperatingCostBizOrder.settleStatus = '对账状态'
t.baOperatingCostBizOrder.remark = '备注'

/* 作业成本费用明细表 BaOperatingCostFee */
t.baOperatingCostFee = {}
t.baOperatingCostFee.id = '成本费用ID'
t.baOperatingCostFee.operatingNo = '作业单号'
t.baOperatingCostFee.orderType = '作业单类型'
t.baOperatingCostFee.settlementObjectId = '结算对象'
t.baOperatingCostFee.settlementObjectName = '结算对象名称'
t.baOperatingCostFee.billsId = '计费单号'
t.baOperatingCostFee.feeTypeId = '费用项'
t.baOperatingCostFee.currency = '币种'
t.baOperatingCostFee.qty = '数量'
t.baOperatingCostFee.price = '单价'
t.baOperatingCostFee.sum = '金额'
t.baOperatingCostFee.packageQty = '件数'
t.baOperatingCostFee.billingDate = '计费时间'
t.baOperatingCostFee.divideMethod = '分摊方式'
t.baOperatingCostFee.divideStatus = '分摊状态'
t.baOperatingCostFee.volDiv = '材积除'
t.baOperatingCostFee.settleBillId = '对账单号'
t.baOperatingCostFee.settleStatus = '对账状态'
t.baOperatingCostFee.remark = '备注'
t.baOperatingCostFee.inputTips = '请把费用信息填写完整'
t.baOperatingCostFee.creator = '费用录入人'
t.baOperatingCostFee.auditor = '费用审核人'
t.baOperatingCostFee.isPlusInteger2 = '非0正整数'

/* 干线主单毛利统计报表 RpGrossStatisticsMawb */
t.rpGrossStatisticsMawb = {}
t.rpGrossStatisticsMawb.id = 'ID'
t.rpGrossStatisticsMawb.mawbId = '提单ID'
t.rpGrossStatisticsMawb.mawbNo = '提单号'
t.rpGrossStatisticsMawb.currency = '币种'
t.rpGrossStatisticsMawb.receivableSum = '应收金额'
t.rpGrossStatisticsMawb.payableSum = '应付金额'
t.rpGrossStatisticsMawb.costFee = '成本费用'
t.rpGrossStatisticsMawb.commissionFee = '提成费用'
t.rpGrossStatisticsMawb.grossprofitSum = '毛利金额'
t.rpGrossStatisticsMawb.nocommissionGrossprofitSum = '扣提成毛利金额'
t.rpGrossStatisticsMawb.optDate = '离港时间'
t.rpGrossStatisticsMawb.lastStatisticsDate = '上次统计时间'
t.rpGrossStatisticsMawb.restatistics = '重新统计'

/* 作业成本对账单 BaOperatingCostBill */
t.baOperatingCostBill = {}
t.baOperatingCostBill.id = '对账单号'
t.baOperatingCostBill.settlementObjectId = '结算对象编码'
t.baOperatingCostBill.settlementObjectName = '结算对象名称'
t.baOperatingCostBill.billDeadline = '账单截至日期'
t.baOperatingCostBill.totalSum = '应付金额'
t.baOperatingCostBill.currency = '应付币种'
t.baOperatingCostBill.invoiceNo = '发票号'
t.baOperatingCostBill.pushStatus = '账单推送状态'
t.baOperatingCostBill.remark = '备注'
t.baOperatingCostBill.closed = '关账人'
t.baOperatingCostBill.closeDate = '关账时间'
t.baOperatingCostBill.monthStatementDate = '账单所属月份'
t.baOperatingCostBill.fileUrl = '文件URL'

/* 作业成本结算单核销金额信息 BaOperatingCostSettleBillAmount */
t.baOperatingCostSettleBillAmount = {}
t.baOperatingCostSettleBillAmount.id = 'ID'
t.baOperatingCostSettleBillAmount.settleBillsId = '结算单号'
t.baOperatingCostSettleBillAmount.currency = '币种'
t.baOperatingCostSettleBillAmount.totalAmount = '总金额'
t.baOperatingCostSettleBillAmount.writeOffAmount = '已核销金额'
t.baOperatingCostSettleBillAmount.noWriteOffAmount = '未核销金额'

/* 作业成本结算单 BaOperatingCostSettleBill */
t.baOperatingCostSettleBill = {}
t.baOperatingCostSettleBill.id = 'ID'
t.baOperatingCostSettleBill.billId = '对账单号'
t.baOperatingCostSettleBill.invoiceNo = '发票号'
t.baOperatingCostSettleBill.settlementObjectId = '结算对象编码'
t.baOperatingCostSettleBill.settlementObjectName = '结算对象名称'
t.baOperatingCostSettleBill.billDeadline = '帐期结束日期'
t.baOperatingCostSettleBill.accountingPeriod = '会计期间'
t.baOperatingCostSettleBill.memo = '备注'

/* 作业成本结算单核销记录 BaOperatingCostSettleBillLog */
t.baOperatingCostSettleBillLog = {}
t.baOperatingCostSettleBillLog.id = 'ID'
t.baOperatingCostSettleBillLog.settleBillsId = '结算单号'
t.baOperatingCostSettleBillLog.writeOffOperator = '核销人'
t.baOperatingCostSettleBillLog.amount = '核销金额'
t.baOperatingCostSettleBillLog.creditAmount = '入账金额'
t.baOperatingCostSettleBillLog.currency = '币种'
t.baOperatingCostSettleBillLog.serialNumber = '支付流水号'
t.baOperatingCostSettleBillLog.receivablesDate = '收款时间'
t.baOperatingCostSettleBillLog.receivablesBankAccountId = '收款账户'
t.baOperatingCostSettleBillLog.payablesBankAccountId = '付款账户'
t.baOperatingCostSettleBillLog.remark = '备注'
t.baOperatingCostSettleBillLog.writeOffDate = '核销时间'

/* 尾程派送渠道重量修正 BdLogisticsChannelCorrectedWeight */
t.bdLogisticsChannelCorrectedWeight = {}
t.bdLogisticsChannelCorrectedWeight.id = 'ID'
t.bdLogisticsChannelCorrectedWeight.channelId = '渠道编码'
t.bdLogisticsChannelCorrectedWeight.matchMinWeight = '匹配起始重量'
t.bdLogisticsChannelCorrectedWeight.matchMaxWeight = '匹配截止重量'
t.bdLogisticsChannelCorrectedWeight.correctedMethod = '修正方式'
t.bdLogisticsChannelCorrectedWeight.correctedWeight = '修正重量'
t.bdLogisticsChannelCorrectedWeight.correctedLength = '修正长度'
t.bdLogisticsChannelCorrectedWeight.correctedWidth = '修正宽度'
t.bdLogisticsChannelCorrectedWeight.correctedHeight = '修正高度'

/* 尾程派送申报金额修正 BdLogisticsChannelCorrectedDeclareSum */
t.bdLogisticsChannelCorrectedDeclareSum = {}
t.bdLogisticsChannelCorrectedDeclareSum.id = 'ID'
t.bdLogisticsChannelCorrectedDeclareSum.channelId = '渠道编码'
t.bdLogisticsChannelCorrectedDeclareSum.matchMinSum = '匹配起始总金额'
t.bdLogisticsChannelCorrectedDeclareSum.matchMaxSum = '匹配截止总金额'
t.bdLogisticsChannelCorrectedDeclareSum.correctedSum = '修正总金额'
t.bdLogisticsChannelCorrectedDeclareSum.type = '修正方式'

/* 欠费单表 WsComRelease */
// t.wsComRelease = {}
// t.wsComRelease.id = 'id'
// t.wsComRelease.waybillNo = '运单号'
// t.wsComRelease.deliveryNo = '派送单号'
// t.wsComRelease.waybillId = '运单ID'
// t.wsComRelease.warehouseId = '仓库ID'
// t.wsComRelease.releaseDate = '放行时间'
// t.wsComRelease.releasePedestrians = '放行人'
// t.wsComRelease.release = '放行标记'
// t.wsComRelease.releaseType = '放行类型'
// t.wsComRelease.remarks = '放行备注'
// t.wsComRelease.deleted = '是否删除'

/* 货代公司-注册 SaasCompanyRegister */
t.saasCompanyRegister = {}
t.saasCompanyRegister.id = 'ID'
t.saasCompanyRegister.introducerComId = '介绍者公司'
t.saasCompanyRegister.comId = '货代公司ID'
t.saasCompanyRegister.code = '公司编码'
t.saasCompanyRegister.name = '公司名称'
t.saasCompanyRegister.contact = '联系人'
t.saasCompanyRegister.phone = '手机'
t.saasCompanyRegister.qq = 'QQ'
t.saasCompanyRegister.weixin = '微信'
t.saasCompanyRegister.email = '电子邮箱'
t.saasCompanyRegister.logisticsType = '物流类型'
t.saasCompanyRegister.salesman = '业务员'
t.saasCompanyRegister.waiter = '跟进人'
t.saasCompanyRegister.hangupMemo = '挂起备注'
t.saasCompanyRegister.closeMemo = '关闭备注'
t.saasCompanyRegister.visitCode = '邀请码'
t.saasCompanyRegister.country = '国家'
t.saasCompanyRegister.province = '省'
t.saasCompanyRegister.city = '市'
t.saasCompanyRegister.district = '区'
t.saasCompanyRegister.street = '详细地址'
t.saasCompanyRegister.postcode = '邮编'
t.saasCompanyRegister.verificationCode = '验证码'
t.saasCompanyRegister.logisticsTypeList = '物流类型'

t.saasCompanyRegister.title = '公司注册'
t.saasCompanyRegister.illegalEmail = '请输入正确的邮箱格式'
t.saasCompanyRegister.emailPlaceHolder = '用于接收系统注册成功信息，请正确填写'
t.saasCompanyRegister.salesmanPlaceHolder = '向您推荐系统的业务员编号，可提高为您服务的质量'

/* 公司邀请注册 SaasCompanyVisit */
t.saasCompanyVisit = {}
t.saasCompanyVisit.title = '邀请有奖'
t.saasCompanyVisit.id = 'ID'
t.saasCompanyVisit.originalCompanyId = '发起公司,ID'
t.saasCompanyVisit.receiveCompanyId = '接收公司,ID'
t.saasCompanyVisit.visitCode = '邀请码'
t.saasCompanyVisit.receiveDate = '新公司接收时间'
t.saasCompanyVisit.rebateDate = '返利时间'

/* 货代公司-微信支付 SaasCompanyWxpay */
t.saasCompanyWxpay = {}
t.saasCompanyWxpay.id = 'ID'
t.saasCompanyWxpay.companyCode = '公司代码'
t.saasCompanyWxpay.companyName = '公司名称'
t.saasCompanyWxpay.rechargeCard = '充值卡ID'
t.saasCompanyWxpay.feeType = '标价币种'
t.saasCompanyWxpay.totalFee = '标价金额(分)'
t.saasCompanyWxpay.outTradeNo = '商户订单号'
t.saasCompanyWxpay.body = '商品描述'
t.saasCompanyWxpay.detail = '商品详情'
t.saasCompanyWxpay.goodsTag = '订单优惠标记'
t.saasCompanyWxpay.appid = '公众账号ID'
t.saasCompanyWxpay.mchId = '商户号'
t.saasCompanyWxpay.deviceInfo = '设备号'
t.saasCompanyWxpay.nonceStr = '随机字符串'
t.saasCompanyWxpay.sign = '签名'
t.saasCompanyWxpay.resultCode = '业务结果'
t.saasCompanyWxpay.errCode = '错误代码'
t.saasCompanyWxpay.errCodeDes = '错误代码描述'
t.saasCompanyWxpay.tradeState = '交易状态'
t.saasCompanyWxpay.tradeType = '交易类型'
t.saasCompanyWxpay.prepayId = '预支付交易会话标识,微信生成的预支付会话标识，用于后续接口调用中使用，该值有效期为2小时'
t.saasCompanyWxpay.codeUrl = '二维码链接,trade_type为NATIVE时有返回，用于生成二维码，展示给用户进行扫码支付'
t.saasCompanyWxpay.transactionId = '微信支付订单号'
t.saasCompanyWxpay.bankType = '银行类型'
t.saasCompanyWxpay.attach = '商家数据包'
t.saasCompanyWxpay.timeEnd = '支付完成时间'

/* 充值卡 SaasRechargeCard */
t.saasRechargeCard = {}
t.saasRechargeCard.id = 'ID'
t.saasRechargeCard.name = '标题'
t.saasRechargeCard.description = '描述'
t.saasRechargeCard.months = '月数'
t.saasRechargeCard.price = '价格'

t.diyLogin = {}
t.diyLogin.setting = '设置'
t.diyLogin.domainName = '专属域名'
t.diyLogin.settingPage = '设置登录页'
t.diyLogin.tipText = '上传图片大小不能超过 1MB!'
t.diyLogin.bgImg = '背景图片'
t.diyLogin.logo = 'logo'
t.diyLogin.icpName = '备案号'
t.diyLogin.icpNamePlaceholder = '请输入备案号'
t.diyLogin.icpLink = '备案链接'
t.diyLogin.icpLinkPlaceholder = '请输入备案链接'
t.diyLogin.bottomCont = '底部内容'
t.diyLogin.settingBottomCont = '设置页面底部内容'

/* 消息通知内容表 MsNotice */
t.msNotice = {}
t.msNotice.id = 'ID'
t.msNotice.title = '标题'
t.msNotice.type = '类型'
t.msNotice.receiverType = '通知接收者类型'
t.msNotice.property = '通知属性'
t.msNotice.isTop = '是否置顶'
t.msNotice.grade = '通知等级'
t.msNotice.customerArray = '指定客户'
t.msNotice.companyUserArray = '指定公司员工'
t.msNotice.publishDate = '发布时间'
t.msNotice.content = '内容'
t.msNotice.creatorName = '发布者'

/* 消息通知-接收者 MsNoticeReceiver */
t.msNoticeReceiver = {}
t.msNoticeReceiver.id = 'ID'
t.msNoticeReceiver.noticeId = '通知ID'
t.msNoticeReceiver.recevierId = '接收者ID，（公司员工账号ID或客户账号ID)'

/* 轨迹替换 TksTrackReplace */
t.tksTrackReplace = {}
t.tksTrackReplace.id = 'ID'
t.tksTrackReplace.logisticsChannelCode = '尾程派送渠道'
t.tksTrackReplace.matchType = '匹配类型'
t.tksTrackReplace.doType = '处理方式'
t.tksTrackReplace.sourceValue = '原轨迹描述或发生地'
t.tksTrackReplace.targetValue = '新轨迹描述或发生地'
t.tksTrackReplace.targetDesc = '新轨迹描述'
t.tksTrackReplace.targetTrackingCode = '新轨迹节点'
t.tksTrackReplace.targetTimeBeforeHoursTipPrefix = '新轨迹时间提前'
t.tksTrackReplace.targetTimeBeforeHoursTipSuffix = '小时'
t.tksTrackReplace.firstDeliveryTrack = '是第一条上网轨迹'

/* 订单导入模版 BdOrderImportTemplate */
t.bdOrderImportTemplate = {}
t.bdOrderImportTemplate.id = 'ID'
t.bdOrderImportTemplate.multiple = '支持多票'
t.bdOrderImportTemplate.name = '模块名称'
t.bdOrderImportTemplate.content = '模块内容'
t.bdOrderImportTemplate.description = '描述'
t.bdOrderImportTemplate.metaFileUrl = '设计文件'
t.bdOrderImportTemplate.demoFileUrl = '样例文件'
t.bdOrderImportTemplate.importMetaFile = '上传设计文件'
t.bdOrderImportTemplate.importDemoFile = '上传样例文件'
t.bdOrderImportTemplate.downloadMetaFile = '下载设计文件'
t.bdOrderImportTemplate.downloadDemoFile = '下载样例文件'

/* 订单API日志 CoOrderApiLog */
t.coOrderApiLog = {}
t.coOrderApiLog.title = 'API预报日志'
t.coOrderApiLog.id = 'id'
t.coOrderApiLog.customerId = '客户'
t.coOrderApiLog.type = '类型'
t.coOrderApiLog.customerOrderNo = '客户单证号'
t.coOrderApiLog.waybillNo = '运单号'
t.coOrderApiLog.deliveryNo = '派送单号'
t.coOrderApiLog.requestUrl = '请求url'
t.coOrderApiLog.requestHeader = '请求头'
t.coOrderApiLog.requestBody = '请求内容'
t.coOrderApiLog.responseBody = '响应内容'
t.coOrderApiLog.sign = '系统签名结果'
t.coOrderApiLog.signBody = '签名拼接字符串'
t.coOrderApiLog.elapse = '耗时(毫秒)'
t.coOrderApiLog.ip = 'IP'
t.coOrderApiLog.creatorName = '创建者'
t.coOrderApiLog.createDate = '创建时间'
t.coOrderApiLog.status = '状态'
t.coOrderApiLog.status0 = '成功'
t.coOrderApiLog.status1 = '失败'

/* 指定单价计费  */
t.baChargeByPriceReq = {}
t.baChargeByPriceReq.price = '单价'
t.baChargeByPriceReq.currency = '币种'
t.baChargeByPriceReq.balanceWeightD = '结算重'

/* 结算单发票信息  */
t.settleBillInvoiceInfo = {}
t.settleBillInvoiceInfo.invoiceNo = '发票号'
t.settleBillInvoiceInfo.sum = '金额'
t.settleBillInvoiceInfo.remark = '备注'

/* 客户api参数 BdCustomerApiParam */
t.bdCustomerApiParam = {}
t.bdCustomerApiParam.id = 'ID'
t.bdCustomerApiParam.customerId = '客户编号'
t.bdCustomerApiParam.paramKey = '参数名'
t.bdCustomerApiParam.paramValue = '参数值'
t.bdCustomerApiParam.remark = '备注'

/* 供应商api参数 BdProviderApiParam */
t.bdProviderApiParam = {}
t.bdProviderApiParam.id = 'ID'
t.bdProviderApiParam.providerId = '供应商ID'
t.bdProviderApiParam.paramKey = '参数名'
t.bdProviderApiParam.paramValue = '参数值'
t.bdProviderApiParam.remark = '备注'

/* 供应商api账号 BdProviderApiuser */
t.bdProviderApiuser = {}
t.bdProviderApiuser.id = 'ID'
t.bdProviderApiuser.providerId = '供应商ID'
t.bdProviderApiuser.isPushPath = '是否API推送轨迹'
t.bdProviderApiuser.apiTypeId = 'API接口类型编号'

/* 轨迹屏蔽 TksTrackingCodeForbidden */
t.tksTrackingCodeForbidden = {}
t.tksTrackingCodeForbidden.id = 'ID'
t.tksTrackingCodeForbidden.logisticsProductCode = '物流产品'
t.tksTrackingCodeForbidden.customerIdList = '指定客户'
t.tksTrackingCodeForbidden.customerTrackingCodeList = '仅客户不可见'
t.tksTrackingCodeForbidden.trackingCodeList = '任何人不可见'

/* 客户回邮地址 BdCustomerLogisticsReturnaddr */
t.bdCustomerLogisticsReturnaddr = {}
t.bdCustomerLogisticsReturnaddr.title = '客户回邮地址'
t.bdCustomerLogisticsReturnaddr.id = 'ID'
t.bdCustomerLogisticsReturnaddr.returnAddrId = '回邮地址'
t.bdCustomerLogisticsReturnaddr.logisticsId = '物流产品'
t.bdCustomerLogisticsReturnaddr.objectId = '客户编号'
t.bdCustomerLogisticsReturnaddr.objectName = '客户名称'
t.bdCustomerLogisticsReturnaddr.logisticsType = '物流类型'
t.bdCustomerLogisticsReturnaddr.addrType = '地址来源类型'

t.coSetDiyimportModule = {}
t.coSetDiyimportModule.id = 'ID'
t.coSetDiyimportModule.customerId = '客户'
t.coSetDiyimportModule.matchId = '字段映射模块编码'
t.coSetDiyimportModule.name = '名称'
t.coSetDiyimportModule.description = '描述'
t.coSetDiyimportModule.type = '类型'

t.coSetDiyimportModuleDetail = {}
t.coSetDiyimportModuleDetail.id = 'ID'
t.coSetDiyimportModuleDetail.customerId = '客户编号'
t.coSetDiyimportModuleDetail.matchId = '字段映射编码'
t.coSetDiyimportModuleDetail.matchDetailId = '字段映射明细编码'
t.coSetDiyimportModuleDetail.objectType = '对象类型'
t.coSetDiyimportModuleDetail.moduleId = '自定义模版编码'
t.coSetDiyimportModuleDetail.repeatableColumns = '可重复列'
t.coSetDiyimportModuleDetail.fieldValue = '字段值'
t.coSetDiyimportModuleDetail.fieldName = '字段名'
t.coSetDiyimportModuleDetail.digital = '是否数字'
t.coSetDiyimportModuleDetail.allowEmpty = '是否允许空'

/* 省州城市区域代码匹配 BdProvinceRegionCodeMatch */
t.bdProvinceRegionCodeMatch = {}
t.bdProvinceRegionCodeMatch.id = 'ID'
t.bdProvinceRegionCodeMatch.countryCode = '国家'
t.bdProvinceRegionCodeMatch.province = '省'
t.bdProvinceRegionCodeMatch.city = '城市'
t.bdProvinceRegionCodeMatch.district = '地区'
t.bdProvinceRegionCodeMatch.provinceCode = '省代码'
t.bdProvinceRegionCodeMatch.cityCode = '城市代码'
t.bdProvinceRegionCodeMatch.districtCode = '地区代码'

/* 毛利校验 */
t.inWaybillGrossValid = {}
t.inWaybillGrossValid.customerOrderNo = '客户单号'
t.inWaybillGrossValid.waybillNo = '运单号'
t.inWaybillGrossValid.deliveryNo = '派送单号'
t.inWaybillGrossValid.currency = '币种'
t.inWaybillGrossValid.receivableSum = '票应收'
t.inWaybillGrossValid.payableSum = '票应付'
t.inWaybillGrossValid.costFee = '票成本'
t.inWaybillGrossValid.grossSum = '票毛利'
t.inWaybillGrossValid.totalReceivableSum = '件总应收'
t.inWaybillGrossValid.totalPayableSum = '件总应付'
t.inWaybillGrossValid.totalCostFee = '件总成本'
t.inWaybillGrossValid.inBillWeight = '票重量'
t.inWaybillGrossValid.outBillTotalWeight = '件总重量'
t.inWaybillGrossValid.inTotalPackageQty = '票件数'
t.inWaybillGrossValid.outTotalPackageQty = '件总数'
t.inWaybillGrossValid.difference = '是否有误差'
t.inWaybillGrossValid.receivableDifference = '应收误差'
t.inWaybillGrossValid.payableSumDifference = '应付误差'
t.inWaybillGrossValid.costFeeDifference = '成本误差'
t.inWaybillGrossValid.grossDifference = '毛利误差'
t.inWaybillGrossValid.otherPackageReceivableSum = '其他提单件总应收'
t.inWaybillGrossValid.otherPackagePayableSum = '其他提单件总应付'
t.inWaybillGrossValid.otherPackageCostFee = '其他提单件总成本'

/* 毛利包裹 */
t.rpGrossStatisticsPackage = {}
t.rpGrossStatisticsPackage.packageNo = '箱号'
t.rpGrossStatisticsPackage.deliveryNo = '派送单号'
t.rpGrossStatisticsPackage.mawbNo = '干线主单号'
t.rpGrossStatisticsPackage.receivableSum = '应收金额'
t.rpGrossStatisticsPackage.payableSum = '应付金额'
t.rpGrossStatisticsPackage.costFee = '成本金额'

/* 轨迹签收时效统计 TksTrackingDeliveryAging */
t.tksTrackingDeliveryAging = {}
t.tksTrackingDeliveryAging.id = 'id'
t.tksTrackingDeliveryAging.destinationCountry = '目的地二字码'
t.tksTrackingDeliveryAging.customerName = '客户'
t.tksTrackingDeliveryAging.waybillNo = '运单号'
t.tksTrackingDeliveryAging.customerOrderNo = '客户单号'
t.tksTrackingDeliveryAging.postalTrackingNo = '邮政单号'
t.tksTrackingDeliveryAging.deliveryNo = '派送单号'
t.tksTrackingDeliveryAging.logisticsProductCode = '物流产品'
t.tksTrackingDeliveryAging.logisticsChannelCode = '物流渠道'
t.tksTrackingDeliveryAging.providerId = '供应商'
t.tksTrackingDeliveryAging.deliveryTime = '签收时间'
t.tksTrackingDeliveryAging.inDeliveryDays = '入库签收天数'
t.tksTrackingDeliveryAging.forecastDeliveryDays = '预报签收天数'
t.tksTrackingDeliveryAging.inTime = '入库时间'
t.tksTrackingDeliveryAging.forecastTime = '预报时间'
t.tksTrackingDeliveryAging.period = '时间段'
t.tksTrackingDeliveryAging.waybillQty = '订单数'
t.tksTrackingDeliveryAging.deliveryQty = '签收单数'
t.tksTrackingDeliveryAging.aging = '平均时效(天)'
t.tksTrackingDeliveryAging.deliveryRate = '签收率(%)'
t.tksTrackingDeliveryAging.dimension = '统计维度'
t.tksTrackingDeliveryAging.type = '时间段类型'
t.tksTrackingDeliveryAging.inPeriod = '入库时间段'
t.tksTrackingDeliveryAging.forecastPeriod = '预报时间段'

/* 轨迹节点时效统计 TksTrackingNodeAgingStatistics */
t.tksTrackingNodeAgingStatistics = {}
t.tksTrackingNodeAgingStatistics.id = 'id'
t.tksTrackingNodeAgingStatistics.destinationCountry = '目的地二字码'
t.tksTrackingNodeAgingStatistics.customerId = '客户编号'
t.tksTrackingNodeAgingStatistics.customerName = '客户名称'
t.tksTrackingNodeAgingStatistics.logisticsProductCode = '物流产品'
t.tksTrackingNodeAgingStatistics.logisticsChannelCode = '物流渠道'
t.tksTrackingNodeAgingStatistics.providerId = '供应商ID'
t.tksTrackingNodeAgingStatistics.waybillQty = '订单数'
t.tksTrackingNodeAgingStatistics.aging = '时效'
t.tksTrackingNodeAgingStatistics.agingByHour = '平均时效(小时)'
t.tksTrackingNodeAgingStatistics.trackingCode = '轨迹代码'
t.tksTrackingNodeAgingStatistics.startTrackingCode = '起始轨迹代码'
t.tksTrackingNodeAgingStatistics.endTrackingCode = '结束轨迹代码'
t.tksTrackingNodeAgingStatistics.inTime = '入库时间'
t.tksTrackingNodeAgingStatistics.forecastTime = '预报时间'
t.tksTrackingNodeAgingStatistics.inSeason = '入库季'
t.tksTrackingNodeAgingStatistics.inMonth = '入库月'
t.tksTrackingNodeAgingStatistics.inWeek = '入库周'
t.tksTrackingNodeAgingStatistics.inDay = '入库日'
t.tksTrackingNodeAgingStatistics.forecastSeason = '预报季'
t.tksTrackingNodeAgingStatistics.forecastMonth = '预报月'
t.tksTrackingNodeAgingStatistics.forecastWeek = '预报周'
t.tksTrackingNodeAgingStatistics.forecastDay = '预报日'
t.tksTrackingNodeAgingStatistics.period = '时间段'
t.tksTrackingNodeAgingStatistics.inPeriod = '入库时间段'
t.tksTrackingNodeAgingStatistics.forecastPeriod = '预报时间段'
t.tksTrackingNodeAgingStatistics.dimension = '统计维度'
t.tksTrackingNodeAgingStatistics.type = '时间段类型'

t.regexBuild = {}
t.regexBuild.regex = '正则表达式'
t.regexBuild.diyRegex = '自定义表达式'
t.regexBuild.no = '单号'
t.regexBuild.description = '正则描述'
t.regexBuild.buildRegex = '生成正则表达式'
t.regexBuild.gen = '生成'
t.regexBuild.select = '选择'

/* 回邮地址组 BdLogisticsReturnaddrGroup */
t.bdLogisticsReturnaddrGroup = {}
t.bdLogisticsReturnaddrGroup.title = '地址组'
t.bdLogisticsReturnaddrGroup.id = 'ID'
t.bdLogisticsReturnaddrGroup.name = '组名'
t.bdLogisticsReturnaddrGroup.remark = '备注'

/* 回邮地址组明细 BdLogisticsReturnaddrGroupItem */
t.bdLogisticsReturnaddrGroupItem = {}
t.bdLogisticsReturnaddrGroupItem.id = 'ID'
t.bdLogisticsReturnaddrGroupItem.returnAddrId = '回邮地址'
t.bdLogisticsReturnaddrGroupItem.groupId = '地址组ID'
t.bdLogisticsReturnaddrGroupItem.frequency = '连续使用次数'
t.bdLogisticsReturnaddrGroupItem.serial = '序号'

/* 轨迹默认值表 BdTrackDefaultValue */
t.bdTrackDefaultValue = {}
t.bdTrackDefaultValue.id = 'ID'
t.bdTrackDefaultValue.platformCode = '平台代码'
t.bdTrackDefaultValue.platformTrackingCode = '平台轨迹代码'
t.bdTrackDefaultValue.fieldKey = '字段属性'
t.bdTrackDefaultValue.fieldName = '字段属性名称'
t.bdTrackDefaultValue.fieldType = '字段类型'
t.bdTrackDefaultValue.defaultValue = '默认值'
t.bdTrackDefaultValue.cutLenght = '超长保留位数'
t.bdTrackDefaultValue.fillType = '填充方式'
t.bdTrackDefaultValue.defaultValueType = '默认值来源类型'
t.bdTrackDefaultValue.linkFieldName = ''

/* 操作员计件  */
t.wsComWaybillOperateCountDTO = {}
t.wsComWaybillOperateCountDTO.createDate = '操作时间'
t.wsComWaybillOperateCountDTO.creator = '操作员'
t.wsComWaybillOperateCountDTO.pickupQty = '揽收'
t.wsComWaybillOperateCountDTO.receiveQty = '仓库签收'
t.wsComWaybillOperateCountDTO.inQty = '入库'
t.wsComWaybillOperateCountDTO.printQty = '面单打印'
t.wsComWaybillOperateCountDTO.packQty = '装袋'
t.wsComWaybillOperateCountDTO.returnInQty = '退货入库'
t.wsComWaybillOperateCountDTO.returnOutQty = '退货出库'

/* 换邮政单表 WsComReplacePostalTrackingNo */
t.wsComReplacePostalTrackingNo = {}
t.wsComReplacePostalTrackingNo.id = 'ID'
t.wsComReplacePostalTrackingNo.batchNo = '批次号'
t.wsComReplacePostalTrackingNo.type = '1:代理换单'
t.wsComReplacePostalTrackingNo.waybillId = '运单ID'
t.wsComReplacePostalTrackingNo.orderId = '订单ID'
t.wsComReplacePostalTrackingNo.waybillNo = '运单号'
t.wsComReplacePostalTrackingNo.oldPostalTrackingNo = '原邮政单号'
t.wsComReplacePostalTrackingNo.newPostalTrackingNo = '新邮政单号'
t.wsComReplacePostalTrackingNo.auditor = '审核人'
t.wsComReplacePostalTrackingNo.auditDate = '审核时间'
t.wsComReplacePostalTrackingNo.audit = '审核'

/* 税金明细 PsExpressBaseQuotationTaxPrice */
t.psExpressBaseQuotationTaxPrice = {}
t.psExpressBaseQuotationTaxPrice.id = 'ID'
t.psExpressBaseQuotationTaxPrice.baseQuotationId = '基础报价'
t.psExpressBaseQuotationTaxPrice.zone = '分区'
t.psExpressBaseQuotationTaxPrice.beginDeclarePrice = '起始申报金额'
t.psExpressBaseQuotationTaxPrice.endDeclarePrice = '结束申报金额'
t.psExpressBaseQuotationTaxPrice.ratio = '税率'
t.psExpressBaseQuotationTaxPrice.minimumSum = '最低税费'
t.psExpressBaseQuotationTaxPrice.formula = '计算公式'

t.psExpressBaseQuotationTaxPrice.zoneRequired = '第{index}行,分区不能为空'
t.psExpressBaseQuotationTaxPrice.beginDeclarePriceRequired = '第{index}行,起始申报金额不能为空'
t.psExpressBaseQuotationTaxPrice.endDeclarePriceRequired = '第{index}行,结束申报金额不能为空'
t.psExpressBaseQuotationTaxPrice.ratioRequired = '第{index}行,税率不能为空'
t.psExpressBaseQuotationTaxPrice.minimumSumRequired = '第{index}行,最低税费不能为空'
t.psExpressBaseQuotationTaxPrice.beginDeclarePriceRegex = '第{index}行,起始申报金额必须大于0且小于9999999,最多{max}小数'
t.psExpressBaseQuotationTaxPrice.endDeclarePriceRegex = '第{index}行,结束申报金额必须大于0且小于9999999,最多{max}小数'
t.psExpressBaseQuotationTaxPrice.minimumSumRegex = '第{index}行,最低税费必须大于0且小于9999999,最多{max}小数'
t.psExpressBaseQuotationTaxPrice.ratioRegex = '第{index}行,税率必须大于0且小于9999999,最多{max}小数'

/* 换标打印 WsChangeLabelPrintOrder */
t.wsChangeLabelPrintOrder = {}
t.wsChangeLabelPrintOrder.id = 'ID'
t.wsChangeLabelPrintOrder.customerId = '客户'
t.wsChangeLabelPrintOrder.customerName = '客户'
t.wsChangeLabelPrintOrder.orderId = '订单ID'
t.wsChangeLabelPrintOrder.waybillId = '运单ID'
t.wsChangeLabelPrintOrder.customerOrderNo = '客户单号'
t.wsChangeLabelPrintOrder.deliveryNo = '派送单号'
t.wsChangeLabelPrintOrder.waybillNo = '运单号'
t.wsChangeLabelPrintOrder.logisticsProductCode = '运输方式'
t.wsChangeLabelPrintOrder.logisticsChannelCode = '物流渠道'
t.wsChangeLabelPrintOrder.pushOrderToProviderStatus = '同步供应商状态'
t.wsChangeLabelPrintOrder.pushOrderToProviderErrorMessage = '同步供应商错误信息'
t.wsChangeLabelPrintOrder.deliveryNoSource = '派送单号来源'
t.wsChangeLabelPrintOrder.hasLabel = '是否有面单'
t.wsChangeLabelPrintOrder.channelLabelUrl = '渠道面单地址'
t.wsChangeLabelPrintOrder.printTimes = '打印次数'
t.wsChangeLabelPrintOrder.successPrintTimes = '打印成功次数'
t.wsChangeLabelPrintOrder.failPrintTimes = '打印失败次数'
t.wsChangeLabelPrintOrder.warehouseId = '仓库ID'

export default t
