<template>
  <el-dialog :visible.sync="visible" title="导入文件" min-width="420" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true">
    <el-row style="margin-bottom: 20px;">
      <el-col :span="4" style="font-weight: bold;">面单匹配方式: </el-col>
      <el-col :span="16">
        <el-radio-group  v-model="matchMethod"  style="width: 100%">
          <el-radio v-for="item in matchMethodList" :label="item.value" :key="item.value" @change="matchMethodChangeHandle">{{item.name}}</el-radio>
        </el-radio-group>
      </el-col>
    </el-row>
    <el-upload
      drag
      :show-file-list="false"
      :data="param"
      :action="getUrl()"
      :before-upload="beforeUploadHandle"
      :on-success="successHandle"
      :headers="getToken"
      :multiple="false"
      accept=".pdf,.rar,.zip"
      style="padding-bottom: 10px;">
      <i class="el-icon-upload"></i>
      <!-- <el-button size="large" type="primary">选择文件上传</el-button> -->
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">
        {{ $t('upload.tip', { 'format': 'pdf、rar、zip' }) }}且不超过{{sizeLimit}}M
      </div>
    </el-upload>
    <div style="font-size: 13px; margin-top: 20px;float:left;">
      <div style="font-size: 14px; font-weight: bold; margin-top: 5px;">说明：</div>
      <div style="margin: 5px;"><span style="font-weight: bold;">1. 按文件名匹配：</span>一票一个PDF文件，PDF文件名必须是“派送单号”，支持一个PDF上传或者多个PDF可打包(.zip或者.rar)上传，例如：420268369200190387473200146598.pdf<br/></div>
      <div style="margin: 5px;"><span style="font-weight: bold;">2. 按面单内容匹配：</span>PDF文件名随便命名，支持一个PDF文件含多票上传或者多个PDF打包(.zip或者.rar)上传。注：该方式性能比较差，偶尔会出现超时现象</div>
    </div>
    <template slot="footer">
      <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
import { clearLoginInfo } from '@/utils'
let loading = ''
export default {
  props: {
    uploadFileUrl: '',
    excelSizeLimit: ''
  },
  data () {
    return {
      visible: false,
      uploadDisable: false, // 避免重复上传
      token: '',
      lang: '',
      fileUrl: '',
      // 添加参数
      param: {},
      sizeLimit: 2,
      matchMethod: parseInt(localStorage.getItem(this.$store.state.user.id + '-upload-label-math-method') || '0'),
      matchMethodList: [{ value: 0, name: '按文件名匹配' }, { value: 1, name: '按面单内容匹配' }]
    }
  },
  methods: {
    getUrl () {
      let matchByFileName = this.matchMethod === 0
      return `${this.$baseUrl}${this.uploadFileUrl}?matchByFileName=${matchByFileName}`
    },
    init (param) {
      this.token = Cookies.get('sys_token')
      this.visible = true
      this.lang = Cookies.get('language') || 'zh-CN'
      this.sizeLimit = this.excelSizeLimit
      if (param) {
        this.param = param
      } else {
        this.param = null
      }
    },
    beforeUploadHandle (file) {
      this.uploadDisable = true
      let regStr = '(.pdf)$|(.rar)$|(.zip)$'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { 'format': `pdf、rar、zip` }))
        return false
      }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        return false // 必须返回false
      }
      loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    },
    successHandle (res, file) {
      if (res !== '' && res.code !== 0) {
        this.message = res.msg && res.msg.replace(/<br>/g, '\r\n')
        if (this.message === '未授权') {
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        } else {
          this.$message.error({
            message: res.msg,
            duration: 5000,
            dangerouslyUseHTMLString: true
          })
        }
      } else {
        this.message = this.$t('prompt.success')
        this.$emit('successHandle', res)
      }
      loading.close()
      this.uploadDisable = false
      this.visible = false
    },
    cancelFn () {
      this.visible = false
    },
    matchMethodChangeHandle () {
      localStorage.setItem(this.$store.state.user.id + '-upload-label-math-method', this.matchMethod)
    }
  },
  computed: {
    getToken () {
      return {
        token: this.token,
        'Accept-Language': this.lang
      }
    }
  }
}
</script>

<style scoped>

</style>
