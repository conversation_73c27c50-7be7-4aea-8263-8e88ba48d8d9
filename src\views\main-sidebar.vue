<template>
  <div>
    <aside :class="['aui-sidebar', `aui-sidebar--${$store.state.sidebarLayoutSkin}`]">
      <div class="aui-navbar__header" id="mainSidebar">
        <h1 class="aui-navbar__brand" @click="$router.push({ name: 'home' })">
          <a class="aui-navbar__brand-lg" href="javascript:;">
            <label v-if="systemLogo === '' || systemLogo === null" class="logo_char">ITS</label>
<!--            <img  src="../assets/img/logo.jpg" :alt="$t('brand.lg')">-->
            <img v-if="systemLogo" :src="systemLogo" class="logo_round" fit="cover" lazy>
          </a>
          <a class="aui-navbar__brand-mini" href="javascript:;">
            <label class="logo_mini_char">ITS</label>
          </a>
        </h1>
      </div>
      <div class="aui-sidebar__inner">
        <div class="all_menu" @click="showMenu" v-show="false">
          <svg class="icon-svg aui-sidebar__menu-icon" aria-hidden="true">
            <use xlink:href="#icon-menu"></use>
          </svg>
          <span>所有菜单</span>
          <i class="el-icon el-icon-arrow-right"></i>
        </div>

        <el-input v-model="searchMenu" placeholder="点击搜索菜单..." prefix-icon="el-icon-search" size="small" class="custom-input" style="width: 300px;" clearable></el-input>

        <el-menu
          :default-active="$store.state.sidebarMenuActiveName"
          :collapse="$store.state.sidebarFold"
          :unique-opened="!searchMenu"
          :default-openeds="openeds"
          :collapseTransition="false"
          class="aui-sidebar__menu">
          <sub-menu v-for="menu in sidebarMenuListView" :key="menu.id" :menu="menu" />
        </el-menu>
      </div>

    </aside>
    <div :class="isShow ? 'show': ''" ref="sidebarList">
      <div class="sidebarList" >
        <el-button class="close" type="text" @click="showMenu"> <i class="el-icon-close"></i></el-button>
        <div class="sidebarListBox clearfix">
          <div class="sidebar_category_group" v-for="(itemArr,index) in sidebarMenuArr" :key="index">
            <div class="sidebar_category_group_item" v-for="(item,i) in itemArr" :key="i">
              <h5 id="category-0">{{item.name}}</h5>
              <ul>
                <li v-for="(child,idx) in item.children" :class="child.isMark?'on':''" :key="idx">
                  <a href="">{{child.name}}</a>
                  <el-button type="text" class="button" @click="markFn(child)"><i :class="child.isMark ? 'el-icon-star-on':'el-icon-star-off'"></i></el-button>
                </li>
                <li v-show="!item.children.length" :class="item.isMark?'on':''">
                  <a href="javascript:;">{{item.name}}</a>
                  <el-button type="text" class="button" @click="markFn(item)"><i :class="item.isMark ? 'el-icon-star-on':'el-icon-star-off'"></i></el-button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import SubMenu from './main-sidebar-sub-menu'
export default {
  data () {
    return {
      openeds: [],
      searchMenu: '',
      isShow: false,
      sidebarMenuList: [],
      sidebarMenuLen: '',
      sidebarMenuArr: [],
      systemLogo: ''
    }
  },
  components: {
    SubMenu
  },
  created () {
    this.getInfo()
    this.sidebarMenuList = this.$store.state.sidebarMenuList = window.SITE_CONFIG['menuList']
    let len = 0
    this.sidebarMenuList.forEach(item => {
      len = len + 1
      item.isMark = false
      if (item.children.length) {
        item.children.forEach(data => {
          data.isMark = false
        })
      }
    })

    let n = 2
    this.sidebarMenuLen = Math.ceil(len / n)
    for (let i = 0; i < this.sidebarMenuLen; i++) {
      let temp = this.sidebarMenuList.slice(i * n, i * n + n)
      this.sidebarMenuArr.push(JSON.parse(JSON.stringify(temp)))
    }
  },
  computed: {
    sidebarMenuListView () {
      this.setOpeneds([])
      if (!this.searchMenu) {
        return this.$store.state.sidebarMenuList
      }
      let cloneMenuList = cloneDeep(this.$store.state.sidebarMenuList)
      let list = this.filterMenuList(cloneMenuList, this.searchMenu)
      this.setOpeneds(list)
      console.log('this.opended', this.openeds)
      return list
    }
  },
  methods: {
    setOpeneds (list, init = true) {
      if (init) {
        this.openeds = []
      }
      if (!this.searchMenu) {
        return
      }
      list.forEach(item => {
        if (item.children && item.children.length > 0) {
          this.openeds.push(item.id)
          if (item.children && item.children.length > 0) {
            this.setOpeneds(item.children, false)
          }
        }
      })
    },
    filterMenuList (menuList, searchMenu) {
      return menuList.filter(item => {
        if (item.children && item.children.length > 0) {
          item.children = this.filterMenuList(item.children, searchMenu)
        }
        return item.name.toLowerCase().includes(this.searchMenu.toLowerCase()) || (item.children || []).length > 0
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/bd/syssetting/find`).then(({ data: res }) => {
        this.systemLogo = res.data.systemLogo
      }).catch(() => {})
    },
    showMenu () {
      this.isShow = !this.isShow
    },
    markFn (item) {
      item.isMark = !item.isMark
    }
  }
}
</script>
<style lang="scss">
  .all_menu{
    position: relative;
    height: 38px;
    line-height: 38px;
    padding-left: 20px;
    border-bottom: 1px solid #E4E7ED;
    cursor: pointer;
    &:hover{
      background: #fdf0e6;
    }
    & > span{
      font-size:13px;
    }
    .el-icon {
      position: absolute;
      top: 50%;
      right: 20px;
      margin-top: -7px;
      -webkit-transition: -webkit-transform .3s;
      transition: -webkit-transform .3s;
      transition: transform .3s;
      font-size: 13px;
    }
  }
  .show{
    .sidebarList{
      left: 230px;
    }
  }
  .sidebarList{
    position: absolute;
    top: 89px;
    bottom: 0;
    left: -960px;
    padding: 40px 30px 0;
    -webkit-box-shadow: 2px 2px 6px 0 rgba(0,0,0,.2);
    box-shadow: 2px 2px 6px 0 rgba(0,0,0,.2);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background-color: #f7f7f7;
    width: 960px;
    overflow: hidden;
    -webkit-transition: all .3s cubic-bezier(0,0,.2,1);
    transition: all .3s cubic-bezier(0,0,.2,1);
    z-index: 99;
    .close{
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 10px;
      cursor: pointer;
      font-size: 24px;
      color: rgba(0,0,0,.65);
    }
    .sidebar_category_group{
      float: left;
      margin-right: 10px;
      width: 230px;
      .sidebar_category_group_item{
        margin-bottom: 30px;
        h5 {
          margin: 0;
          padding: 0 10px;
          height: 40px;
          line-height: 40px;
          font-size: 13px;
          font-weight: 600;
          color: #000000;
        }
        ul{
          margin: 0;
          padding: 0;
          list-style: none;
        }
        li {
          position: relative;
          &:hover {
            background-color: rgba(0,0,0,.06);
            .button{
              display: inline-block;
            }
          }
          &.on{
            .button{
              display: inline-block;
            }
          }
           a {
              display: block;
              padding: 0 10px;
              height: 30px;
              line-height: 30px;
              font-size: 13px;
              text-decoration: none;
              color: #555;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
          }
          .button{
            display: none;
            position: absolute;
            top: 50%;
            right: 0;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 16px;
            color: #ff6a00;
            -webkit-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            transform: translateY(-50%);
          }
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
.logo_char {
  font-size: 87px;
  font-family: Slabo;
  font-weight: bolder;
  letter-spacing: 13px;
  color: white;
}
.logo_mini_char {
  font-family: Slabo;
  font-weight: bolder;
  letter-spacing: 5px;
  color: white;
}

.logo_round {
  border-radius:10px;
  border: 1px solid #1890ff;
  border-radius:10px;
  -webkit-border-radius:10px;
  -moz-border-radius:10px;
}
</style>
<style lang='scss' scoped>
/*用v-deep穿透改变第三方库的样式。且style标签上需有scoped作用域，标注为本vue页面生效*/
#mainSidebar{
  ::v-deep .aui-navbar__brand-lg, .aui-navbar__brand-mini {
    padding-top: 0px;
    padding-left: 0px;
  }
}
/deep/ .el-input__inner {
  background-color: #031324 !important; /* 设置背景色 */
  border-color: #031324 !important;
  color: #717171 !important;
}
/deep/ input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #575757;
}
</style>
