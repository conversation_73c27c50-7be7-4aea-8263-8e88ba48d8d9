<template>
  <div class="add-body panel_body">
    <div class="mod-co__inorder flex_wrap">
      <el-form ref="form" label-width="140px">
        <el-row :gutter="10">
          <el-col :sm="24" :md="8">
            <el-form-item :label="$t('bdLogisticsProduct.code')">
              <span v-text="dataForm.code"></span>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="8">
            <el-form-item :label="$t('bdLogisticsProduct.name')">
              <span v-text="dataForm.name"></span>
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="8">
            <el-form-item :label="$t('bdLogisticsProduct.businessType')">
              <span>{{ dataForm.businessType | formatterType(logisticsProductBusinessTypeList)}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :sm="24" :md="8">
            <el-form-item :label="$t('bdLogisticsProduct.logisticsProductType')">
              {{dataForm.logisticsProductType | formatterName(logisticsProductTypeList)}}
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="8">
            <el-form-item :label="$t('bdLogisticsProduct.logisticsType')">
              {{dataForm.logisticsType | formatterType(logisticsTypeList)}}
            </el-form-item>
          </el-col>
          <el-col :sm="24" :md="8">
            <el-form-item :label="$t('system.createDate')">
              <span>{{ dataForm.createDate | gtmToLtm }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-tabs v-model="activeName" type="border-card" class="no_shadow">
          <el-tab-pane label="产品规则" name="logisticsProductRule" >
            <logisticsProductChannelRule  ref="logisticsProductRule" @onLogisticsProductRuleData="logisticsProductRuleData" :propData="setLogisticsProductRuleData" ></logisticsProductChannelRule>
          </el-tab-pane>
          <el-tab-pane label="面单规则" name="labelRole" >
            <lableRule  ref="labelRule" @onLogisticsProductOtherData="labelRuleData" :propData="setLabelRuleData" ></lableRule>
          </el-tab-pane>
          <el-tab-pane label="运单号设置" name="waybillSource" >
            <waybillNoSource ref="waybillNoSource" @onWaybillNoSourceData="waybillNoSourceData" :propData="setWaybillNoSourceData"></waybillNoSource>
          </el-tab-pane>
        <el-tab-pane label="邮政单号设置" name="postalTrackingNoSource" >
          <postalTrackingNoSource ref="postalTrackingNoSource" @onTrackingNoSourceData="trackingNoSourceData" :propData="setTrackingNoSourceData"></postalTrackingNoSource>
        </el-tab-pane>
          <el-tab-pane label="订单校验" name="orderVerify" >
            <orderVerifyTemplatePublic ref="orderVerifyTemplatePublic" @onOrderVerifyTemplateData="orderVerifyTemplateData" :propData="setOrderVerifyTemplateData"></orderVerifyTemplatePublic>
          </el-tab-pane>
        <el-tab-pane label="回邮地址" name="returnAddress" >
          <returnMailingAddressPublic ref="returnMailingAddressPublic" @onReturnMailingAddress="returnMailingAddressData" :propData="setReturnMailingAddressData"></returnMailingAddressPublic>
        </el-tab-pane>
          <el-tab-pane label="分区" name="channelZone" >
            <zoneTemplatePublic ref="zoneTemplatePublic" @onZoneTemplateData="zoneTemplateData" :propData="setZoneTemplateData"></zoneTemplatePublic>
          </el-tab-pane>
          <el-tab-pane label="偏远分区" name="remoteZone" >
            <remoteZoneTemplatePublic ref="remoteZoneTemplatePublic" @onRemoteZoneTemplateData="remoteZoneTemplateData" :propData="setRemoteZoneTemplateData"></remoteZoneTemplatePublic>
          </el-tab-pane>
          <el-tab-pane label="计费设置" name="rateSetting" >
            <rateSetting ref="rateSetting" @onRateSettingData="rateSettingData"></rateSetting>
          </el-tab-pane>
          <el-tab-pane label="趣物流代码" name="qwlSetting" >
            <qwlSetting ref="qwlSetting" @onQwlSettingData="qwlSettingData"></qwlSetting>
<!--            <div v-if="!isSaas" style="font-size:14px;">-->
<!--              请登录到 <el-link href="http://api.goto56.com:8086/apic/admin" target="_blank" style="font-size: 14px;"> 趣物流 </el-link> 平台维护-->
<!--            </div>-->
          </el-tab-pane>
        </el-tabs>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
        <el-button @click="$closeFn" icon="el-icon-close">{{ $t('close') }}</el-button>
      </div>
    </div>
    </div>
</template>

<script>
import debounce from 'lodash/debounce'
import closeMixins from '@/mixins/closeMixins'
import mixinViewModule from '@/mixins/view-module'
// import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import api from '@/api'
import baseData from '@/api/baseData'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './logistics-channel-add-or-update'
import ViewDetail from './logistics-channel-view-detail'
import logisticsProductChannelRule from './logistics-product-channel-rule-add-or-update'
import lableRule from './lable-rule'
import waybillNoSource from './product_waybill-no-source-add-or-update.vue'
import postalTrackingNoSource from './tracking-no-source-add-or-update'
import orderVerifyTemplatePublic from './order-verification-template-public'
import zoneTemplatePublic from './zone-template-public'
import remoteZoneTemplatePublic from './remote-zone-template-public'
import rateSetting from './logistics-product-rate-setting'
import qwlSetting from './logistics-product-setting-qwl'
import returnMailingAddressPublic from '@/views/modules/bd/return-mailing-address-public.vue'
export default {
  mixins: [mixinViewModule, dictTypeMixins, closeMixins],
  data () {
    return {
      mixinViewModuleOptions: {
        activatedIsNeed: false
      },
      dataForm: {
        id: '',
        code: '',
        name: '',
        lableType: '',
        pushTrackProviderId: '',
        logisticsType: '',
        // businessType: '',
        quotationSource: '10',
        forecastPay: '0',
        sellAccount: '0',
        qwlReturnNoField: '',
        qwlCode: '',
        logisticsProductRule: {},
        logisticsProductOther: {}
      },
      isSaas: this.$store.state.user.isSaas,
      waybillNoSource: {},
      postalTrackingNoSource: {},
      orderVerifyTemplate: {},
      qwlSetting: {},
      zoneTemplate: {},
      remoteZoneTemplate: {},
      returnMailingAddress: {},
      logisticsChannelLinksNodeList: [],
      logisticsProductBusinessTypeList: [],
      logisticsProductTypeList: [],
      logisticsTypeList: [],
      waybillNoSourceList: [],
      specialItemList: [],
      countryCityList: [],
      activeName: 'logisticsProductRule'
    }
  },
  activated () {
    if (this.$route.params.refresh) {
      this.getInfo()
    }
    this.$router.push({ params: { refresh: false } })
  },
  created () {
    Promise.all([
      this.getDict(),
      this.getBaseData()
    ]).then(() => {
      // 如果菜单浮动位置 需要初始化
      this.$footerScroll()
    })
  },
  methods: {
    getInfo () {
      this.dataForm.id = this.$route.query.logisticsProductId
      if (this.dataForm.id) {
        this.getLogisticsProductInfo()
      }
      this.activeName = 'logisticsProductRule'
      this.$refs.logisticsProductRule.clearValidated()
    },
    clearValidate (formName) {
      this.$refs[formName].clearValidate()
    },
    returnMailingAddressData (data) {
      console.log('this.returnMailingAddress', this.returnMailingAddress)
      this.returnMailingAddress = data || {}
    },
    remoteZoneTemplateData (data) {
      this.remoteZoneTemplate = data
    },
    zoneTemplateData (data) {
      this.zoneTemplate = data
    },
    orderVerifyTemplateData (data) {
      this.orderVerifyTemplate = data
    },
    waybillNoSourceData (data) {
      this.waybillNoSource = data
    },
    trackingNoSourceData (data) {
      this.postalTrackingNoSource = data
    },
    labelRuleData (data) {
      this.dataForm.logisticsProductOther = data || {}
    },
    logisticsProductRuleData (data) {
      this.dataForm.logisticsProductRule = data
    },
    rateSettingData (data) {
      if (data) {
        this.dataForm.quotationSource = data.quotationSource
        this.dataForm.sellAccount = data.sellAccount
        this.dataForm.forecastPay = data.forecastPay
      }
    },
    qwlSettingData (data) {
      this.dataForm.qwlCode = data.qwlCode
    },
    getLogisticsProductInfo () {
      this.$http.get(`/bd/logisticsproduct/getAllById/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        // 初始化
        let rule = {
          id: '',
          chargeableWeightRule: 0,
          volDiv: '',
          bubbleRate: '',
          bubbleCountingFormula: '',
          returnAddrId: '',
          mismatchingBubbleCountingChargeableWeightRule: 10,
          carryFrontierD: '',
          bigCarryD: '',
          smallCarryD: '',
          multiWeightRule: 0,
          multiWeightCarry: '',
          overrunRule: '',
          constraintRule: '',
          creator: '',
          createDate: ''
        }
        // 初始化
        let lable = {
          id: '',
          lableType: '0',
          orderVerifyId: '',
          returnAddrId: '',
          zoneTemplateId: '',
          remoteZoneTemplateId: '',
          labelTemplateId: '',
          handoverListId: '',
          proformaInvoiceId: '',
          bagCardId: '',
          customsDeclarationDocId: '',
          creator: '',
          createDate: ''
        }
        // 开发人员请勿随意更改下列参数 例如各组件dataForm.id 必须赋值，因为组件会根据id判断是否清空表单
        // 初始化 rule
        this.$refs.logisticsProductRule.dataForm = this.dataForm.logisticsProductRule || rule
        // 初始化 other
        this.$refs.labelRule.dataForm = this.dataForm.logisticsProductOther || lable
        // this.$refs.logisticsProductOther.dataForm.id = this.dataForm.logisticsProductOther.id
        // 标签模板等设置
        this.$refs.labelRule.dataForm.lableType = this.dataForm.lableType || 0
        this.$refs.labelRule.isLogisticsProduct = true
        // 订单校验模板设置
        this.$refs.orderVerifyTemplatePublic.dataForm.id = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.id
        this.$refs.orderVerifyTemplatePublic.isLogisticsProduct = true
        this.$refs.orderVerifyTemplatePublic.dataForm.postcodeRegionConversion = !this.dataForm.logisticsProductOther ? 0 : this.dataForm.logisticsProductOther.postcodeRegionConversion
        this.$refs.orderVerifyTemplatePublic.dataForm.postcodeRegionCheck = !this.dataForm.logisticsProductOther ? 0 : this.dataForm.logisticsProductOther.postcodeRegionCheck
        this.$refs.orderVerifyTemplatePublic.dataForm.usStateShortCodeConversion = !this.dataForm.logisticsProductOther ? 0 : this.dataForm.logisticsProductOther.usStateShortCodeConversion
        this.$refs.orderVerifyTemplatePublic.dataForm.orderVerifyId = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.orderVerifyId
        // 分区模板设置
        this.$refs.zoneTemplatePublic.dataForm.id = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.id
        this.$refs.zoneTemplatePublic.dataForm.zoneTemplateId = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.zoneTemplateId
        // 偏远分区模板设置
        this.$refs.remoteZoneTemplatePublic.dataForm.id = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.id
        this.$refs.remoteZoneTemplatePublic.dataForm.remoteZoneTemplateId = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.remoteZoneTemplateId
        // 运单号设置
        this.$refs.waybillNoSource.dataForm.id = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.id
        this.$refs.postalTrackingNoSource.dataForm.id = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.id
        // 计费设置
        this.$refs.rateSetting.dataForm.id = this.dataForm.id
        this.$refs.rateSetting.dataForm.quotationSource = this.dataForm.quotationSource
        this.$refs.rateSetting.dataForm.forecastPay = this.dataForm.forecastPay
        this.$refs.rateSetting.dataForm.sellAccount = this.dataForm.sellAccount
        this.$refs.rateSetting.dataForm.businessType = this.dataForm.businessType
        let isWaybillNoSourceNull = this.dataForm.waybillNoSource === '' || this.dataForm.waybillNoSource === null || this.dataForm.waybillNoSource === undefined
        let isPostalTrackingNoAsWaybillNoNull = this.dataForm.postalTrackingNoAsWaybillNo === '' || this.dataForm.postalTrackingNoAsWaybillNo === null || this.dataForm.postalTrackingNoAsWaybillNo === undefined
        let isPostalTrackingNoAsDeliveryNoNull = this.dataForm.postalTrackingNoAsDeliveryNo === '' || this.dataForm.postalTrackingNoAsDeliveryNo === null || this.dataForm.postalTrackingNoAsDeliveryNo === undefined
        this.$refs.waybillNoSource.dataForm.waybillNoSource = !isWaybillNoSourceNull ? this.dataForm.waybillNoSource : 1 // 默认为号段组
        this.$refs.waybillNoSource.dataForm.postalTrackingNoAsWaybillNo = !isPostalTrackingNoAsWaybillNoNull ? this.dataForm.postalTrackingNoAsWaybillNo : 1
        this.$refs.waybillNoSource.dataForm.postalTrackingNoAsDeliveryNo = !isPostalTrackingNoAsDeliveryNoNull ? this.dataForm.postalTrackingNoAsDeliveryNo : 1
        this.$refs.waybillNoSource.dataForm.waybillNoGroupId = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.waybillNoGroupId
        this.$refs.waybillNoSource.dataForm.returnDeliveryNo = !this.dataForm.logisticsProductOther ? '' : this.dataForm.logisticsProductOther.returnDeliveryNo
        this.$refs.waybillNoSource.dataForm.apiTypeId = this.dataForm.apiTypeId
        this.$refs.waybillNoSource.dataForm.apiRelatedSettingsArray = this.dataForm.apiRelatedSettingsArray
        this.$refs.waybillNoSource.dataForm.apiDetailList = this.dataForm.apiDetailList
        this.$refs.waybillNoSource.dataForm.businessType = this.dataForm.businessType
        this.$refs.waybillNoSource.dataForm.logisticsType = this.dataForm.logisticsType
        this.$refs.waybillNoSource.dataForm.qwlReturnNoField = this.dataForm.qwlReturnNoField
        this.$refs.waybillNoSource.isLogisticsProduct = true
        let isPostalTrackingNoSourceNull = this.dataForm.postalTrackingNoSource === '' || this.dataForm.postalTrackingNoSource === null || this.dataForm.postalTrackingNoSource === undefined
        this.$refs.postalTrackingNoSource.dataForm.postalTrackingNoSource = !isPostalTrackingNoSourceNull ? this.dataForm.postalTrackingNoSource : 1 // 默认为自定义样式
        this.$refs.postalTrackingNoSource.dataForm.apiTypeId = this.dataForm.apiTypeId
        this.$refs.postalTrackingNoSource.dataForm.apiProductPushTrack = !!this.dataForm.pushTrackProviderId
        this.$refs.postalTrackingNoSource.dataForm.pushTrackProviderId = this.dataForm.pushTrackProviderId
        this.$refs.postalTrackingNoSource.dataForm.apiRelatedSettingsArray = this.dataForm.apiRelatedSettingsArray
        this.$refs.postalTrackingNoSource.dataForm.apiDetailList = this.dataForm.apiDetailList
        this.$refs.postalTrackingNoSource.dataForm.businessType = this.dataForm.businessType
        this.$refs.postalTrackingNoSource.dataForm.logisticsType = this.dataForm.logisticsType
        this.$refs.postalTrackingNoSource.isLogisticsProduct = true
        // 趣物流代码
        this.$refs.qwlSetting.dataForm.qwlCode = this.dataForm.qwlCode

        this.$nextTick(() => {
          this.$refs.logisticsProductRule.init()
          this.$refs.labelRule.init()
          this.$refs.waybillNoSource.init()
          this.$refs.postalTrackingNoSource.init()
          this.$refs.orderVerifyTemplatePublic.init()
          this.$refs.zoneTemplatePublic.init()
          this.$refs.remoteZoneTemplatePublic.init()
          this.$refs.rateSetting.init()
          this.$refs.qwlSetting.init()
          // 回邮地址
          let returnMailingAddressPublicData
          if (this.dataForm.logisticsProductOther) {
            returnMailingAddressPublicData = {
              'id': this.dataForm.logisticsProductOther.id,
              'returnAddrId': this.dataForm.logisticsProductOther.returnAddrId,
              'logisticsType': 11
            }
          } else {
            returnMailingAddressPublicData = {
              'id': this.dataForm.id,
              'returnAddrId': '',
              'logisticsType': 11
            }
          }
          this.$refs.returnMailingAddressPublic.init(returnMailingAddressPublicData)
        })
      }).catch((e) => {
        console.log(e)
      })
    },
    async getDict () {
      // 获取相关字典
      this.logisticsChannelLinksNodeList = await this.getDictTypeList('logisticsChannelLinksNode') // 物流链路节点
      this.logisticsProductBusinessTypeList = await this.getDictTypeList('logisticsProductBusinessType') // 业务类型
      // this.logisticsProductTypeList = await this.getDictTypeList('logisticsProductType') // 产品类型
      this.specialItemList = await this.getDictTypeList('specialItem') // 特殊物品类型
      this.waybillNoSourceList = await this.getDictTypeList('waybillNoSource') // 运单号来源
      this.logisticsTypeList = await this.getDictTypeList('orderLogisticsType') // 订单物流类型
    },
    async getBaseData () {
      // 国家城市信息
      this.countryCityList = await baseData(api.countryCityList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      this.logisticsProductTypeList = await baseData(api.channelTypeList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    statusName (val) {
      let name = ''
      this.logisticsChannelSettingList.forEach((item) => {
        if (item.dictValue === val) {
          name = item.dictName
        }
      })
      return name
    },
    isBadge (item) {
      let ret = true
      if (item.value === 70) {
        ret = false
        return ret
      }
      return ret
    },
    tabsClick (tab, event) {

    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      // 物流产品规则验证
      this.$refs['logisticsProductRule'].validated((valid, data) => {
        if (!valid) {
          this.$message({
            message: '物流产品规则的必填项未填写或填写错误,请填写',
            type: 'warning',
            duration: 3000
          })
          return false
        }

        this.dataForm.logisticsProductRule = data
        if (!this.$refs['returnMailingAddressPublic'].validated()) {
          this.$message({
            message: '回邮地址的必填项未填写或填写错误,请填写',
            type: 'warning',
            duration: 3000
          })
          return false
        }
        if (!this.$refs['labelRule'].validated() || !this.$refs['waybillNoSource'].validated() ||
          !this.$refs['orderVerifyTemplatePublic'].validated() || !this.$refs['zoneTemplatePublic'].validated() ||
          !this.$refs['remoteZoneTemplatePublic'].validated() || !this.$refs['rateSetting'].validated() ||
          !this.$refs['qwlSetting'].validated() || !this.$refs['postalTrackingNoSource'].validated()
        ) {
          return false
        }
        console.log('this.returnMailingAddress.returnAddrId: ' + this.returnMailingAddress.returnAddrId)
        this.dataForm.lableType = this.dataForm.logisticsProductOther.lableType
        this.dataForm.waybillNoSource = this.waybillNoSource.waybillNoSource
        this.dataForm.postalTrackingNoAsWaybillNo = this.waybillNoSource.postalTrackingNoAsWaybillNo
        this.dataForm.qwlReturnNoField = this.waybillNoSource.qwlReturnNoField
        this.dataForm.postalTrackingNoSource = this.postalTrackingNoSource.postalTrackingNoSource
        this.dataForm.logisticsProductOther.waybillNoGroupId = this.waybillNoSource.waybillNoGroupId
        this.dataForm.logisticsProductOther.returnDeliveryNo = this.waybillNoSource.returnDeliveryNo
        this.dataForm.logisticsProductOther.postcodeRegionCheck = this.orderVerifyTemplate.postcodeRegionCheck
        this.dataForm.logisticsProductOther.postcodeRegionConversion = this.orderVerifyTemplate.postcodeRegionConversion
        this.dataForm.logisticsProductOther.usStateShortCodeConversion = this.orderVerifyTemplate.usStateShortCodeConversion
        this.dataForm.logisticsProductOther.orderVerifyId = this.orderVerifyTemplate.orderVerifyId
        this.dataForm.logisticsProductOther.zoneTemplateId = this.zoneTemplate.zoneTemplateId
        this.dataForm.logisticsProductOther.remoteZoneTemplateId = this.remoteZoneTemplate.remoteZoneTemplateId
        this.dataForm.logisticsProductOther.returnAddrId = this.returnMailingAddress.returnAddrId
        this.dataForm.apiDetailList = this.postalTrackingNoSource.apiDetailList
        this.dataForm.apiTypeId = this.postalTrackingNoSource.apiTypeId
        this.dataForm.pushTrackProviderId = this.postalTrackingNoSource.pushTrackProviderId
        this.dataForm.apiRelatedSettingsArray = this.postalTrackingNoSource.apiRelatedSettingsArray

        this.$http['post']('/bd/logisticsproduct/setLogisticsProductInfo', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$closeFn()
              this.$router.push({ name: `bd-logistics-product` })
            }
          })
        })
      })
      // })
    }, 1000, { 'leading': true, 'trailing': false })
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    setLogisticsProductRuleData () {
      return this.dataForm.logisticsProductRule
    },
    setLabelRuleData () {
      return this.dataForm.logisticsProductOther || {}
    },
    setWaybillNoSourceData () {
      return this.waybillNoSource
    },
    setTrackingNoSourceData () {
      return this.postalTrackingNoSource
    },
    setOrderVerifyTemplateData () {
      return this.orderVerifyTemplate
    },
    setZoneTemplateData () {
      return this.zoneTemplate
    },
    setReturnMailingAddressData () {
      return this.returnMailingAddress || {}
    },
    setRemoteZoneTemplateData () {
      return this.remoteZoneTemplate
    }
  },
  components: {
    returnMailingAddressPublic,
    AddOrUpdate,
    ViewDetail,
    tableSet,
    logisticsProductChannelRule,
    lableRule,
    waybillNoSource,
    postalTrackingNoSource,
    orderVerifyTemplatePublic,
    zoneTemplatePublic,
    remoteZoneTemplatePublic,
    rateSetting,
    qwlSetting
  }
}
</script>
