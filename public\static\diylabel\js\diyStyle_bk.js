$( function() {
    var currentObj, skulistObj;
    var TYPE = ["地址单", "报关单", "配货单"];
    var OPTIONS = {
        "address": ["detail", "field-address", "border"],
        "character": ["title", "detail", "border"],
        //"character1": ["detail", "border"],
        "barcode": ["barcode", "border"],
        "line-x": ["line-x"],
        "line-y": ["line-y"],
        "customtext": ["text", "border"],
        "circletext": ["circletext"],
        "onlineimage": ["imgurl", "border"],
        "image": ["border"],
        "skulist": ["table", "field-sku"],
        "declarelist": ["table", "field-declare"]
    };
    var NORMS = [{
        "width": "80",
        "height": "90",
        "name": "热敏纸(80mm×90mm)"
    },{
        "width": "100",
        "height": "100",
        "name": "热敏纸(100mm×100mm)"
    },{
        "width": "105",
        "height": "210",
        "name": "热敏纸(105mm×210mm)"
    },{
        "width": "102",
        "height": "76",
        "name": "热敏纸(102mm×76mm)"
    },{
        "width": "110",
        "height": "85",
        "name": "热敏纸(110mm×85mm)"
    },{
        "width": "100",
        "height": "150",
        "name": "热敏纸(100mm×150mm)"
    },{
        "width": "150",
        "height": "100",
        "name": "热敏纸(150mm×100mm)"
    },{
        "width": "210",
        "height": "297",
        "name": "A4纸(210mm×297mm)"
    }];

    var font_size ={"72px":"100%",
                    "101px":"140%",
                    "86px":"120%",
                    "79px":"110%",
                    "68px":"95%",
                    "65px":"90%",
                    "61px":"85%",
                    "58px":"80%",
                    "54px":"75%",
                    "50px":"70%",
                    "47px":"65%",
                    "43px":"60%",
                    "40px":"55%",
                    "36px":"50%",
                    "32px":"45%",
                    "29px":"40%",
                    "25px":"35%",
                    "22px":"30%",
                    "18px":"25%",
                    "14px":"20%",
                    "12px":"10%"};
    function getFontSize(px){
        var size = "";
        if( px == null || px == "") return "50%";
        if( px!=null && px!="" ){
            size = font_size[px];
            if( size==null || size=="" ) size = "50%";
        }
        return size;
    }

    init();
    testFont();

    function init(){
        var html = $("#styleHtml").val();
        if(html){
            $( "#custom-palette").html(html);
        }
        setDefault();

        $( ".label-content" ).draggable();
        $( ".dragitem" ).draggable({
            helper: "clone",
            start: function(){
                $( "#custom-palette" ).addClass("active");
            },
            stop: function(){
                $( "#custom-palette" ).removeClass("active");
            }
        });
        $( "#custom-palette" ).droppable({
            greedy: true,
            drop: function(event, ui){
                createCloneitem(event, ui, this);
                senditemExtend();
            }
        });

        $(".unit-g").addClass("disn");
    }

    function setDefault(){
        var norms = getLocationSearch().norms,
            type = TYPE[getLocationSearch().type] || "地址单";

        var width = NORMS[norms] && NORMS[norms].width || 75,
            height = NORMS[norms] && NORMS[norms].height || 85,
            ml = - (width/2),
            name = NORMS[name] && NORMS[name].width || "热敏纸(150mm×100mm)";

        //alert("width = "+width + ",height = "+height+", margin-left ="+ml);
        //$(".label-content").css({
        //    "width": width + "mm",
        //    "height": height + "mm",
        //    "margin-left": ml + "mm"
        //}).fadeIn();

        changeFormSize();

        $("#type").html(type);
        $("#name").html(name);

        if(!!$(".custom-label #custom-palette").html()){
            $(".custom-label").find(".senditem").removeClass("ui-resizable ui-draggable active").find(".ui-resizable-handle").remove();
        }

        //if(type=='报关单'){
        //    $(".dragitem.declarelist").show();
        //    $(".dragitem.skulist").hide();
        //}else{
        //    $(".dragitem.declarelist").hide();
        //    $(".dragitem.skulist").show();
        //}

        $(".dragitem.declarelist").show();
        $(".dragitem.skulist").show();
        //changeFormType();
    }

    function getLocationSearch(){
        var url = window.location.search;
        var search = new Object();
        if (url.indexOf("?") != -1) {
            var str = url.substr(1), strs = str.split("&");
            for(var i = 0; i < strs.length; i ++) {
                search[strs[i].split("=")[0]]=(strs[i].split("=")[1]);
            }
        }
        return search;
    }

    function createCloneitem(event, ui, wrap){
        var correct = 5;//修正值
        var senditem = $(ui.draggable), htmlStr;

        var width = senditem.attr('data-default-width') || "auto",
            height = senditem.attr('data-default-height') || "auto",
            type = senditem.attr("data-type");

        if( $(".senditem.skulist").length && type=="skulist" ){
            $(".dragitem.skulist").draggable({ revert: true });
            return;
        }else if( $(".senditem.declarelist").length && type=="declarelist" ){
            $(".dragitem.declarelist").draggable({ revert: true });
            return;
        }

        if( senditem.attr("class").indexOf('dragitem') >= 0 ) {
            //create dom
            switch(type){
                case "character":
                    htmlStr = '<div>' + senditem.html().replace(/<i class="(.*)">(.*)<\/i>/gi, '') + '</div>';
                    break;
                //case "character1":
                //    htmlStr = '<div class="productType">' + senditem.find(".detail").html() + '</div>';
                //    break;
                case "address":
                    htmlStr = '<div>' + senditem.html().replace(/<strong class="title">.*<\/strong>/gi, '') + '</div>';
                    break;
                case "barcode":
                    htmlStr = '<div class="barcode">' + senditem.find(".detail").html() + '</div>';
                    break;
                case "line-x":
                    htmlStr = '<div class="line-x"></div>';
                    break;
                case "line-y":
                    htmlStr = '<div class="line-y"></div>';
                    break;
                case "customtext":
                    htmlStr = '<div><span class="detail">自定义文本内容</span></div>';
                    break;
                case "circletext":
                    htmlStr = '<div class="circletext"><span class="detail">A</span></div>';
                    break;
                case "onlineimage":
                    htmlStr = '<div class="onlineimage"><img class="placeholder" src="images/placeholder-img.png" width="100%" height="100%"><img class="online disn" src=""></div>';
                    break;
                case "image":
                    htmlStr = '<div class="imageitem">' + senditem.html().replace(/<strong class="title disn">.*<\/strong>/gi, '') + '</div>';
                    break;
                case "skulist":
                    htmlStr = '<div class="skulist">' + senditem.find(".detail").html() + '</div>';
                    break;
                case "declarelist":
                    htmlStr = '<div class="declarelist">' + senditem.find(".detail").html() + '</div>';
            }

            var htmlObj = $(htmlStr).appendTo($(wrap)).css({"width": width, "height":height});
            htmlObj.addClass("senditem");
            htmlObj.attr("data-type", type);
            htmlObj.attr("data-title", senditem.find(".title").text());

            if( type=="barcode" ){
                htmlObj.css({ "text-align": "center"});
            }
            if( type=="line-x" ){
                htmlObj.attr("data-max-width", htmlObj.width());
            }else{
                htmlObj.css("left", event.clientX - $(wrap).offset().left - htmlObj.width()/2 + correct);
            }
            if( type=="line-y" ){
                htmlObj.css("width", 0);
                htmlObj.attr("data-max-height", htmlObj.height());
            }else{
                htmlObj.css("top", event.clientY - $(wrap).offset().top - $(htmlObj).height()/2 + correct);
            }
            if( type=="skulist" ){
                $(".dragitem.skulist").addClass("hasSkulist");
                htmlObj.css({ "left":0, "right":0});
                htmlObj.attr("data-default-height", senditem.attr("data-default-height"));
                htmlObj.find(".skulist-table").css("width", Math.ceil(htmlObj.width()));
                htmlObj.find(".skulist-table").css({ "font-size": "12px"});
            }
            if( type=="declarelist" ){
                $(".dragitem.declarelist").addClass("hasSkulist");
                htmlObj.css({ "left":0, "right":0});
                htmlObj.attr("data-default-height", senditem.attr("data-default-height"));
                htmlObj.find(".declarelist-table").css("width", Math.ceil(htmlObj.width()));
                htmlObj.find(".declarelist-table").css({ "font-size": "10px" });
            }

            currentObj = htmlObj;

        }
    }

    function senditemExtend(){
        // senditem resizable &;& draggable
        if( currentObj.hasClass("barcode") ) {
            currentObj.resizable({
                containment: "#custom-palette",
                minWidth: 100,
                minHeight: 28
            });
        }else if( currentObj.hasClass("line-x") ){
        }else if( currentObj.hasClass("line-y") ){
        }else if( currentObj.hasClass("circletext") ){
            currentObj.resizable({
                aspectRatio: 1,
                containment: "#custom-palette",
                stop: function(event, ui) {
                    $(this).css("line-height", $(this).css("height"));
                }
            });
        }else if( currentObj.hasClass("onlineimage") ){
            currentObj.resizable({
                containment: "#custom-palette",
                resize: function(event, ui){
                    var width = $(this).width(),
                        height = $(this).height();

                    $(this).find("img.placeholder").css({
                        "width": Math.min(width, height),
                        "height": Math.min(width, height)
                    });
                }
            });
        }else if( currentObj.hasClass("imageitem") ){
            var width = currentObj.find("img").width(),
                height = currentObj.find("img").height();
            currentObj.resizable({
                //handles: "n, e, s, w, ne, se, sw, nw",
                aspectRatio: width/height,
                containment: "#custom-palette"
            });
        }else if( currentObj.hasClass("skulist") ){
            var tr = currentObj.find("tbody>tr");
            currentObj.resizable({
                handles: "s",
                grid: tr.height(),
                containment: "#custom-palette",
                resize: function(event, ui){
                    var length = currentObj.find(".skulist-table>tbody>tr").length;
                    if( currentObj.height() > currentObj.find(".skulist-table").height()) {
                        currentObj.find(".skulist-table>tbody>tr:first").clone().addClass("empty").appendTo(currentObj.find(".skulist-table>tbody")).find("td").html("");
                    }else{
                        length>1 && currentObj.find(".skulist-table>tbody>tr:last").remove();
                    }
                    currentObj.css("height", currentObj.find(".skulist-table").height());
                }
            });
            draggable(currentObj, {
                axis: "y",
                stop: function(){
                    if( parseFloat($(this).css("bottom"))<=0 ){
                        $(this).css("top", $(this).parent().height()-$(this).height());
                    }
                    $( "#custom-palette" ).removeClass("active");
                }
            });
            return;
        }else if( currentObj.hasClass("declarelist") ){
            var tr = currentObj.find("tbody>tr");
            currentObj.resizable({
                minWidth: 150,
                grid: tr.height(),
                containment: "#custom-palette",
                resize: function(event, ui){
                    var length = currentObj.find(".skulist-table>tbody>tr").length;
                    if( currentObj.height() > currentObj.find(".skulist-table").height() ) {
                        var tbodyTr = currentObj.find(".skulist-table>tbody>tr:first").clone();
                        replaceIdName(tbodyTr, length);
                        tbodyTr.appendTo(currentObj.find(".skulist-table>tbody")).find("hls").html("");
                        //currentObj.find(".skulist-table>tbody>tr:first").clone().addClass("empty").appendTo(currentObj.find(".skulist-table>tbody")).find("td").html("");
                    }else if( parseInt(currentObj.height()) == parseInt(currentObj.find(".skulist-table").height()) ){
                        return;
                    }else{
                        length>1 && currentObj.find(".skulist-table>tbody>tr:last").remove();
                    }
                    currentObj.css("height", currentObj.find(".skulist-table").height());
                }
            });
            draggable(currentObj, {
                start: function(){
                    if( parseInt($(this).css("width"))==parseInt($(this).parent().width()) ){
                        $(this).css("width", $(this).parent().width());
                    }
                },
                stop: function(){
                    if( parseFloat($(this).css("bottom"))<=0 ){
                        $(this).css("top", $(this).parent().height()-$(this).height());
                    }
                    if( ($(this).width() + parseInt($(this).css("left"))) >= $(this).parent().width() ){
                        $(this).css("left", $(this).parent().width()-$(this).width());
                    }
                    $( "#custom-palette" ).removeClass("active");
                }
            });
            return;
        }else{
            currentObj.resizable({
                containment: "#custom-palette"
            });
        }

        draggable(currentObj);

        function draggable(obj, datas){
            var options = {
                containment: "#custom-palette",
                start: function(){
                    $( "#custom-palette" ).addClass("active");
                    $(this).trigger("click");
                },
                stop: function(){
                    $( "#custom-palette" ).removeClass("active");
                }
            };
            if (datas){
                obj.draggable($.extend(options, datas));
            }else{
                obj.draggable(options);
            }
        }
    }

    function testFont(){
        if($("#font-download").css("font-family") == "\"Microsoft YaHei\""
            || $("#font-download").css("font-family") == "\'Microsoft YaHei\'"){
            $("#font-download").hide();
        }else {
            $("#font-download").show();
        }
        if($("#code-download").css("font-family") == "\"Code 128\""
            || $("#code-download").css("font-family") == "\'Code 128\'"){
            $("#code-download").hide();
        }else {
            $("#code-download").show();
        }

    }
    //替换name
    function replaceID(tbodyTr, oldId, newId){
        var oldIdStr = tbodyTr.attr("id");
        if (!(typeof(oldIdStr) == "undefined")) {
            if (oldIdStr.lastIndexOf (oldId) > 0) {
                var newIdStr = oldIdStr.replace(oldId, newId);
                tbodyTr.attr("id", newIdStr);
            }
        }
    }
    //替换name
    function replaceName(tbodyTr, oldName, newName){
        var oldNameStr = tbodyTr.attr("name");
        if (!(typeof(oldNameStr) == "undefined")){
            if(oldNameStr.indexOf(oldName)>0){
                var newNameStr = oldNameStr.replace(oldName, newName);
                tbodyTr.attr("name", newNameStr);
            }
        }
    }

    //递归替换Tr下的id和name
    function replaceIdName(tbodyTr, len){
        var oldId = "_0";
        var newId = "_"+len;
        var oldName = "[0]";
        var newName = "["+len+"]";
        replaceID(tbodyTr, oldId, newId);
        replaceName(tbodyTr, oldName, newName);
        var childrens = tbodyTr.children();
        if(childrens.length>0){
            for(var i = 0; i<childrens.length; i++){
                replaceIdName(childrens.eq(i), len);
            }
        }
    }

    $(".label-content").on("mouseenter", ".senditem", function() {
    	currentObj = $(this);
	    senditemExtend();
    });

    $(".form-size").on("change",function(){
        changeFormSize();
    });

    //$(".form-type").on("change",function(){
    //    changeFormType();
    //});

    $(".label-content").on("click", ".senditem", function(){
        currentObj = $(this);
        setOptions(currentObj);
	    senditemExtend();
        optionsWarningMsg(false, $(".label-set"));

        $(this).addClass("active").siblings().removeClass("active");
        $(".hotkey-panel").css("opacity", 1);
        $(".label-content").find(".ui-icon").css("opacity", 0).parents(".label-content").find(".senditem.active .ui-icon").css("opacity", 1);
        $(".label-set").addClass("opened");
        $(".label-set .panel-toolbar li, .label-set .panel-body").removeClass("appear active");
        $(".label-set .panel-title").html(currentObj.attr("data-title"));

        //匹配参数tab项
        var type = $(this).attr("data-type");
        for(var i in OPTIONS){
            if( i==type ){
                for(var j=0; j<OPTIONS[i].length; j++){
                    $(".label-set .panel-toolbar li." + OPTIONS[i][j]).addClass("appear");
                    $(".label-set .panel-toolbar li." + OPTIONS[i][0]).addClass("active");
                    $(".label-set .panel-body#option-" + OPTIONS[i][0]).addClass("active");
                }
            }
        }
        if( $(".label-set .panel-toolbar li.appear").length<=1 ) {
            $(".label-set .panel-toolbar li").removeClass("appear");
        }

        if( currentObj.hasClass("skulist") || currentObj.hasClass("declarelist") ){
            //skulistObj = $("#copy-custom-palette");
            $(".btn-copy").addClass("disabled").prop("disabled", true);
        }else{
            $(".btn-copy").removeClass("disabled").prop("disabled", false);
        }

    });

    $(document).on("mousedown", function(event){
        currentObj = $('.senditem.active');
        if( !$(event.target).parents('.label-set').length && !$(event.target).hasClass('label-set') && !$(event.target).parents('.senditem').length && !$(event.target).hasClass('senditem')){
            $(".label-set").removeClass("opened");
            $(".senditem").removeClass("active");
            $(".senditem .ui-icon").css("opacity", 0);
            $(".hotkey-panel").css("opacity", 0);
        }
    });

    $(".senditem.active").on("change",function(){
        currentObj = $(this);
        alert("12");
        $("#fontDownloads").html(currentObj.css("width"));
    });

    function setOptions(obj){
        var type = obj.attr("data-type"),
            title = obj.find(".title"),
            detail = obj.find(".detail"),
            codeNumber = obj.find(".codeNumber");
        //$("#option-detail .content").addClass("disn");
        //$("#option-detail .product-type").addClass("disn");
        $("#rflagDiv").addClass("disn");
        //$("#rotateDiv").addClass("disn");
        $("#countryDiv").addClass("disn");
        switch(type){
            case "character":
                $("#viewTitle").prop("checked", title.css("display")!="none");
                $("#titleName").val(title.text());
                $("#titleNowrap").prop("checked", title.css("display")=="block");
                $("#titleAlign").val(title.css("text-align")=="start" ? "left" : title.css("text-align"));
                $("#titlePaddingBottom").val(title.css("padding-bottom"));
                $("#titleFontFamily").val(title.css("font-family"));
                $("#titleFontSize").val(title.css("font-size"));
                $("#titleLineHeight").val(Math.round(parseInt(title.css("line-height")) / parseInt(title.css("font-size")) * 10) / 10);
                $("#titleFontWeight").prop("checked", (title.css("font-weight")==700)||(title.css("font-weight")=="bold"));
                if( title.css("display")=="inline" ){
                    $(".title-set .moreinfo").addClass("disn");
                    $(".title-set input, .title-set select").removeClass("disabled").attr("disabled", false);
                }else if( title.css("display")=="block" ) {
                    $(".title-set .moreinfo").removeClass("disn");
                    $(".title-set input, .title-set select").removeClass("disabled").attr("disabled", false);
                }else{
                    $(".title-set .moreinfo").addClass("disn");
                    $(".title-set input, .title-set select").addClass("disabled").attr("disabled", true);
                }

                var pickMemoList = currentObj.find(".pickMemoList");
                if(pickMemoList.length){
                    $("#showProductNameDiv").removeClass("disn");
                    var disn = pickMemoList.find(".disn");
                    if(disn.length){
                        $("#showProductName").prop("checked", false);
                    }else{
                        $("#showProductName").prop("checked", true);
                    }
                }else
                    $("#showProductNameDiv").addClass("disn");
                assignDetailOptions(obj);
                //var wnoRuleList = currentObj.attr("wnoRuleList");
                //if(undefined != wnoRuleList && null != wnoRuleList && "" != wnoRuleList)
                //    $("#wnoRuleList").val(wnoRuleList);
                //else
                //    $("#wnoRuleList").val("");
                assignBorderOptions(obj);
                break;
            //case "character1":
            //    $("#option-detail .content").removeClass("disn");
            //    $("#option-detail .product-type").removeClass("disn");
            //    var content = obj.find(".content");
            //    var html = content.text();
            //    $("#detailName").val(html);
            //    if( title.css("display")=="inline" ){
            //        $(".title-set .moreinfo").addClass("disn");
            //        $(".title-set input, .title-set select").removeClass("disabled").attr("disabled", false);
            //    }else if( title.css("display")=="block" ) {
            //        $(".title-set .moreinfo").removeClass("disn");
            //        $(".title-set input, .title-set select").removeClass("disabled").attr("disabled", false);
            //    }else{
            //        $(".title-set .moreinfo").addClass("disn");
            //        $(".title-set input, .title-set select").addClass("disabled").attr("disabled", true);
            //    }
            //    $("#detailAlign").val(obj.css("text-align")=="start" ? "left" : obj.css("text-align"));
            //    $("#detailFontFamily").val(obj.css("font-family"));
            //    $("#detaillFontSize").val(obj.css("font-size"));
            //    $("#detailLineHeight").val(Math.round(parseInt(obj.css("lineHeight")) / parseInt(obj.css("font-size")) * 10) / 10);
            //    $("#detailFontWeight").prop("checked", (obj.css("font-weight")==700)||(obj.css("font-weight")=="bold"));
            //    assignBorderOptions(obj);
            //    break;
            case "address":
                var displayArr = [];
                $("#option-field-address .multiple input").each(function(){
                    var display = detail.find("span."+$(this).val()).css("display");
                    displayArr.push(display);
                    $(this).prop("checked", display!="none");
                });
                for(var i=0; i<displayArr.length; i++){
                    if( displayArr[i]=="block" ){
                        $("#newline").prop("checked", true);
                    }else if( displayArr[i]=="inline" ){
                        $("#newline").prop("checked", false);
                    }else {
                        continue;
                    }
                }
                assignDetailOptions(obj);
                assignBorderOptions(obj);
                break;
            case "barcode":
                var barcode = obj.find(".default-barcode");
                var height = barcode.css("height");
                var width = barcode.css("font-size");
                var fontSize = barcode.css("font-size");
                if(fontSize)
                    $("#codeFontStretch").val(getFontSize(fontSize));
                if(undefined != height && null != height && "" != height)
                    $("#xBarcodeHeight").val(height);
                if(undefined != width && null != width && "" != width){
                    width = width.replace("px","");
                    $("#xBarcodeWidth").val(width);
                }

                $("#viewCodeNum").prop("checked", codeNumber.css("display")!="none");
                $("#codeNumAlign").val(codeNumber.css("text-align")=="start" ? "left" : codeNumber.css("text-align"));
                $("#codeNumFontSize").val(codeNumber.css("font-size"));
                $("#barcodeTxt").val(codeNumber.text());
                $("#codeNumFontWeight").prop("checked", (codeNumber.css("font-weight")==700)||(codeNumber.css("font-weight")=="bold"));
                assignBorderOptions(obj);
                $("#rflagDiv").removeClass("disn");
                $("#countryDiv").removeClass("disn");
                var rflag = currentObj.attr("rflag");
                var countryOld = currentObj.attr("country");
                var notCountryOld = currentObj.attr("notCountry");
                var rflagAndCountry = currentObj.attr("rflagAndCountry");
                if(undefined != rflag && null != rflag && "" != rflag){
                    $("#checkBoxRFlag").val(rflag);
                }else
                    $("#checkBoxRFlag").val(0);

                if(undefined != rflagAndCountry && null != rflagAndCountry && "" != rflagAndCountry){
                    $("#rflagAndCountry").val(rflagAndCountry);
                }else
                    $("#rflagAndCountry").val(0);

                if(undefined != countryOld && null != countryOld && "" != countryOld){
                    $("#checkBoxCountry").val(1);
                    $("#checkBoxNotCountry").val(0);
                    $("#rflagAndCountryDiv").removeClass("disn");
                    $("#notCountryDiv").removeClass("disn");
                    $("#countrys").removeClass("disn");
                    $("#countryList").removeClass("disn");
                    $("#countrys").val(countryOld);
                }else if(undefined != notCountryOld && null != notCountryOld && "" != notCountryOld){
                    $("#checkBoxNotCountry").val(1);
                    $("#checkBoxCountry").val(0);
                    $("#rflagAndCountryDiv").removeClass("disn");
                    $("#notCountryDiv").removeClass("disn");
                    $("#countrys").removeClass("disn");
                    $("#countryList").removeClass("disn");
                    $("#countrys").val(notCountryOld);
                }else{
                    $("#checkBoxCountry").val(0);
                    $("#rflagAndCountryDiv").addClass("disn");
                    $("#notCountryDiv").addClass("disn");
                    $("#countryList").addClass("disn");
                    $("#countrys").addClass("disn");
                    $("#countrys").val("");
                }

                break;
            case "line-x":
                $("#xLineWidth").val(parseInt(currentObj.width()));
                $("#xLineStyle").val(currentObj.css("border-top-style"));
                $("#xLineWeight").val(currentObj.css("border-top-width"));
                break;
            case "line-y":
                $("#yLineHeight").val(parseInt(currentObj.height()));
                $("#yLineStyle").val(currentObj.css("border-left-style"));
                $("#yLineWeight").val(currentObj.css("border-left-width"));
                break;
            case "customtext":
                $("#textDetail").val(detail.html().replace(/<br>/g, "\n").replace(/<i class=".*"><\/i>/, ""));
                $("#textAlign").val(obj.css("text-align")=="start" ? "left" : obj.css("text-align"));
                $("#textFontFamily").val(detail.css("font-family"));
                $("#textFontSize").val(detail.css("font-size"));
                $("#textLineHeight").val(Math.round(parseInt(detail.css("line-height")) / parseInt(detail.css("font-size")) * 10) / 10);
                $("#textFontWeight").prop("checked", (detail.css("font-weight")==700)||(detail.css("font-weight")=="bold"));

                if( !detail.find("i").length ){
                    $("#textCheckBox").prop("checked", false);
                    $("#checkBoxType").val("ico-checkbox-unchecked").parent().addClass("disn");
                    $("#product_checkbox").parent().addClass("disn");
                }else if( detail.find("i").length && detail.find("i").hasClass("ico-checkbox-unchecked") ) {
                    $("#textCheckBox").prop("checked", true);
                    $("#checkBoxType").val("ico-checkbox-unchecked").parent().removeClass("disn");
                    $("#product_checkbox").parent().removeClass("disn");
                }else if( detail.find("i").length && detail.find("i").hasClass("ico-checkbox") ) {
                    $("#textCheckBox").prop("checked", true);
                    $("#checkBoxType").val("ico-checkbox").parent().removeClass("disn");
                    $("#product_checkbox").parent().removeClass("disn");
                }else{
                    $("#textCheckBox").prop("checked", true);
                    $("#checkBoxType").val("ico-checkbox-remove").parent().removeClass("disn");
                    $("#product_checkbox").parent().removeClass("disn");
                }

                $("#productType").parent().addClass("disn");
                if(detail.find("i").length && detail.find("i").hasClass("OrderType")){
                    $("#detailOrderType").prop("checked", true);
                    $("#detailOrderType").trigger("change");
                }else{
                    $("#detailOrderType").prop("checked", false);
                }
                assignBorderOptions(obj);
                $("#rflagDiv").removeClass("disn");
                $("#countryDiv").removeClass("disn");
                var rflag = currentObj.attr("rflag");
                var countryOld = currentObj.attr("country");
                var notCountryOld = currentObj.attr("notCountry");
                var rflagAndCountry = currentObj.attr("rflagAndCountry");
                if(undefined != rflag && null != rflag && "" != rflag){
                    $("#checkBoxRFlag").val(rflag);
                }else
                    $("#checkBoxRFlag").val(0);
                if(undefined != rflagAndCountry && null != rflagAndCountry && "" != rflagAndCountry){
                    $("#rflagAndCountry").val(rflagAndCountry);
                }else
                    $("#rflagAndCountry").val(0);
                if(undefined != countryOld && null != countryOld && "" != countryOld){
                    $("#checkBoxCountry").val(1);
                    $("#checkBoxNotCountry").val(0);
                    $("#rflagAndCountryDiv").removeClass("disn");
                    $("#notCountryDiv").removeClass("disn");
                    $("#countrys").removeClass("disn");
                    $("#countryList").removeClass("disn");
                    $("#countrys").val(countryOld);
                }else if(undefined != notCountryOld && null != notCountryOld && "" != notCountryOld){
                    $("#checkBoxNotCountry").val(1);
                    $("#checkBoxCountry").val(0);
                    $("#rflagAndCountryDiv").removeClass("disn");
                    $("#notCountryDiv").removeClass("disn");
                    $("#countrys").removeClass("disn");
                    $("#countryList").removeClass("disn");
                    $("#countrys").val(notCountryOld);
                }else{
                    $("#checkBoxCountry").val(0);
                    $("#rflagAndCountryDiv").addClass("disn");
                    $("#notCountryDiv").addClass("disn");
                    $("#countryList").addClass("disn");
                    $("#countrys").addClass("disn");
                    $("#countrys").val("");
                }
                break;
            case "circletext":
                $("#circleBorderWidth").val(obj.css("border-top-width"));
                $("#circleText").val(detail.text());
                $("#circleFontFamily").val(detail.css("font-family"));
                $("#circleFontSize").val(detail.css("font-size"));
                $("#circleFontWeight").prop("checked", (detail.css("font-weight")==700)||(detail.css("font-weight")=="bold"));
                break;
            case "onlineimage":
                $("#imageUrl").val(obj.find("img.online").attr("src"));
                assignBorderOptions(obj);
                break;
            case "image":
                assignBorderOptions(obj);
                //$("#rotateDiv").removeClass("disn");
                $("#rflagDiv").removeClass("disn");
                $("#countryDiv").removeClass("disn");
                var rflag = currentObj.attr("rflag");
                var countryOld = currentObj.attr("country");
                var notCountryOld = currentObj.attr("notCountry");
                var rflagAndCountry = currentObj.attr("rflagAndCountry");
                if(undefined != rflag && null != rflag && "" != rflag){
                    $("#checkBoxRFlag").val(rflag);
                }else
                    $("#checkBoxRFlag").val(0);
                if(undefined != rflagAndCountry && null != rflagAndCountry && "" != rflagAndCountry){
                    $("#rflagAndCountry").val(rflagAndCountry);
                }else
                    $("#rflagAndCountry").val(0);
                if(undefined != countryOld && null != countryOld && "" != countryOld){
                    $("#checkBoxCountry").val(1);
                    $("#checkBoxNotCountry").val(0);
                    $("#rflagAndCountryDiv").removeClass("disn");
                    $("#notCountryDiv").removeClass("disn");
                    $("#countrys").removeClass("disn");
                    $("#countryList").removeClass("disn");
                    $("#countrys").val(countryOld);
                }else if(undefined != notCountryOld && null != notCountryOld && "" != notCountryOld){
                    $("#checkBoxNotCountry").val(1);
                    $("#checkBoxCountry").val(0);
                    $("#rflagAndCountryDiv").removeClass("disn");
                    $("#notCountryDiv").removeClass("disn");
                    $("#countrys").removeClass("disn");
                    $("#countryList").removeClass("disn");
                    $("#countrys").val(notCountryOld);
                }else{
                    $("#checkBoxCountry").val(0);
                    $("#rflagAndCountryDiv").addClass("disn");
                    $("#notCountryDiv").addClass("disn");
                    $("#countryList").addClass("disn");
                    $("#countrys").addClass("disn");
                    $("#countrys").val("");
                }
                break;
            case "skulist":
                var thead = obj.find(".skulist-table thead"),
                    tbody = obj.find(".skulist-table tbody"),
                    tfoot = obj.find(".skulist-table tfoot");
                $("#viewTdBorder").prop("checked", thead.find("th").css("border-top-style")!="none");
                $("#viewThead").prop("checked", thead.css("display")!="none");
                $("#theadFontFamily").val(thead.css("font-family"));
                $("#theadFontSize").val(thead.css("font-size"));
                $("#tbodyFontFamily").val(tbody.css("font-family"));
                $("#tbodyFontSize").val(tbody.css("font-size"));
                $("#viewTfoot").prop("checked", tfoot.css("display")!="none");
                $("#tfootFontFamily").val(tfoot.css("font-family"));
                $("#tfootFontSize").val(tfoot.css("font-size"));
                $("#option-field-sku input[type='checkbox']").each(function(){
                    var display = thead.find("th."+$(this).val()).css("display");
                    $(this).prop("checked", display!="none");
                });
                $("#option-field-sku input[type='text']").each(function(){
                    var value = $(this).parents(".form-group").find("input[type='checkbox']").val();
                    $(this).val(thead.find("th."+value).text());
                });
                break;
            case "declarelist":
                var thead = obj.find(".skulist-table thead"),
                    tbody = obj.find(".skulist-table tbody"),
                    tfoot = obj.find(".skulist-table tfoot");
                $("#viewTdBorder").prop("checked", thead.find("th").css("border-top-style")!="none");
                $("#viewThead").prop("checked", thead.css("display")!="none");
                $("#theadFontFamily").val(thead.css("font-family"));
                $("#theadFontSize").val(thead.css("font-size"));
                $("#theadHeight").val(thead.css("height"));
                $("#tbodyFontFamily").val(tbody.css("font-family"));
                $("#tbodyFontSize").val(tbody.css("font-size"));
                $("#tbodyHeight").val(tbody.css("height"));
                $("#viewTfoot").prop("checked", tfoot.css("display")!="none");
                $("#tfootFontFamily").val(tfoot.css("font-family"));
                $("#tfootFontSize").val(tfoot.css("font-size"));
                $("#tfootHeight").val(tfoot.css("height"));
                $("#declareNameTitle").val(thead.find("th.name_declare span").html().replace(/<br>/g, '\n'));
                if(thead.find("th.no_head span").length)
                    $("#declareNoTitle").val(thead.find("th.no_head span").html().replace(/<br>/g, '\n'));
                if(thead.find("th.qty_declare span").length)
                    $("#declareQtyTitle").val(thead.find("th.qty_declare span").html().replace(/<br>/g, '\n'));
                if(thead.find("th.customsNo_declare span").length)
                    $("#declareCustomTitle").val(thead.find("th.customsNo_declare span").html().replace(/<br>/g, '\n'));
                $("#declareWeightTitle").val(thead.find("th.weight_declare span").html().replace(/<br>/g, '\n'));
                $("#declarePriceTitle").val(thead.find("th.price_declare span").html().replace(/<br>/g, '\n'));
                if(tfoot.find("th.total_qty_declare span").length)
                    $("#declareTotalQtyTitle").val(tfoot.find("th.total_qty_declare span").html().replace(/<br>/g, '\n'));
                $("#declareTotalWeightTitle").val(tfoot.find("th.weight_declare span").html().replace(/<br>/g, '\n'));
                $("#declareTotalPriceTitle").val(tfoot.find("th.price_declare span").html().replace(/<br>/g, '\n'));
                if(thead.find("th.origin_declare span").length)
                    $("#declareOriginTitle").val(thead.find("th.origin_declare span").html().replace(/<br>/g, '\n'));
                if(tbody.find("td.origin_body span hls").length)
                    $("#declareOrigin").val(tbody.find("td.origin_body span hls").html().replace(/<br>/g, '\n'));
                if(tfoot.find("th.origin_total span").length)
                    $("#declareOriginToTalTitle").val(tfoot.find("th.origin_total span").html().replace(/<br>/g, '\n'));
                if(tfoot.find("td.origin_total span").length)
                    $("#declareOriginToTal").val(tfoot.find("td.origin_total span").html().replace(/<br>/g, '\n'));
                if( tbody.find("span.unit-kg").hasClass("disn") ){
                    $("input[name='weight-unit'][value='g']").prop("checked", true);
                }else{
                    $("input[name='weight-unit'][value='kg']").prop("checked", true);
                }
                tbody.find("td.name_declare").each(function(){
                    if( !$(this).hasClass("disn") ){
                        var value = $(this).attr("class").split(' ')[1];
                        $("#declareType").val(value);
                        $("#declareName ." + value).removeClass("disn").siblings().addClass("disn");
                    }
                });
                $("#declareName .multiple input[type='checkbox']").each(function(){
                    var value = $(this).val(),
                        nodisplay = tbody.find("span." + value).hasClass("disn");
                    $(this).prop("checked", !nodisplay);
                });
                break;
        }
    }

    function assignDetailOptions(obj){
        var detail = obj.find(".detail");
        $("#detailAlign").val(obj.css("text-align")=="start" ? "left" : obj.css("text-align"));
        $("#detailFontFamily").val(detail.css("font-family"));
        $("#detaillFontSize").val(detail.css("font-size"));
        $("#detailLineHeight").val(Math.round(parseInt(detail.css("lineHeight")) / parseInt(detail.css("font-size")) * 10) / 10);
        $("#detailFontWeight").prop("checked", (detail.css("font-weight")==700)||(detail.css("font-weight")=="bold"));
    }

    function assignBorderOptions(obj){
        var bor_top = obj.css("border-top-width").split(" ")[0],
            bor_right = obj.css("border-right-width").split(" ")[0],
            bor_bottom = obj.css("border-bottom-width").split(" ")[0],
            bor_left = obj.css("border-left-width").split(" ")[0],
            p_top = obj.css("padding-top"),
            p_right = obj.css("padding-right"),
            p_bottom = obj.css("padding-bottom"),
            p_left = obj.css("padding-left");
        $("#borderTop").prop("checked", bor_top!="0px");
        $("#borderRight").prop("checked", bor_right!="0px");
        $("#borderBottom").prop("checked", bor_bottom!="0px");
        $("#borderLeft").prop("checked", bor_left!="0px");
        $("#borderTopWidth").val(bor_top);
        $("#borderRightWidth").val(bor_right);
        $("#borderBottomWidth").val(bor_bottom);
        $("#borderLeftWidth").val(bor_left);
        $("#paddingTop").val(p_top);
        $("#paddingRight").val(p_right);
        $("#paddingBottom").val(p_bottom);
        $("#paddingLeft").val(p_left);

        $("#option-border").find("input[type='checkbox']").each(function(){
            if(!$(this).prop("checked")){
                $(this).parents(".form-group").find("select.form-control").addClass("disabled").attr("disabled", true).val("0px");
            }else{
                $(this).parents(".form-group").find("select.form-control").removeClass("disabled").attr("disabled", false);
            }
        });
    }

    function optionsWarningMsg(show, wrap, msg){
        if(!show) {
            wrap.find(".group-warning").remove();
            return;
        }

        var html = '<div class="alert alert-warning group-warning"><p class="mb0">' +(msg || '文字尺寸小于12px，在使用Google Chrome浏览器可能会出现显示异常，所以我们建议您使用Firefox或IE9以上版本浏览器执行标签打印')+ '</p></div>';
        wrap.find(".group-warning").remove();
        $(html).appendTo(wrap).addClass("fade in");
    }

    //button && link handles
    $(".label-type .panel.panel-default").on("click", function(){
        var height = window.innerHeight - $(".custom-header").height() - (($(".panel.panel-default .panel-heading").outerHeight()+2)/**$(".panel.panel-default").length*/);
        $(this).find(".panel-body").css("height", height);
        $(".label-type .panel-title .glyphicon").attr("class", "glyphicon glyphicon-menu-down");
        if( !$(this).find(".panel-collapse").hasClass("in") ){
            $(this).find(".panel-title .glyphicon").attr("class", "glyphicon glyphicon-menu-up");
            $(".label-type .panel.panel-default").hide();
            $(this).show();
        }else{
            $(this).find(".panel-title .glyphicon").attr("class", "glyphicon glyphicon-menu-down");
            $(".label-type .panel.panel-default").show();
        }
    });

    $(".btn-copy").on("click", function(){
        var copy = currentObj.clone();
        currentObj.removeClass("active");
        copy.find("div.ui-resizable-handle").remove();
        copy.html(copy.html()).appendTo(currentObj.parent());
        copy.find(".ui-icon").css("opacity", 1);
        copy.css({
            "left": parseInt(copy.css("left")) + 10,
            "top": parseInt(copy.css("top")) + 10
        });
        currentObj = copy;
        senditemExtend();
    });

    $(".btn-save").on("click", function(){
        var text = $("#custom-palette").html();
        var size = $("#size").val();
        if(size == undefined || size == "" || size == null)
            alert("请选择尺寸！");
        if(text != undefined && text != "" && text != null && text.length > 1){
            $("#styleHtml").val(text);
            //alert(text);
            var $form =  $("#validateForm");
            var action = $form.attr("action");
            $("#save-data").attr("disabled", true);
            $form.submit();
            $form.attr("action",action);
        }
    });

    $(".btn-clear-item").on("click", function(){
        if( currentObj.hasClass("skulist") ){
            //$(".copy-label-content").remove();
            $(".dragitem.skulist").draggable({ revert: false });
        }else if( currentObj.hasClass("declarelist") ){
            $(".dragitem.declarelist").draggable({ revert: false });
        }
        currentObj.remove();
        $(".dragitem.hasSkulist").removeClass("hasSkulist");
        $(".hotkey-panel").css("opacity", 0);
        $(".label-set").removeClass("opened");
    });

    $(".btn-reset").on("click", function(){
        //$(".senditem").remove();
        //$(".copy-label-content").remove();
	$("#custom-palette").html("");
        $(".dragitem.hasSkulist").removeClass("hasSkulist");
        $(".dragitem.skulist").draggable({ revert: false });
        $(".dragitem.declarelist").draggable({ revert: false });
    });

    $(".btn-view").on("click", function(){
        $("body").addClass("viewtype");
        $(".view-panel, .viewCover").show();
    });

    $(".btn-close").on("click", function(){
        window.close();
    });

    $(".btn-close-view").on("click", function(){
        $("body").removeClass("viewtype");
        $(".view-panel, .viewCover").hide();
        $(".label-content").css({
            left: "50%",
            top: "25%"
        }).removeClass("scale150 scale200 scale300");
        $(".viewpct .text").text("100%");
    });

    $(".viewpct .dropdown-menu a").on("click", function() {
        var newClass = $(this).attr("rel");
        $(".viewpct .dropdown-toggle .text").html($(this).text());
        switch (newClass) {
            case "default":
                $(".label-content").removeClass("scale150 scale200 scale300");
                break;
            case "scale150":
                $(".label-content").removeClass("scale200 scale300").addClass("scale150");
                break;
            case "scale200":
                $(".label-content").removeClass("scale150 scale300").addClass("scale200");
                break;
            case "scale300":
                $(".label-content").removeClass("scale200 scale150").addClass("scale300");
                break;
            default:
                ;
        }
    });

    $(".btn-print-view").on("click", function(){
        var obj = $(".custom-label").clone();
        obj.find("div.ui-resizable-handle").remove();

        localStorage.setItem("printView", obj.html());
        window.open('./bd_size!printPreview.action');
    });

    //title options event
    $("#viewTitle").on("change", function(){
        var title = currentObj.find(".title"),
            checked = !!$(this).prop("checked"),
            wrap = !!$("#titleNowrap").prop("checked");
        if(wrap && checked){
            title.css("display", "block");
        }else if(!wrap && checked) {
            title.css("display", "inline");
        }else if(!checked){
            title.css("display", "none");
        }
        $(this).parents(".panel-body").find(".title-set input, .title-set select").addClass("disabled").attr("disabled", !checked);
    });

    $("#titleName").on("keyup", function(){
        currentObj.find(".title").html($(this).val());
    });

    $("#titleNowrap").on("change", function(){
        currentObj.find(".title").css("display", !!$(this).prop("checked") ? "block" : "inline");
        var moreinfo = $(this).parents(".panel-body").find(".title-set .moreinfo");
        !!$(this).prop("checked") && moreinfo.removeClass("disn");
        !$(this).prop("checked") && moreinfo.addClass("disn");
    });

    $("#titleAlign").on("change", function(){
        currentObj.find(".title").css("text-align", $(this).val());
    });

    $("#titlePaddingBottom").on("change", function(){
        currentObj.find(".title").css("padding-bottom", $(this).val());
    });

    $("#titleFontFamily").on("change", function(){
        currentObj.find(".title").css("font-family", $(this).val());
    });

    $("#titleFontSize").on("change", function(){
        currentObj.find(".title").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 && $(this).val().indexOf("pt")<0 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#titleLineHeight").on("change", function(){
        currentObj.find(".title").css("line-height", $(this).val());
    });

    $("#titleFontWeight").on("change", function(){
        currentObj.find(".title").css("font-weight", !!$(this).prop("checked") ? 700 : 400);
    });


    $("#detailName").on("change", function(){
        currentObj.find(".content").html($(this).val());
    });
    //detail options event
    $("#detailAlign").on("change", function(){
        currentObj.css("text-align", $(this).val());
    });

    $("#detailFontFamily").on("change", function(){
        currentObj.find(".detail").css("font-family", $(this).val());
    });

    $("#detaillFontSize").on("change", function(){
        currentObj.find(".detail").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 && $(this).val().indexOf("pt")<0 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#detailLineHeight").on("change", function(){
        currentObj.find(".detail").css("line-height", $(this).val());
    });

    $("#detailFontWeight").on("change", function(){
        currentObj.find(".detail").css("font-weight", !!$(this).prop("checked") ? 700 : 400);
    });

    $("#showProductName").on("change", function(){
        if(!!$(this).prop("checked")){
            currentObj.find(".productName").removeClass("disn");
        }else
            currentObj.find(".productName").addClass("disn");
    });

    //border options event
    $("#borderTop").on("change", function(){
        currentObj.css("border-top", !!$(this).prop("checked") ? "1px solid #000" : "none");
        currentObj.css("padding-top", "0px");
        $("#borderTopWidth").val(!!$(this).prop("checked") ? "1px" :  "0px");
        $("#paddingTop").val("0px");
        $(this).parents(".form-group").find("select.form-control").addClass("disabled").attr("disabled", !$(this).prop("checked"));
    });

    $("#borderBottom").on("change", function(){
        currentObj.css("border-bottom", !!$(this).prop("checked") ? "1px solid #000" : "none");
        currentObj.css("padding-bottom", "0px");
        $("#borderBottomWidth").val(!!$(this).prop("checked") ? "1px" : "0px");
        $("#paddingBottom").val("0px");
        $(this).parents(".form-group").find("select.form-control").addClass("disabled").attr("disabled", !$(this).prop("checked"));
    });

    $("#borderRight").on("change", function(){
        currentObj.css("border-right", !!$(this).prop("checked") ? "1px solid #000" : "none");
        currentObj.css("padding-right", "0px");
        $("#borderRightWidth").val(!!$(this).prop("checked") ? "1px" : "0px");
        $("#paddingRight").val("0px");
        $(this).parents(".form-group").find("select.form-control").addClass("disabled").attr("disabled", !$(this).prop("checked"));
    });

    $("#borderLeft").on("change", function(){
        currentObj.css("border-left", !!$(this).prop("checked") ? "1px solid #000" : "none");
        currentObj.css("padding-left", "0px");
        $("#borderLeftWidth").val(!!$(this).prop("checked") ? "1px" : "0px");
        $("#paddingLeft").val("0px");
        $(this).parents(".form-group").find("select.form-control").addClass("disabled").attr("disabled", !$(this).prop("checked"));
    });

    $("#borderTopWidth").on("change", function(){
        currentObj.css("border-top-width", $(this).val());
    });

    $("#paddingTop").on("change", function(){
        currentObj.css("padding-top", $(this).val());
    });

    $("#borderBottomWidth").on("change", function(){
        currentObj.css("border-bottom-width", $(this).val());
    });

    $("#paddingBottom").on("change", function(){
        currentObj.css("padding-bottom", $(this).val());
    });

    $("#borderRightWidth").on("change", function(){
        currentObj.css("border-right-width", $(this).val());
    });

    $("#paddingRight").on("change", function(){
        currentObj.css("padding-right", $(this).val());
    });

    $("#borderLeftWidth").on("change", function(){
        currentObj.css("border-left-width", $(this).val());
    });

    $("#paddingLeft").on("change", function(){
        currentObj.css("padding-left", $(this).val());
    });


    //address options event
    $("#option-field-address").on("click", "input[name='viewField']", function(){
        var classname = $(this).val(),
            display = !!$(this).prop("checked"),
            wrap = $(this).parents(".panel-body").find("#newline").prop("checked"),
            checkedNum = $(this).parents(".multiple").find(".checkbox-inline.check input[type='checkbox']:checked").length;

        if( !checkedNum ){
            $(this).prop("checked", true);
            $.gritter.add({
                title: '操作错误',
                text: '至少勾选一个字段！',
                sticky: false,
                time: 3000,
                class_name: 'danger-gritter'
            });
            return false;
        }

        if( wrap && display ) {
            currentObj.find("span."+classname).css("display", "block");
        }else if( !wrap && display ) {
            currentObj.find("span."+classname).css("display", "inline");
        }else{
            currentObj.find("span."+classname).css("display", "none");
        }

        if( classname=="country" && !display ) {
            $(this).parents(".form-group.multiple").find("input[value='country_cn']").addClass("disabled").attr("disabled", true);
            $(this).parents(".form-group.multiple").find("input[value='country_en']").addClass("disabled").attr("disabled", true);
        }else if( classname=="country" && display ){
            $(this).parents(".form-group.multiple").find("input[value='country_cn']").removeClass("disabled").attr("disabled", false);
            $(this).parents(".form-group.multiple").find("input[value='country_en']").removeClass("disabled").attr("disabled", false);
        }

        if( classname=="country_cn" && display) {
            currentObj.find("span.country_cn").css("display", "inline");
        }

    });

    $("#option-field-declare .checkbox-inline").on("change", function(){
        var value = $(this).find("input").val(),
            checkedNum = $(this).parents("#option-field-declare").find("input[type='checkbox']:checked").length;

        if( !checkedNum ){
            $(this).find("input[type='checkbox']").prop("checked", true);
            $.gritter.add({
                title: '操作错误',
                text: '至少勾选一个字段！',
                sticky: false,
                time: 3000,
                class_name: 'danger-gritter'
            });
            return false;
        }
        if( !!$(this).find("input[type='checkbox']").prop("checked") ){
            //currentObj.find("."+value).removeClass("disn");
            if(value == "qty" || value == "customsNo" || value == "totalQty"){
                var headerHtml = "";
                var bodyHtml = "";
                if(value == "qty"){
                    headerHtml = "<th class='qty_declare qty' width='65'>"
                    +"<span>数量<br/>Qty</span>"
                    +"</th>";
                    bodyHtml = "<td class='qty_declare qty'>"
                    +"<hls id='name_declare_qty_0' name='labelStyle.itemList[0].qty'>1</hls>"
                    +"</td>";
                    currentObj.find(".weight_header").before(headerHtml);
                    currentObj.find(".weight_body").before(bodyHtml);
                }else if(value == "customsNo"){
                    headerHtml = "<th class='customsNo_declare customsNo' width='65'>"
                    +"<span>海关编码<br/>Hs Tariff</span>"
                    +"</th>";
                    bodyHtml = "<td class='customsNo_declare customsNo'>"
                    +"<hls id='name_declare_customsNo_0' name='labelStyle.itemList[0].customsNo'>1000025485</hls>"
                    +"</td>";

                    currentObj.find(".name_header").after(headerHtml);
                    currentObj.find(".name_body").after(bodyHtml);
                }
                else{
                    headerHtml = "<th class='total_qty_declare' width='65'>"
                    +"<span>总数量<br/>Total Qty</span>"
                    +"</th>";
                    bodyHtml = "<td class='total_qty_declare tfoot'>"
                    +"<span id='name_declare_total_qty'>1</span>"
                    +"</td>";

                    currentObj.find(".origin_declare").after(headerHtml);
                    currentObj.find(".text-center").after(bodyHtml);
                }
                //var col = currentObj.find(".text-center").attr("colspan");
                //currentObj.find(".tfoot").attr("colspan",1);
                //currentObj.find(".text-center").attr("colspan",parseInt(col) + 1);
                totalSum();
            }else{
                currentObj.find("."+value).removeClass("disn");
            }

            $(this).parents(".form-group").find("input[type='text']").removeClass("disabled").prop("disabled", false);
        }else{
            //currentObj.find("."+value).addClass("disn");
            if(value == "qty" || value == "customsNo" || value == "totalQty"){
                var html;
                if(value == "qty"){
                    html = currentObj.find(".qty_declare.qty");
                }else if(value == "customsNo"){
                    html = currentObj.find(".customsNo_declare.customsNo_declare");
                }else{
                    html = currentObj.find(".total_qty_declare");
                }
                html.remove();
                //var col = currentObj.find(".text-center").attr("colspan");
                //currentObj.find(".tfoot").attr("colspan",1);
                //currentObj.find(".text-center").attr("colspan",parseInt(col) - 1);
                totalSum();
            }else{
                currentObj.find("."+value).addClass("disn");
            }

            $(this).parents(".form-group").find("input[type='text']").prop("disabled", true).addClass("disabled");
        }
        //var checkedNum = $("#option-field-declare").find("input[type='checkbox']:checked").length;
        //currentObj.find("tfoot>tr>td").attr("colspan", checkedNum);
    });

    function totalSum(){
        var theadChilds,tfoodChilds;
        var thead = currentObj.find("thead>tr").children();
        theadChilds = thead.length;
        var tfood = currentObj.find("tfoot>tr").children();
        tfoodChilds = tfood.length / 2;
        //alert(theadChilds+","+tfoodChilds);
        if(theadChilds > tfoodChilds){//表头td大于页脚td
            var col = theadChilds - tfoodChilds;
            currentObj.find(".origin").attr("colspan",parseInt(col)+1);
        }else if(theadChilds < tfoodChilds){ //表头td小于页脚td
            var col = tfoodChilds - theadChilds;
            currentObj.find(".name").attr("colspan",parseInt(col)+1);
        }else{
            currentObj.find(".origin").attr("colspan",1);
            currentObj.find(".name").attr("colspan",1);
        }
    }

    $("#showWeightUnit").on("change", function(){
        var $weight_unit_value = $(this).find("input").val();
        var check = $(this).prop("checked");
        changeWeight(check,$weight_unit_value);
    });
    $("#option-field-declare .radio-inline").on("change", function(){
        var $weight_unit_value = $(this).find("input").val();
        var check = $("#showWeightUnit").prop("checked");
        changeWeight(check,$weight_unit_value);
    });

    function changeWeight(check,$weight_unit_value){
        if($weight_unit_value == 'g'){
            currentObj.find(".unit-kg").addClass("disn");
            currentObj.find(".unit-g").removeClass("disn");
            if(check)
                currentObj.find(".unit-g-body").removeClass("disn");
            else
                currentObj.find(".unit-g-body").addClass("disn");
            currentObj.find(".unit-kg-body").addClass("disn");
        }else{
            currentObj.find(".unit-kg").removeClass("disn");
            currentObj.find(".unit-g").addClass("disn");
            if(check)
                currentObj.find(".unit-kg-body").removeClass("disn");
            else
                currentObj.find(".unit-kg-body").addClass("disn");
            currentObj.find(".unit-g-body").addClass("disn");
        }
    }

    $("#showCurrency").on("change",function(){
        var value = $(this).prop("checked");
        if(value){
            $("#fieldTextCurrency").removeClass("disn");
            $("#switchCurrencyLabel").removeClass("disn");
            $("#currency-position").removeClass("disn");
            $("#fieldTextCurrency").trigger("change");
        }else{
            $("#fieldTextCurrency").addClass("disn");
            $("#switchCurrencyLabel").addClass("disn");
            $("#currency-position").addClass("disn");
            currentObj.find(".currency-before").addClass("disn");
            currentObj.find(".currency-after").addClass("disn");
        }
    });

    $("#switchCurrency").on("change",function(){
        var value = $(this).prop("checked");
        if(value){
            currentObj.find(".currency-before").addClass("switch");
            currentObj.find(".currency-after").addClass("switch");
        }else{
            currentObj.find(".currency-before").removeClass("switch");
            currentObj.find(".currency-after").removeClass("switch");
        }
    });

    $("#fieldTextCurrency").on("change", function(){
        var currency = $('#fieldTextCurrency option:selected').val();
        var currency_position_value = $("input[name='currency-position']:checked").val();
        changeCurrencyPosition(currency,currency_position_value);
    });

    function changeCurrencyPosition(currency,currency_position_value){
        if(currency_position_value == "before"){
            currentObj.find(".currency-before").removeClass("disn");
            currentObj.find(".currency-after").addClass("disn");
            currentObj.find(".currency-before").each(function(){
                $(this).html(currency);
            });
        }else{
            currentObj.find(".currency-after").removeClass("disn");
            currentObj.find(".currency-before").addClass("disn");
            currentObj.find(".currency-after").each(function(){
                $(this).html(currency);
            });
        }
    }

    $("#option-field-declare .radio-currency-position").on("change", function(){
        var currency = $('#fieldTextCurrency option:selected').val();
        var currency_position_value = $(this).find("input").val();
        changeCurrencyPosition(currency,currency_position_value);
    });

    $("#declareType").on("change", function(){
        var select = $('#declareType option:selected').val();
        if(select == "sort"){
            //$(".name_body .name").removeClass("disn");
            $(".name_body .name_cn").addClass("disn");
            $(".name_body .number").addClass("disn");
        }else{
            $(".name_body .name").removeClass("disn");
            $(".name_body .name_cn").removeClass("disn");
            $(".name_body .number").removeClass("disn");
        }
    });

    $("#newline").on("change", function(){
        var display = !!$(this).prop("checked") ? "block" : "inline";

        currentObj.find("span").each(function(){
            if( $(this).css("display")!="none" && $(this).hasClass("country_cn") ){
                $(this).css("display", "inline");
            }else if( $(this).css("display")!="none" ){
                $(this).css("display", display);
            }
        });
    });


    //barcode options event
    $("#viewCodeNum").on("change", function(){
        currentObj.find(".codeNumber").css("display", !!$(this).prop("checked")?"block":"none" );
    });

    $("#codeNumAlign").on("change", function(){
        currentObj.find(".codeNumber").css("text-align", $(this).val());
    });

    $("#codeNumFontSize").on("change", function(){
        currentObj.find(".codeNumber").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 && $(this).val().indexOf("pt")<0 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#codeFontStretch").on("change", function(){
        currentObj.find(".default-barcode").css("font-size", $(this).val());
    });

    $("#codeNumFontWeight").on("change", function(){
        currentObj.find(".codeNumber").css("font-weight", !!$(this).prop("checked")?700:400);
    });


    //line-x && line-y options event
    $(".customnum .subtract").on("click", function(){
        if( $(this).hasClass("subtract-x") ) {
            var width = parseInt(currentObj.width());
            if(width<=0) return;
            width--;
            currentObj.css("width", width);
            $("#xLineWidth").val(width);
        }else{
            var height = parseInt(currentObj.height());
            if(height<=0) return;
            height--;
            currentObj.css("height", height);
            $("#yLineHeight").val(height);
        }

    });

    $(".customnum .add").on("click", function(){
        if( $(this).hasClass("add-x") ) {
            var width = parseInt(currentObj.width()),
                maxwidth = parseInt(currentObj.attr("data-max-width"));
            //if( width>= maxwidth ) return;
            width++;
            currentObj.css("width", width);
            $("#xLineWidth").val(width);
        }else{
            var height = parseInt(currentObj.height()),
                maxheight = parseInt(currentObj.attr("data-max-height"));
            //if( height>= maxheight ) return;
            height++;
            currentObj.css("height", height);
            $("#yLineHeight").val(height);
        }

    });


    $("#rotateDiv .subtract").on("click", function(){
        var transform = currentObj.attr("transform");
        if(undefined == transform || "" == transform || null == transform)
            transform = 0;
        var rotate = parseInt(transform);
        rotate--;
        currentObj.attr("transform", rotate);
        $("#rotate").val(rotate);
        currentObj.find("img").css("transform:rotate", rotate+"deg");
        currentObj.find("img").css("-ms-transform:rotate", rotate+"deg");  /* IE 9 */
        currentObj.find("img").css("-moz-transform:rotate", rotate+"deg"); /* Firefox */
        currentObj.find("img").css("-o-transform:rotate", rotate+"deg");   /* Opera */
        currentObj.find("img").css("-webkit-transform:rotate", rotate+"deg");  /* Safari 和 Chrome */
    });

    $("#rotateDiv .add").on("click", function(){
        var transform = currentObj.attr("transform");
        if(undefined == transform || "" == transform || null == transform)
            transform = 0;
        var rotate = parseInt(transform);
        rotate++;
        currentObj.attr("transform", rotate);
        $("#rotate").val(rotate);
        currentObj.find("img").css("transform:rotate", rotate+"deg");
        currentObj.find("img").css("-ms-transform:rotate", rotate+"deg");  /* IE 9 */
        currentObj.find("img").css("-moz-transform:rotate", rotate+"deg"); /* Firefox */
        currentObj.find("img").css("-o-transform:rotate", rotate+"deg");   /* Opera */
        currentObj.find("img").css("-webkit-transform:rotate", rotate+"deg");  /* Safari 和 Chrome */
    });

    $("#rotate").on("keyup", function(){
        var transform = $(this).val();
        if(!isNaN(transform)){
            var rotate = parseInt(transform);
            currentObj.attr("transform",rotate);
            $("#rotate").val(rotate);
            currentObj.find("img").css("transform:rotate", rotate+"deg");
            currentObj.find("img").css("-ms-transform:rotate", $(this).val()+"deg");  /* IE 9 */
            currentObj.find("img").css("-moz-transform:rotate", $(this).val()+"deg"); /* Firefox */
            currentObj.find("img").css("-o-transform:rotate", $(this).val()+"deg");   /* Opera */
            currentObj.find("img").css("-webkit-transform:rotate", $(this).val()+"deg");  /* Safari 和 Chrome */
        }
    });

    $("#option-detail .add").on("click", function(){
        var wnoRuleBefore = $("#wnoRuleBefore").val();
        var wnoRuleAfter = $("#wnoRuleAfter").val();
        var wnoRuleList = $("#wnoRuleList").val();
        var before = (undefined != wnoRuleBefore && "" != wnoRuleBefore && null != wnoRuleBefore);
        var after = (undefined != wnoRuleAfter && "" != wnoRuleAfter && null != wnoRuleAfter);
        if(before && after){
            if(wnoRuleBefore == wnoRuleAfter)
                alert("单号转换前后的规则不能一样");
            else{
                var rule = wnoRuleBefore + "," + wnoRuleAfter + ";";
                //旧规则不为空，且不包含
                if(undefined != wnoRuleList && "" != wnoRuleList && null != wnoRuleList
                    && wnoRuleList.indexOf(rule) == -1 && wnoRuleList.indexOf(wnoRuleBefore) == -1)
                    wnoRuleList = wnoRuleList + rule;
                else
                    wnoRuleList = rule;
                currentObj.attr("wnoRuleList",wnoRuleList);
                $("#wnoRuleList").val(wnoRuleList);
                $("#wnoRuleBefore").val("");
                $("#wnoRuleAfter").val("");
            }
        }else
            alert("请输入单号转换前后的规则");
    });

    $("#setMaxWidth").on("click", function(){
        var maxwidth = currentObj.attr("data-max-width");
        currentObj.css({
            "width": maxwidth,
            "left": 0
        });
        $("#xLineWidth").val(parseInt(maxwidth));
    });

    $("#setMaxHeight").on("click", function(){
        var maxheight = currentObj.attr("data-max-height");
        currentObj.css({
            "height": maxheight,
            "top": 0
        });
        $("#yLineHeight").val(parseInt(maxheight));
    });

    $("#xLineWidth").on("keyup", function(){
        var maxwidth = parseInt(currentObj.attr("data-max-width"));
        var value = parseInt($(this).val());
        if(!value){
            $(this).val(maxwidth);
            currentObj.css("width", value);
        }
        //else if( value>=maxwidth ){
        //    $(this).val(maxwidth);
        //    currentObj.css("width", maxwidth);
        //}
        else{
            currentObj.css("width", value);
        }
    });

    $("#yLineHeight").on("keyup", function(){
        var maxheight = parseInt(currentObj.attr("data-max-height"));
        var value = parseInt($(this).val());
        if(!value){
            $(this).val(maxheight);
            currentObj.css("height", value);
        }
        //else if( value>=maxheight ){
        //    $(this).val(maxheight);
        //    currentObj.css("height", maxheight);
        //}
        else{
            currentObj.css("height", value);
        }
    });

    $("#xLineStyle").on("change", function(){
        currentObj.css("border-top-style", $(this).val());
    });

    $("#xLineWeight").on("change", function(){
        currentObj.css("border-top-width", $(this).val());
    });

    $("#yLineStyle").on("change", function(){
        currentObj.css("border-left-style", $(this).val());
    });

    $("#yLineWeight").on("change", function(){
        currentObj.css("border-left-width", $(this).val());
    });


    //textDetail options event
    $("#textDetail").on("keyup", function(){
        var checkIcon = currentObj.find(".detail>i").prop("outerHTML");
        currentObj.find(".detail").html( (checkIcon||'') + $(this).val().replace(/\n/gi, '<br>'));
    });

    $("#textAlign").on("change", function(){
        currentObj.css("text-align", $(this).val());
    });

    $("#textFontFamily").on("change", function(){
        currentObj.find(".detail").css("font-family", $(this).val());
    });

    $("#textFontSize").on("change", function(){
        currentObj.find(".detail").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 && $(this).val().indexOf("pt")<0 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#textLineHeight").on("change", function(){
        currentObj.find(".detail").css("line-height", $(this).val());
    });

    $("#textFontWeight").on("change", function(){
        currentObj.find(".detail").css("font-weight", !!$(this).prop("checked")?700:400);
    });

    //$("#textCheckBox").on("click",function(){
    //    var content;
    //    if( !!$(this).prop("checked") ){
    //        $("#checkBoxType").parent().removeClass("disn");
    //        content = '<i class="ico-checkbox-unchecked"></i>' + currentObj.find(".detail").html();
    //        currentObj.find(".detail").html(content);
    //    }else{
    //        $("#checkBoxType").parent().addClass("disn");
    //        currentObj.find(".detail>i").remove();
    //    }
    //});

    $("#textCheckBox").on("change", function(){
        var content;
        if( !!$(this).prop("checked") ){
            $("#checkBoxType").parent().removeClass("disn");
            $("#product_checkbox").parent().removeClass("disn");
            content = '<i class="ico-checkbox-unchecked"></i>' + currentObj.find(".detail").html();
            currentObj.find(".detail").html(content);
        }else{
            $("#checkBoxType").parent().addClass("disn");
            $("#product_checkbox").parent().addClass("disn");
            currentObj.find(".detail>i").remove();
        }
    });

    $("#checkBoxType").on("change", function(){
        var value = $(this).val();
        currentObj.find(".detail>i").addClass(value);
        if(value == "ico-checkbox-unchecked"){
            currentObj.find(".detail>i").removeClass("ico-checkbox");
            currentObj.find(".detail>i").removeClass("ico-checkbox-remove");
        }else if(value == "ico-checkbox-remove"){
            currentObj.find(".detail>i").removeClass("ico-checkbox");
            currentObj.find(".detail>i").removeClass("ico-checkbox-unchecked");
        }else{
            currentObj.find(".detail>i").removeClass("ico-checkbox-remove");
            currentObj.find(".detail>i").removeClass("ico-checkbox-unchecked");
        }
    });

    $("#checkBoxRFlag").on("change", function(){
        var value = $(this).val();
        if(value != "0"){
            currentObj.attr("rflag",value);
        }else{
            currentObj.removeAttr("rflag");
        }
    });

    $("#checkBoxCountry").on("change", function(){
        var value = $(this).val();
        if(value == "1"){
            $("#countryList").removeClass("disn");
            $("#countrys").removeClass("disn");
            $("#notCountryDiv").removeClass("disn");
            $("#rflagAndCountryDiv").removeClass("disn");
        }else{
            $("#countryList").addClass("disn");
            $("#countrys").addClass("disn");
            $("#notCountryDiv").addClass("disn");
            $("#rflagAndCountryDiv").addClass("disn");
            currentObj.removeAttr("country");
            currentObj.removeAttr("notCountry");
            $("#countrys").val("");
        }
    });

    $("#checkBoxNotCountry").on("change", function(){
        var value = $(this).val();
        if(value == "1"){
            var countryOld = currentObj.attr("country");
            if(undefined != countryOld && null != countryOld && "" != countryOld){
                currentObj.attr("notCountry",countryOld);
                currentObj.removeAttr("country");
            }
        }else{
            var notCountryOld = currentObj.attr("notCountry");
            if(undefined != notCountryOld && null != notCountryOld && "" != notCountryOld){
                currentObj.attr("country",notCountryOld);
                currentObj.removeAttr("notCountry");
            }
        }
    });

    $("#rflagAndCountry").on("change", function(){
        var value = $(this).val();
        if(value == "1"){
            currentObj.attr("rflagAndCountry",1);
        }else{
            currentObj.removeAttr("rflagAndCountry");
        }
    });

    $("#countryList").on("change", function(){
        var value = $(this).val();
        var notCountryValue = $("#checkBoxNotCountry").val();
        var country = "";
        if( notCountryValue == "1"){
            var notCountryOld = currentObj.attr("notCountry");
            if(undefined != notCountryOld && null != notCountryOld && "" != notCountryOld)
                country = notCountryOld + "," + value;
            else
                country = value;
            currentObj.attr("notCountry",country);
            currentObj.removeAttr("country");
        }else{
            var countryOld = currentObj.attr("country");
            if(undefined != countryOld && null != countryOld && "" != countryOld)
                country = countryOld + "," + value;
            else
                country = value;
            currentObj.attr("country",country);
            currentObj.removeAttr("notCountry");
        }
        $("#countrys").val(country);
    });

    $("#countrys").on("keyup", function(){
        var notCountryValue = $("#checkBoxNotCountry").val();
        if( notCountryValue == "1" ){
            currentObj.attr("notCountry",$(this).val());
            currentObj.removeAttr("country");
        }else{
            currentObj.attr("country",$(this).val());
            currentObj.removeAttr("notCountry");
        }
    });

    $("#detailOrderType").on("change",function(){
        if( !!$(this).prop("checked") ){
            currentObj.find(".detail>i").addClass($(this).val());
            $("#productType").parent().removeClass("disn");
            var name = currentObj.find(".detail>i").attr("name");
            if(name != undefined && name != null && name != "")
                $("#productType").trigger("change",name);
            else
                $("#productType").trigger("change","");
        }else{
            var type = $("#productType").val();
            currentObj.find(".detail>i").removeClass($(this).val());
            currentObj.find(".detail>i").removeClass(type);
            $("#productType").parent().addClass("disn");
        }
    });

    $("#productType").on("change",function(event,name){
        if(name != undefined && name != null && name != ""){
            $("#productType").val(name);
        }else{
            var type = $("#productType").val();
            currentObj.find(".detail>i").attr("name",type);
        }
    });


    //circle text options event
    $("#circleBorderWidth").on("change", function(){
        currentObj.css("border-width", $(this).val());
    });

    $("#circleText").on("keyup", function(){
        currentObj.find(".detail").html($(this).val());
    });

    $("#circleFontFamily").on("change", function(){
        currentObj.find(".detail").css("font-family", $(this).val());
    });

    $("#circleFontSize").on("change", function(){
        currentObj.find(".detail").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 && $(this).val().indexOf("pt")<0 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#circleFontWeight").on("change", function(){
        currentObj.find(".detail").css("font-weight", !!$(this).prop("checked")?700:400);
    });


    //loading img options event
    $("#loadImgUrl").on("click", function(){
        var src = $("#imageUrl").val();
        currentObj.find("img.placeholder").css("display", "none");
        currentObj.find("img.online").attr("src", src).removeClass("disn");
    });


    //table options event
    $("#viewTdBorder").on("change", function(){
        if( !!$(this).prop("checked") ){
            currentObj.find("th, td").css("border", "1px solid #000");
            //skulistObj.find("th, td").css("border", "1px solid #000");
        }else{
            currentObj.find("th, td").css("border", "none");
            //skulistObj.find("th, td").css("border", "none");
        }
        currentObj.css("height", currentObj.find(".skulist-table").height());
    });

    $("#viewThead").on("change", function(){
        if( !!$(this).prop("checked") ){
            currentObj.find("thead").show();
            $(this).parents(".form-group").next(".moreinfo").find("input,select").removeClass("disabled").prop("disabled", false);
            //skulistObj.find("thead").show();
        }else{
            currentObj.find("thead").hide();
            $(this).parents(".form-group").next(".moreinfo").find("input,select").addClass("disabled").prop("disabled", true);
            //skulistObj.find("thead").hide();
        }
        currentObj.css("height", currentObj.find(".skulist-table").height());
    });

    $("#theadFontFamily").on("change", function(){
        currentObj.find("thead").css("font-family", $(this).val());
        //skulistObj.find("thead").css("font-family", $(this).val());
    });

    $("#theadFontSize").on("change", function(){
        currentObj.find("thead").css("font-size", $(this).val());
        //skulistObj.find("thead").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#theadLineHeight").on("change", function(){
        currentObj.find("thead").css("line-height", $(this).val());
        currentObj.find("thead>tr").css("line-height", $(this).val());
        currentObj.find("thead>tr>td").css("line-height", $(this).val());
        currentObj.find("thead>tr>th").css("line-height", $(this).val());
        currentObj.find("thead>tr>th>span").css("line-height", $(this).val());
        currentObj.find("thead>tr>td>span").css("line-height", $(this).val());
    });

    $("#theadHeight").on("change", function(){
        currentObj.find("thead").css("height", $(this).val());
        currentObj.find("thead>tr").css("height", $(this).val());
        currentObj.find("thead>tr>th").css("height", $(this).val());
        currentObj.find("thead>tr>td").css("height", $(this).val());
        currentObj.find("thead>tr>th>span").css("height", $(this).val());
        currentObj.find("thead>tr>td>span").css("height", $(this).val());
    });

    $("#tbodyFontFamily").on("change", function(){
        currentObj.find("tbody").css("font-family", $(this).val());
        //skulistObj.find("tbody").css("font-family", $(this).val());
    });

    $("#tbodyFontSize").on("change", function(){
        currentObj.find("tbody").css("font-size", $(this).val());
        currentObj.find("tbody>tr").css("font-size", $(this).val());
        currentObj.find("tbody>tr>td").css("font-size", $(this).val());
        currentObj.find("tbody>tr>tr").css("font-size", $(this).val());
        currentObj.find("tbody>tr>td>span").css("font-size", $(this).val());
        currentObj.find("tbody>tr>tr>span").css("font-size", $(this).val());
        //skulistObj.find("tbody").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#tbodyLineHeight").on("change", function(){
        currentObj.find("tbody").css("line-height", $(this).val());
        currentObj.find("tbody>tr").css("line-height", $(this).val());
        currentObj.find("tbody>tr>td").css("line-height", $(this).val());
        currentObj.find("tbody>tr>th").css("line-height", $(this).val());
        currentObj.find("tbody>tr>th>span").css("line-height", $(this).val());
        currentObj.find("tbody>tr>td>span").css("line-height", $(this).val());
        /*currentObj.find("tbody>tr>th>hls").css("line-height", $(this).val());
        currentObj.find("tbody>tr>td>hls").css("line-height", $(this).val());
        currentObj.find("tbody>tr>th>span>hls").css("line-height", $(this).val());
        currentObj.find("tbody>tr>td>span>hls").css("line-height", $(this).val());*/
    });

    $("#tbodyHeight").on("change", function(){
        currentObj.find("tbody").css("height", $(this).val());
        currentObj.find("tbody>tr").css("height", $(this).val());
        currentObj.find("tbody>tr>th").css("height", $(this).val());
        currentObj.find("tbody>tr>td").css("height", $(this).val());
        currentObj.find("tbody>tr>td>span").css("height", $(this).val());
        currentObj.find("tbody>tr>th>span").css("height", $(this).val());
        currentObj.find("tbody>tr>td>span>hls").css("height", $(this).val());
        currentObj.find("tbody>tr>th>span>hls").css("height", $(this).val());
    });

    $("#customsNoWidth").on("change", function(){
        $(".customsNo_declare").css("width", $(this).val());
    });
    $("#nameWidth").on("change", function(){
        $(".name_declare").css("width", $(this).val());
    });
    $("#qtyWidth").on("change", function(){
        $(".qty_declare").css("width", $(this).val());
    });
    $("#weightWidth").on("change", function(){
        $(".weight_declare").css("width", $(this).val());
    });
    $("#priceWidth").on("change", function(){
        $(".price_declare").css("width", $(this).val());
    });
    $("#originWidth").on("change", function(){
        $(".price_declare").css("width", $(this).val());
    });
    $("#noWidth").on("change", function(){
        $(".origin_declare").css("width", $(this).val());
    });



    $("#viewTfoot").on("change", function(){
        if( !!$(this).prop("checked") ){
            currentObj.find("tfoot").show();
            $(this).parents(".form-group").next(".moreinfo").find("input,select").removeClass("disabled").prop("disabled", false);
            //skulistObj.find("tfoot").show();
        }else{
            currentObj.find("tfoot").hide();
            $(this).parents(".form-group").next(".moreinfo").find("input,select").addClass("disabled").prop("disabled", true);
            //skulistObj.find("tfoot").hide();
        }
        currentObj.css("height", currentObj.find(".skulist-table").height());
    });

    $("#viewTfootHeader").on("change", function(){
        if( !!$(this).prop("checked") ){
            currentObj.find(".tfootHeader").removeClass("disn");
            //$(this).parents(".form-group").next(".moreinfo").find("input,select").removeClass("disabled").prop("disabled", false);
            //skulistObj.find("tfoot").show();
        }else{
            currentObj.find(".tfootHeader").addClass("disn");
            //$(this).parents(".form-group").next(".moreinfo").find("input,select").addClass("disabled").prop("disabled", true);
            //skulistObj.find("tfoot").hide();
        }
        currentObj.css("height", currentObj.find(".skulist-table").height());
    });

    /*$("#tfootFontFamily").on("change", function(){
        currentObj.find("tfoot").css("font-family", $(this).val());
        //skulistObj.find("tfoot").css("font-family", $(this).val());
    });*/

    $("#tfootFontSize").on("change", function(){
        //alert("span:"+currentObj.find("tfoot>tr>th>span").css("font-family")+",body:"+$("body").css("font-family")+"tbody`span:"+currentObj.find("tbody").css("font-family"));
        currentObj.find("tfoot").css("font-size", $(this).val());
        currentObj.find("tfoot>tr").css("font-size", $(this).val());
        currentObj.find("tfoot>tr>td").css("font-size", $(this).val());
        currentObj.find("tfoot>tr>th").css("font-size", $(this).val());
        currentObj.find("tfoot>tr>td>span").css("font-size", $(this).val());
        currentObj.find("tfoot>tr>th>span").css("font-size", $(this).val());
        //skulistObj.find("tfoot").css("font-size", $(this).val());
        if( parseInt($(this).val())<12 ) {
            optionsWarningMsg(true, $(this).parents(".label-set"));
        }else{
            optionsWarningMsg(false, $(".label-set"));
        }
    });

    $("#tfootLineHeight").on("change", function(){
        currentObj.find("tfoot").css("line-height", $(this).val());
        currentObj.find("tfoot>tr").css("line-height", $(this).val());
        currentObj.find("tfoot>tr>td").css("line-height", $(this).val());
        currentObj.find("tfoot>tr>th").css("line-height", $(this).val());
        currentObj.find("tfoot>tr>th>span").css("line-height", $(this).val());
        currentObj.find("tfoot>tr>td>span").css("line-height", $(this).val());
    });

    $("#tfootHeight").on("change", function(){
        currentObj.find("tfoot").css("height", $(this).val());
        currentObj.find("tfoot>tr").css("height", $(this).val());
        currentObj.find("tfoot>tr>th").css("height", $(this).val());
        currentObj.find("tfoot>tr>td").css("height", $(this).val());
        currentObj.find("tfoot>tr>th>span").css("height", $(this).val());
        currentObj.find("tfoot>tr>th>span").css("height", $(this).val());
    });

    var maxWidth = 72;
    var minWidth = 40;
    $("#xBarcodeWidth").on("keyup", function(){
        var value = parseInt($(this).val());
        if(!value || value <= minWidth)
            value = minWidth;
        else if( value>= maxWidth )
            value = maxWidth;
        currentObj.find(".default-barcode").css("font-size", value+"px");
    });

    $(".barcodeWidth .add").on("click", function(){
        var width = currentObj.find(".default-barcode").css("font-size");
        if(undefined == width || null == width || "" == width)
            width = minWidth;
        width = parseInt(width.replace("px",""));
        if( width >= maxWidth ) return;
        width++;
        currentObj.find(".default-barcode").css("font-size", width+"px");
        $("#xBarcodeWidth").val(width);
    });

    $(".barcodeWidth .subtract").on("click", function(){
        var width = currentObj.find(".default-barcode").css("font-size");
        if(undefined == width || null == width || "" == width)
            width = maxWidth;
        width = parseInt(width.replace("px",""));
        if( width <= minWidth ) return;
        width--;
        currentObj.find(".default-barcode").css("font-size", width+"px");
        $("#xBarcodeWidth").val(width);
    });

    $("#xBarcodeHeight").on("change", function(){
        currentObj.find(".default-barcode").css("height", $(this).val());
        currentObj.find(".default-barcode").css("line-height", $(this).val());
    });

    $("#barcodeTxt").on("keyup", function(){
        currentObj.find(".barcode-text").html($(this).val());
        $.ajax({
            url : "bd_size!getCode128Font.action",
            type : "post",
            dataType : "json",
            data: "no=" + $(this).val(),
            success : function(result) {
                if(result.status=='success') {
                    currentObj.find(".default-barcode").html(result.no);
                } else {
                    alert(result.message);
                }
            } ,
            error:function(result){
                alert(result.message);
            }
        });

    });

    $("#option-field-sku .checkbox-inline").on("change", function(){
        var value = $(this).find("input").val(),
            checkedNum = $(this).parents("#option-field-sku").find("input[type='checkbox']:checked").length;

        if( !checkedNum ){
            $(this).find("input[type='checkbox']").prop("checked", true);
            $.gritter.add({
                title: '操作错误',
                text: '至少勾选一个字段！',
                sticky: false,
                time: 3000,
                class_name: 'danger-gritter'
            });
            return false;
        }

        if( !!$(this).find("input[type='checkbox']").prop("checked") ){
            currentObj.find("."+value).removeClass("disn");
            //skulistObj.find("."+value).removeClass("disn");
            $(this).parents(".form-group").find("input[type='text']").removeClass("disabled").prop("disabled", false);
        }else{
            currentObj.find("."+value).addClass("disn");
            //skulistObj.find("."+value).addClass("disn");
            $(this).parents(".form-group").find("input[type='text']").prop("disabled", true).addClass("disabled");
        }
        var checkedNum = $("#option-field-sku").find("input[type='checkbox']:checked").length;
        currentObj.find("tfoot>tr>td").attr("colspan", checkedNum);
        //skulistObj.find("tfoot>tr>td").attr("colspan", checkedNum);
    });

    $("#option-field-sku input[type='text']").on("keyup", function(){
        var value = $(this).parents(".form-group").find("input[type='checkbox']").val();
        currentObj.find("th."+value+" span").html($(this).val());
        //skulistObj.find("th."+value+" span").html($(this).val());
    });

    $("#declareNameTitle").on("keyup", function(){
        currentObj.find("thead .name_declare span").html($(this).val().replace(/\n/g, '<br>'));
        currentObj.css("height", currentObj.find(".skulist-table").height());
    });

    $("#declareType").on("change", function(){
        var value = $(this).val();
        $("#declareName ." + value).removeClass("disn").siblings().addClass("disn");
        currentObj.find(".name_declare."+value).removeClass("disn").siblings(".name_declare").addClass("disn");
    });

    $("#declareName .multiple input[type='checkbox']").on("change", function(){

        var checkedNum = $(this).parents(".multiple").find(".checkbox-inline input[type='checkbox']:checked").length;

        if( !checkedNum ){
            $(this).prop("checked", true);
            $.gritter.add({
                title: '操作错误',
                text: '至少勾选一个字段！',
                sticky: false,
                time: 3000,
                class_name: 'danger-gritter'
            });
            return false;
        }

        var checked = !!$(this).prop("checked"),
            name = $(this).val();
        if(checked){
            currentObj.find("span." + name).removeClass("disn");
        }else{
            currentObj.find("span." + name).addClass("disn");
        }
    });

    $("#declareNameCustom").on("keyup", function(){
        currentObj.find(".name_declare.custom span").html($(this).val().replace(/\n/g, '<br>'));
        currentObj.css("height", currentObj.find(".skulist-table").height());
    });

    //$("input[name='weight-unit']").on("change", function(){
    //    var value = $(this).val(), content;
    //    if( value=="g" ){
    //        content = '重量(克)<br>Weight(g)';
    //        currentObj.find("span.unit-kg").addClass("disn");
    //        currentObj.find("span.unit-g").removeClass("disn");
    //    }else{
    //        content = '重量(千克)<br>Weight(kg)';
    //        currentObj.find("span.unit-kg").removeClass("disn");
    //        currentObj.find("span.unit-g").addClass("disn");
    //    }
    //    currentObj.find("thead .weight_declare span").html(content);
    //    $("#declareWeightTitle").val(content.replace(/<br>/g, '\n'));
    //});

    $("#declareQtyTitle").on("keyup", function(){
        currentObj.find("thead th.qty_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareNoTitle").on("keyup", function(){
        currentObj.find("thead th.no_head span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareCustomTitle").on("keyup", function(){
        currentObj.find("thead th.customsNo_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareWeightTitle").on("keyup", function(){
        currentObj.find("thead th.weight_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declarePriceTitle").on("keyup", function(){
        currentObj.find("thead th.price_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareOriginTitle").on("keyup", function(){
        currentObj.find("thead th.origin_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareTotalQtyTitle").on("keyup", function(){
        currentObj.find("tfoot th.total_qty_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareTotalWeightTitle").on("keyup", function(){
        currentObj.find("tfoot th.weight_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareTotalPriceTitle").on("keyup", function(){
        currentObj.find("tfoot th.price_declare span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareOrigin").on("change", function(){
        currentObj.find("td.origin_declare span hls").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareOriginToTalTitle").on("keyup", function(){
        currentObj.find("tfoot th.origin span").html($(this).val().replace(/\n/g, '<br>'));
    });

    $("#declareOriginToTal").on("change", function(){
        currentObj.find("tfoot td.origin_total span").html($(this).val().replace(/\n/g, '<br>'));
    });


    //keyboard event
    $(document).on("keydown", function(event){
        if( event.keyCode==38 ){
            //up
            var itemTop = parseInt($(".senditem.active").css("top"));
            if (itemTop > 0) {
                $(".senditem.active").css("top", parseInt($(".senditem.active").css("top")) - 1 + "px");
            }
        }else if( event.keyCode==40 ){
            //down
            var itemTop = parseInt($(".senditem.active").css("top")),
                itemHeight = parseInt($(".senditem.active").outerHeight()),
                parentHeight = parseInt($("#custom-palette").height());
            if (itemTop < parentHeight - itemHeight) {
                $(".senditem.active").css("top", parseInt($(".senditem.active").css("top")) + 1 + "px");
            }
        }else if( event.keyCode==37 ){
            //left
            var itemLeft = parseInt($(".senditem.active").css("left"));
            if (itemLeft > 0) {
                $(".senditem.active").css("left", parseInt($(".senditem.active").css("left")) - 1 + "px");
            }
        }else if( event.keyCode==39 ){
            //right
            var itemLeft = parseInt($(".senditem.active").css("left")),
                itemWidth = parseInt($(".senditem.active").outerWidth()),
                parentWidth = parseInt($("#custom-palette").width());
            if (itemLeft < parentWidth - itemWidth) {
                $(".senditem.active").css("left", parseInt($(".senditem.active").css("left")) + 1 + "px");
            }
        }else if( event.keyCode==46 || event.keyCode==110 ){
            //delete
            $(".btn-clear-item").trigger("click");
        }
    });

    function changeFormSize(){
        var type = $("#sizeType").attr("value");
        if(type == 2)
            type = "single";
        else
            type = "";
        var size = $("#size option:selected").val();
        if(size == undefined || size == "" || size == null)
            size = $("#size").val();
        var width = "",height = "", ml = "";
        if(size == 1){
            width = 75;
            height = 80;
        }else if(size == 2){
            if(type == "single"){
                width = 95;
                height = 200;
            }else{
                width = 98;
                height = 98;
            }
        }else if(size == 3){
            if(type == "single"){
                width = 198;
                height = 285;
            }else{
                width = 94;
                height = 94;
            }
        }else if(size == 4){
            width = 90;
            height = 65;
        }else if(size == 5){
            if(type == "single"){
                width = 100;
                height = 75;
            }else{
                width = 80;
                height = 75;
            }
        }else if(size == 6){
            width = 98;
            height = 98;
        }else if(size == 7){
            /*if(type == "single"){*/
                width = 98;
                height = 148;
            /*}else{
                width = 98;
                height = 73;
            }*/
        }else if(size == 8){
            if(type == "single"){
                width = 98;
                height = 176;
            }else{
                width = 98;
                height = 176;
            }
        }else{
            width = 98;
            height = 98;
        }

        ml = - (width/2);
        $(".label-content").css({
            "width": width + "mm",
            "height": height + "mm",
            "margin-left": ml + "mm"
        }).fadeIn();
    }

    function changeFormType(){
        var type = $("#sizeType option:selected").val();
        if(type == undefined || type == "" || type == null)
            type = $("#sizeType").val();
        if(type == 1){
            $(".dragitem.declarelist").show();
            $(".dragitem.skulist").hide();
        }else{
            $(".dragitem.declarelist").hide();
            $(".dragitem.skulist").show();
        }
    }


}());
