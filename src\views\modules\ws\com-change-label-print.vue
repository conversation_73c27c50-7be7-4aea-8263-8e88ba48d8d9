<template>
  <div class="split-panel-body panel_dark_bg">
    <splitpanes class="parcel-bagging-wrap default-theme" horizontal>
        <pane>
          <splitpanes>
            <pane size="60">
              <el-row class="form-card" type="flex" justify="center">
                <el-col :span="20">
                  <el-form ref="dataForm" :model="dataForm" :rules="dataRule" @submit.native.prevent @keyup.enter.native="print">
                    <el-form-item label="预设选项">
                      <el-checkbox v-model="checked">先预览后打印</el-checkbox>
                      <el-checkbox style="padding-left: 10px;" v-model="oldPrintNew">扫旧单打新单</el-checkbox>
                    </el-form-item>
                    <el-form-item prop="no">
                      <label slot="label"></label>
                      <el-dropdown @command="changeNoType">
                        <span class="el-dropdown-link el-form-item__label">
                          {{activeNoTypeName}}<i class="el-icon-arrow-down el-icon--right light-blue"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item v-for="item in scanNoTypeList" :disabled='disabledScanNoTypeHandle(item)' :key="item.value" :command="item">{{ item.name }}</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                      <el-col :span="24">
                      <el-radio-group class='width100' v-model="orderLogisticsType" @change='radioBtnChangeHandle'>
                        <el-radio-button :label="10">小包</el-radio-button>
                        <el-radio-button :label="12">FBA / 快递</el-radio-button>
                      </el-radio-group>
                      </el-col>
                      <el-input ref="dataFormNo" size="medium" :autofocus="true" class="fontSize32" v-model.trim="dataForm.no"
                                :placeholder="$t('label.scanTips', { 'orderLogisticsType': orderLogisticsType === 10? '(小包)':'(FBA/快递)', 'no': activeNoTypeName })"></el-input>
                    </el-form-item>
                    <el-form-item>
                      <el-button size="medium" type="primary" v-if="$hasPermission('ws:comReplayLabel:print')"
                                 class="submitBtn fontSize18 fontWeightBolder" @click="print" :loading="submitLoading">打印</el-button>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </pane>
            <pane size="40" :class="isSuccess ? 'green-bg' : 'red-bg'">
              <div class="tip-bg">
                <div class="tip-text">{{ responseMsg }}</div>
              </div>
            </pane>
          </splitpanes>
        </pane>
    </splitpanes>
    <jmPrint ref="jmPrint" @printOver="printOver" @failOk="failOk"  @failCancel="failCancel"></jmPrint>
  </div>
</template>

<script>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import { printFn } from '@/utils/print'
import jmPrint from '@/components/print/index'
import VoiceUtils from '@/utils/voice'

export default {
  name: 'singleIn',
  data () {
    return {
      submitLoading: false,
      activeNoTypeName: localStorage.getItem(this.$store.state.user.id + '-label-scan-type-name') || '运单号',
      dataForm: {
        no: '',
        scanNoType: localStorage.getItem(this.$store.state.user.id + '-label-scan-type-value') || 20
      },
      // 上一次已经打印的单号
      previousPrintNo: localStorage.getItem(this.$store.state.user.id + '-label-last-print-no') || null,
      checked: false,
      oldPrintNew: false,
      // 截断单号
      // 订单物流类型：10=小包 / 11=快递 /12=FBA
      orderLogisticsType: 10,
      printUrl: '/ws/changelabelprintorder/printSubLabel?no=',
      isSuccess: true,
      responseMsg: '',
      baiduVoiceToken: '',
      scanNoTypeList: [
        { 'value': 10, 'name': '客户单号' },
        { 'value': 20, 'name': '运单号' },
        { 'value': 30, 'name': '派送单号' }
      ]
    }
  },
  activated () {
    this.getBaiduVoiceToken()
    this.$nextTick(function () {
      this.$refs.dataFormNo.focus()
      this.$refs.jmPrint.createPrint()
    })
  },
  created () {
    this.getDefaultMasterBusiness().then((defaultMasterBusinessVal) => {
      console.log('defaultMasterBusinessVal', defaultMasterBusinessVal)
      this.orderLogisticsType = Number(defaultMasterBusinessVal) || 10
      this.radioBtnChangeHandle()
    })
  },
  methods: {
    disabledScanNoTypeHandle (item) {
      if (this.oldPrintNew) {
        if (this.orderLogisticsType !== 10 && item.value !== 60) {
          return true
        } else if (this.orderLogisticsType === 10 && item.value !== 30) {
          return true
        } else {
          return false
        }
      } else {
        if (this.orderLogisticsType !== 10 && (item.value === 10 || item.value === 20 || item.value === 30 || item.value === 70)) {
          return true
        } else if (this.orderLogisticsType === 10 && (item.value === 50 || item.value === 60)) {
          return true
        } else {
          return false
        }
      }
    },
    // 获取主业务类型
    getDefaultMasterBusiness () {
      return new Promise((resolve, reject) => {
        this.$http.get(`/sys/params/getValueByCode/WAYBILL_SEARCH_DEFAULT_TAB`).then(({ data: res }) => {
          if (res.code !== 0) {
            resolve(this.$message.error(res.msg))
          }
          resolve(res.data)
        }).catch(() => {
          resolve(null)
        })
      })
    },
    // 单号类型改变
    changeNoType (item) {
      this.$nextTick(() => {
        localStorage.setItem(this.$store.state.user.id + '-label-scan-type-name', item.name)
        localStorage.setItem(this.$store.state.user.id + '-label-scan-type-value', item.value)
        this.dataForm.scanNoType = item.value
        this.activeNoTypeName = item.name
        if (this.dataForm.no) {
          this.$refs.dataFormNo.select()
        } else {
          this.$refs.dataFormNo.focus()
        }
      })
    },
    radioBtnChangeHandle () {
      console.log('radioBtnChangeHandle')
      console.log('this.dataForm.scanNoType', this.dataForm.scanNoType)
      console.log('this.orderLogisticsType', this.orderLogisticsType)
      if (this.orderLogisticsType !== 10 && (Number(this.dataForm.scanNoType) === 10 ||
        Number(this.dataForm.scanNoType) === 20 || Number(this.dataForm.scanNoType) === 30)) {
        console.log('radioBtnChangeHandle in 1')
        this.$nextTick(() => {
          console.log('radioBtnChangeHandle in 2')
          this.dataForm.scanNoType = this.scanNoTypeList[this.scanNoTypeList.length - 1].value
          this.activeNoTypeName = this.scanNoTypeList[this.scanNoTypeList.length - 1].name
          localStorage.setItem(this.$store.state.user.id + '-label-scan-type-name', this.activeNoTypeName)
          localStorage.setItem(this.$store.state.user.id + '-label-scan-type-value', this.dataForm.scanNoType)
        })
      } else if (this.orderLogisticsType === 10 && (Number(this.dataForm.scanNoType) === 50 || Number(this.dataForm.scanNoType) === 60)) {
        this.dataForm.scanNoType = this.scanNoTypeList[1].value
        this.activeNoTypeName = this.scanNoTypeList[1].name
        localStorage.setItem(this.$store.state.user.id + '-label-scan-type-name', this.activeNoTypeName)
        localStorage.setItem(this.$store.state.user.id + '-label-scan-type-value', this.dataForm.scanNoType)
      }
    },
    getBaiduVoiceToken () {
      this.$http.get('/ws/combaiduvoice/getBaiduVoiceToken').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.baiduVoiceToken = res.data
      }).catch(() => {})
    },
    failCancel () {
      // 静默打印失败操作
    },
    failOk () {
      // 静默打印失败后，点击确定后 操作
    },
    printOver () {
      // 静默打印完毕之后，后续操作
    },
    async previousPrintNoConfirm () {
      let confirmRes = await this.$confirm(
        '该单号上一次已经打印过了, 确定继续打印吗?',
        '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          // 阻止回车事件处理
          beforeClose (action, instance, done) {
            if (action === 'confirm') {
              instance.$refs['confirm'].$el.onclick = (function (e) {
                e = e || window.event
                if (e.detail !== 0) {
                  return done()
                }
              })()
            } else {
              done()
            }
          }
        }
      ).catch(err => console.log(err))
      if (confirmRes !== 'confirm') {
        return false
      } else {
        return true
      }
    },
    /**
     * 打印
     * @return {Promise<ElMessageComponent>}
     */
    async print () {
      if (this.submitLoading) {
        return this.$message.error('请求中')
      }
      this.submitLoading = true
      let continuePrint = true
      if (this.previousPrintNo && this.previousPrintNo === this.dataForm.no) {
        continuePrint = await this.previousPrintNoConfirm()
      }
      if (!continuePrint) {
        this.$refs.dataFormNo.select()
        this.submitLoading = false
        return this.$message.info('打印已取消')
      }
      this.$refs['dataForm'].validate((valid, object) => {
        if (!valid) {
          if (object.no !== undefined) {
            this.$refs.dataFormNo.focus()
          }
          this.submitLoading = false
          return false
        }
        let reqObj = {
          'no': this.dataForm.no,
          'scanNoType': this.dataForm.scanNoType,
          'orderLogisticsType': this.orderLogisticsType,
          'oldPrintNew': this.oldPrintNew ? 1 : 0
        }
        this.$http['post'](`/ws/changelabelprintorder/printLabelByScan`, reqObj).then(({ data: res }) => {
          let no = this.dataForm.no
          this.submitLoading = false
          this.$nextTick(function () {
            this.dataForm.no = ''
            this.$refs.dataFormNo.focus()
          })
          if (res.code !== 0) {
            VoiceUtils.playByBaidu('打印失败', this.baiduVoiceToken)
            this.isSuccess = false
            this.responseMsg = res.msg
            return this.$message.error(res.msg)
          }
          this.previousPrintNo = this.dataForm.no
          localStorage.setItem(this.$store.state.user.id + '-label-last-print-no', this.previousPrintNo)
          this.isSuccess = true
          this.responseMsg = res.msg
          this.$message.success(res.msg)
          VoiceUtils.playByBaidu('成功', this.baiduVoiceToken)
          // 判断是否需要预览
          if (this.checked) {
            window.open(res.data)
          } else {
            // 静默打印
            this.$refs.jmPrint.jmPrint(no, res.data)
          }
        }).catch(() => {
        })
      })
    }
  },
  computed: {
    dataRule () {
      return {
        no: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }]
      }
    }
  },
  components: {
    jmPrint,
    Splitpanes,
    Pane,
    printFn
  }
}
</script>
<style lang="scss" scoped>
  @import '~@/assets/scss/splitpanes.scss';
  .parcel-bagging-wrap{
    /deep/ .form-card{
      padding: 15px;
    }
    /deep/ .el-checkbox__label{
      font-size: 12px;
    }
    /deep/ .el-form-item__label, .el-checkbox, .el-radio{
      color: #FFF;
      font-size: 32px;
      margin:15px 0;
    }
    /deep/ .el-form-item__label:after{
      content: '';
    }
  }
  .submitBtn {
    margin-top: 20px;
    text-align: center;
    min-width: 120px;
    padding: 10px 30px;
    text-align-last: justify;
  }
  /deep/ .el-input.el-input--medium .el-input__inner {
    height: 86px !important;
    line-height: 50px !important;
    width: 110% !important;
  }
  /deep/ .el-form-item__error {
    font-size: 18px !important;
    font-weight: bolder !important;
  }
  /deep/ .el-checkbox__input.is-checked+.el-checkbox__label {
    color: white !important;
  }
  .el-radio-group {

  }
  ::v-deep .el-radio-group{
    width: 110% !important;
    .el-radio-button {
      width: 50%!important;
      .el-radio-button__inner {
        width: 100%!important;
        padding-left: 0!important;
        padding-right: 0!important;
        text-align: center;
        font-size: large;
        color: #DCDFE6;
      }
    }
  }
  ::v-deep .el-radio-button:first-child .el-radio-button__inner {
    border-left: 1px solid #DCDFE6;
    border-radius: 4px 0 0 0 !important;
    width: 100% !important;
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
  }
  ::v-deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0 4px 0 0 !important;
    width: 100%;
  }
  ::v-deep .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    color: #FFF;
    background-color: #409EFF;
    border-color: #409EFF;
    width: 100% !important;
    -webkit-box-shadow: -1px 0 0 0 #409eff;
    box-shadow: -1px 0 0 0 #409eff;
  }
  ::v-deep .el-input__inner {
    border-radius: 0 0 5px 5px !important;
  }
  ::v-deep input::-webkit-input-placeholder {
    font-size:27px;

  }
  ::v-deep input::-moz-input-placeholder {
    font-size:27px;
  }
  ::v-deep input::-ms-input-placeholder {
    font-size:27px;
  }
  .light-blue {
    color: #d9ecff;
  }
</style>
