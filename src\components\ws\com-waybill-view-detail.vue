<template>
  <div class="add-body panel_body">
    <el-tabs class="no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
      <el-tab-pane :label="$t('tabPane.waybill')" name="waybill" >
      <div class="panel_body">
        <el-form ref="form" label-width="130px">
          <h2 class="detail-title">
            <span v-text="$t('header.waybill')"></span>
            <el-link v-show="dataForm.logisticsType === 12" style="margin-left:20px;" :underline="false" @click="copyInboundInfoHandle($event)">{{ $t('wsComWaybill.copyInboundInfo') }}</el-link>
          </h2>
          <div class="detail-desc ">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.customerOrderNo')">
                  <span v-text="dataForm.customerOrderNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.waybillNo')">
                  <span v-text="dataForm.waybillNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.postalTrackingNo')">
                  <span v-text="dataForm.postalTrackingNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.deliveryNo')">
                  <span v-text="dataForm.deliveryNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.consigneeCountry')">
                  <span v-text="formatterValue(dataForm.consigneeCountry,'country')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="dataForm.logisticsType === 12">
                <el-form-item :label="$t('fba.warehouseCode')">
                  <span v-text="dataForm.fbaWarehouseCode"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.logisticsProductCode')">
                  <span v-text="formatterValue(dataForm.logisticsProductCode,'product')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.logisticsChannelCode')">
                  <span v-text="formatterValue(dataForm.logisticsChannelCode,'channel')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.providerCode')">
                  <span v-text="formatterValue(dataForm.providerId,'provider')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.electric')">
                  <span v-text="formatterValue(dataForm.electric,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.magnetized')">
                  <span v-text="formatterValue(dataForm.magnetized,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.liquid')">
                  <span v-text="formatterValue(dataForm.liquid,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.powder')">
                  <span v-text="formatterValue(dataForm.powder,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.remote')">
                  <span v-text="formatterValue(dataForm.remote,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.parcelType')">
                  <span v-text="formatterValue(dataForm.parcelType,'parcelType')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.goodsCategory')">
                  <span v-text="formatterValue(dataForm.goodsCategory,'goodsCategory')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.packageQty')">
                  <span v-text="dataForm.packageQty"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.arrearage')">
                  <span v-text="formatterValue(dataForm.arrearage,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.exceptionFlag')">
                  <span v-text="formatterValue(dataForm.exceptionFlag,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.plateformType')">
                  <span v-text="dataForm.plateformType"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.iossTaxType')" prop="shipper.iossTaxType">
                  <span v-text="formatterValue(dataForm.shipper.iossTaxType,'shipper.iossTaxType')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.iossNo')" prop="shipper.iossNo">
                  <span v-text="dataForm.shipper.iossNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.vatNo')" prop="shipper.vatNo">
                  <span v-text="dataForm.shipper.vatNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.eoriNo')" prop="shipper.eoriNo">
                  <span v-text="dataForm.shipper.eoriNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.declareCurrency')">
                  <span v-text="formatterValue(dataForm.declareCurrency,'declareCurrency')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.insuredAmount')">
                  <span v-text="dataForm.insuredAmountD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.insuredCurrency')">
                  <span v-text="formatterValue(dataForm.insuredCurrency,'insuredCurrency')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.codAmount')">
                  <span v-text="dataForm.codAmountD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.codCurrency')">
                  <span v-text="formatterValue(dataForm.codCurrency,'codCurrency')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customsMethod')">
                  <span v-text="formatterValue(dataForm.customsMethod,'customsMethod')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.taxPayMode')">
                  <span v-text="formatterValue(dataForm.taxPayMode,'taxPayMode')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="dataForm.taxPayMode !== 11">
                <el-form-item :label="$t('coOrder.taxPayAccount')">
                  <span v-text="dataForm.taxPayAccount"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.shopName')">
                  <span v-text="dataForm.shopName"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item >
                  <template slot="label">
                    <span>{{$t('fba.forecastWeight')}}({{dataForm.standardUnit === 1? 'LB' : 'KG'}})</span>
                  </template>
                  <span v-text="dataForm.forecastWeightD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item >
                  <template slot="label">
                    <span>{{$t('wsComWaybill.forecastVolumeWeight')}}</span>
                  </template>
                  <span v-text="dataForm.forecastVolumeWeightD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.weight')">
                  <span v-text="dataForm.weightD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.measureWeightD')">
                  <span v-text="dataForm.inOperateWeightD || dataForm.weightD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.volumeWeight')">
                  <span v-text="dataForm.volumeWeightD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.volume')">
                  <span v-text="dataForm.volumeD.toFixed(3)"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.inPackageQty')">
                  <span v-text="dataForm.inPackageQty"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.outPackageQty')">
                  <span v-text="dataForm.outPackageQty"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="false">
                <el-form-item :label="$t('wsComWaybill.volume')" >
                  <span>{{dataForm.lengthD === null ? 0: dataForm.lengthD}}*{{dataForm.widthD ===null ? 0: dataForm.widthD}}*{{dataForm.heightD ===null ? 0: dataForm.heightD}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.status')" prop="status">
                  <span v-text="formatterValue(dataForm.status,'status')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.warehouseId')">
                  <span v-text="formatterValue(dataForm.warehouseId,'warehouse')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.createDate')">
                  <span v-text="dataForm.createDate"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.customerRemark')">
                  <span style="color:red;" v-text="dataForm.customerRemark"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.inRemark')">
                  <span style="color:red;" v-text="dataForm.inRemark"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="图片">
                  <el-image v-if='dataForm.thumbnail' style="width: 40px; height: 30px; display:flex;" :src="dataForm.thumbnail" :preview-src-list="dataForm.images.map(img => img.url)"></el-image>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <el-divider />
          <h2 class="detail-title">
            <span v-text="$t('header.customer')"></span>
          </h2>
          <div class="detail-desc">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.objectName')">
                  <span v-text="dataForm.objectNameDesensitized"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.objectCode')">
                  <span v-text="dataForm.objectCode"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.objectType')">
                  <span v-text="formatterValue(dataForm.objectType,'objectType')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.customerRemark')">
                  <span v-text="dataForm.customerRemark"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.serviceId')">
                  <span v-text="formatterValue(dataForm.serviceId,'creator')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.salesmanId')">
                  <span v-text="formatterValue(dataForm.salesmanId,'creator')"></span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <!--        <el-divider />-->
          <!--        <h2 class="detail-title">-->
          <!--          <span v-text="$t('header.remark')"></span>-->
          <!--        </h2>-->
          <!--        <div class="detail-desc">-->
          <!--          <el-row type="flex" justify="start">-->
          <!--            <el-col :span="10">-->
          <!--              <el-form-item :label="$t('wsComWaybill.remark')" prop="remark">-->
          <!--                <el-input  v-model="remark" type="textarea" :rows="2" :placeholder="$t('wsComWaybill.remark')"></el-input>-->
          <!--              </el-form-item>-->
          <!--            </el-col>-->
          <!--          </el-row>-->
          <!--          <el-row type="flex" justify="start">-->
          <!--            <el-col :offset="1">-->
          <!--              <el-button type="primary" @click="operateLogSubmitHandler">提交</el-button>-->
          <!--            </el-col>-->
          <!--          </el-row>-->
          <!--        </div>-->
        </el-form>
      </div>
    </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.shipperConsignee')" name="shipperConsignee">
      <div class="panel_body">
      <el-form ref="form" label-width="100px">
        <h2 class="detail-title">
          <span v-text="$t('wsComWaybill.consignee')"></span>
        </h2>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeName')" prop="consigneeName">
              <span v-text="dataForm.consignee.consigneeName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="dataForm.logisticsType === 12">
            <el-form-item :label="$t('fba.warehouseCode')" prop="fbaWarehouseCode">
              <span v-text="dataForm.consignee.fbaWarehouseCode"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeCompany')" prop="consigneeCompany">
              <span v-text="dataForm.consignee.consigneeCompany"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneePhone')" prop="consigneePhone">
              <span v-text="dataForm.consignee.consigneePhone"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeCountry')" prop="consigneeCountry">
              <span v-text="formatterValue(dataForm.consignee.consigneeCountry,'country')"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeProvince')" prop="consigneeProvince">
              <span v-text="dataForm.consignee.consigneeProvince"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeCity')" prop="consigneeCity">
              <span v-text="dataForm.consignee.consigneeCity"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item :label="$t('wsComWaybill.consigneeAddress')" prop="consigneeAddress">
              <span v-text="dataForm.consignee.consigneeAddress"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneePostcode')" prop="consigneePostcode">
              <span v-text="dataForm.consignee.consigneePostcode"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeEmail')" prop="consigneeEmail">
              <span v-text="dataForm.consignee.consigneeEmail"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeDistrict')" prop="consigneeDistrict">
              <span v-text="dataForm.consignee.consigneeDistrict"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeDoorplate')" prop="consigneeDoorplate">
              <span v-text="dataForm.consignee.consigneeDoorplate"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeStreet')" prop="consigneeStreet">
              <span v-text="dataForm.consignee.consigneeStreet"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('coOrderConsignee.consigneeTaxNo')" prop="consigneeTaxNo">
              <span v-text="dataForm.consignee.consigneeTaxNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComWaybill.consigneeIdcard')" prop="consigneeIdcard">
              <span v-text="dataForm.consignee.consigneeIdcard"></span>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item :label="dataForm.logisticsType == 10 ? $t('wsComWaybill.consigneeIdcard') : $t('fba.vatNo')" prop="consigneeIdcard">
              <span v-text="dataForm.consignee.consigneeIdcard"></span>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-divider />
        <div v-if="dataForm.logisticsType === 10">
          <h2 class="detail-title">
            <span v-text="$t('wsComWaybill.shipper')"></span>
          </h2>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperName')" prop="shipperName">
                <span v-text="dataForm.shipper.shipperName"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperCompany')" prop="shipperCompany">
                <span v-text="dataForm.shipper.shipperCompany"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperPhone')" prop="shipperPhone">
                <span v-text="dataForm.shipper.shipperPhone"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperCountry')" prop="shipperCountry">
                <span v-text="formatterValue(dataForm.shipper.shipperCountry,'country')"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperProvince')" prop="shipperProvince">
                <span v-text="dataForm.shipper.shipperProvince"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperCity')" prop="shipperCity">
                <span v-text="dataForm.shipper.shipperCity"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperDistrict')" prop="shipperDistrict">
                <span v-text="dataForm.shipper.shipperDistrict"></span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item :label="$t('wsComWaybill.shipperAddress')" prop="shipperAddress">
                <span v-text="dataForm.shipper.shipperAddress"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperPostcode')" prop="shipperPostcode">
                <span v-text="dataForm.shipper.shipperPostcode"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperEmail')" prop="shipperEmail">
                <span v-text="dataForm.shipper.shipperEmail"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperDoorplate')" prop="shipperDoorplate">
                <span v-text="dataForm.shipper.shipperDoorplate"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('wsComWaybill.shipperStreet')" prop="shipperStreet">
                <span v-text="dataForm.shipper.shipperStreet"></span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      </div>
    </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.declare')" name="declare" v-if="declareTabShow">
      <div class="panel_body">
      <div class="flex_table" ref="tableElm" v-domResize="redraw">
        <ux-grid ref="declareDataList" size="mini" :widthResize="true" :border="false" :show-overflow="true"
                 show-summary :summary-method="getDeclareTableSummaries">
          <ux-table-column field="serialNo" title="序号" type="index" width="50"></ux-table-column>
          <ux-table-column v-for="(item, index) in declareTableColumnsArr"
                           :key="index"
                           :field="item.prop"
                           :title="item.label"
                           header-align="center"
                           :align="item.align"
                           :min-width="item.width"
                           :resizable="true">
            <template slot-scope="scope">
              <div>
                <div v-if="item.prop === 'createDate'">
                  <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                    <span>{{formatterFn(scope,item.prop)}}</span>
                  </el-tooltip>
                </div>
                <div v-else-if="item.prop === 'picUrl'">
                  <el-image v-if='scope.row.picUrl' style="width: 5vw; height: 5vh;" fit="scale-down"  :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" >
                    <div slot="placeholder" class="image-slot">
                      加载中<span class="dot">...</span>
                    </div>
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </div>
                <div v-else>
                  {{formatterFn(scope,item.prop)}}
                </div>
              </div>
            </template>
          </ux-table-column>
        </ux-grid>
      </div>
      </div>
    </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.package')" name="package" @click="getPackageList()">
      <div class="panel_body">
      <div class="flex_table" ref="tableElm" v-domResize="redraw">
        <ux-grid ref="packageDataList" size="mini" :widthResize="true" :border="false" :show-overflow="true"
                 show-summary :summary-method="getPackageTableSummaries">
          <ux-table-column field="serialNo" title="序号" type="index" width="50"></ux-table-column>
          <ux-table-column v-for="(item, index) in packageTableColumnsArr"
                           :key="index"
                           :field="item.prop"
                           :title="item.label"
                           header-align="center"
                           :align="item.align"
                           :min-width="item.width"
                           :resizable="true">
            <template slot-scope="scope">
              <div>
                <div v-if="item.prop === 'createDate'">
                  <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                    <span>{{formatterFn(scope,item.prop)}}</span>
                  </el-tooltip>
                </div>
                <div v-else-if="item.prop === 'size'">
                    <span>{{scope.row.lengthD}}*{{scope.row.widthD}}*{{scope.row.heightD}}</span>
                </div>
                <div v-else-if="item.prop === 'weightD' || item.prop === 'volumeWeightD' || item.prop === 'volD'">
                    <span>{{scope.row[item.prop].toFixed(3)}}</span>
                </div>
                <div v-else-if="item.prop === 'lwd' || item.prop === 'l2wd'">
                    <span>{{scope.row[item.prop].toFixed(1)}}</span>
                </div>
                <div v-else-if="item.prop === 'picList'">
                  <div>
                    <el-image style="width: 5vw; height: 5vh;" fit="scale-down"
                              v-for="(item, index) in scope.row.picList" :src="item" :preview-src-list="[item]" :key="index">
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
                <div v-else>
                  {{formatterFn(scope,item.prop)}}
                </div>
              </div>
            </template>
          </ux-table-column>
        </ux-grid>
      </div>
      </div>
    </el-tab-pane>

      <el-tab-pane v-if="dataForm.logisticsType === 12" :label="$t('tabPane.reference')" name="reference">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" v-loading="referenceTableLoading" :data="referenceDataList" border>
            <!-- 动态显示表格 -->
            <el-table-column :label="$t('system.index')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in referenceTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

    <el-tab-pane :label="$t('tabPane.exception')" name="exception" v-if="exceptionTabShow">
      <div class="flex_table panel_body" ref="tableElm" v-domResize="redraw">
        <el-table v-loading="dataListLoading" :data="wsComExceptionBillDTOList" border>
          <!-- 动态显示表格 -->
          <el-table-column v-for="(item, index) in wsComExceptionBillTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
            <template slot-scope="scope">
              <div>
                <div v-if="item.prop === 'createDate'">
                  <span>{{formatterFn(scope,item.prop)}}</span>
                </div>
                <div v-else-if="item.prop === 'status'">
                  {{formatterFn(scope,'exceptionStatus')}}
                </div>
                <div v-else>
                  {{formatterFn(scope,item.prop)}}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.operateLog')" name="operateLog" v-if="operateLogTabShow">
      <div class="panel_body">
        <el-table v-loading="dataListLoading" :data="wsComWaybillOperateLogDTOList" border>
        <!-- 动态显示表格 -->
        <el-table-column v-for="(item, index) in wsComWaybillOperateLogTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
          <template slot-scope="scope">
            <div>
              <div v-if="item.prop === 'createDate'">
                <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                  <span>{{formatterFn(scope,item.prop)}}</span>
                </el-tooltip>
              </div>
              <div v-else>
                {{formatterFn(scope,item.prop)}}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>
    </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.sortRate')" name="sortRate" v-if="sortRateTabShow">
        <div class="panel_body">
          <el-table v-loading="dataListLoading" :data="wsComWaybillSortRateLogDTOList" border>
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in wsComWaybillSortRateLogTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'status'">
                    <el-badge is-dot class="badge_status" v-if="scope.row.status === 2" type="warning"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 0" type="success"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 3" type="primary"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 1" type="info"></el-badge>
                    <span>{{ formatterSortFn(scope,item.prop) }}</span>
                  </div>
                  <div v-else-if="item.prop === 'message'">
                    <el-tooltip placement="top">
                      <div slot="content" style="max-width: 400px;">{{scope.row.message}}</div>
                      <span class="text-overflow">{{formatterSortFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterSortFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('fba.attachment')" name="attachments">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" v-loading="attachmentsTableLoading" :data="attachmentsList" border>
            <!-- 动态显示表格 -->
            <el-table-column :label="$t('system.serialNumber')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in attachmentsTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'name'">
                    <el-link :underline="false" class="el-icon-download" @click="downloadFile(scope.row.attachmentUrl, scope.row.name)"><span style="margin-left: 5px;">{{ scope.row.name }}</span></el-link>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="attachmentsPage" :page-sizes="[10, 20, 50, 100]" :page-size="attachmentsLimit" :total="attachmentsTotal"
                       layout="total, sizes, prev, pager, next, jumper" @size-change="attachmentsPageSizeChangeHandle" @current-change="attachmentsPageCurrentChangeHandle">
        </el-pagination>
      </el-tab-pane>
    <!--<el-tab-pane :label="$t('tabPane.track')" name="track">
    </el-tab-pane>-->
    </el-tabs>
    <div v-if="!$route.query.id" id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import { saveAs } from 'file-saver'
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import clip from '@/utils/clipboard'
import {
  formatterType,
  gtmToLtm,
  timestampFormat,
  formatterUser,
  formatterShowName,
  formatterCodeName,
  formatterName,
  formatterCode,
  numberFormat,
  formatterCodeNativeName
} from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'

export default {
  props: {
    declareTabShow: {
      type: Boolean,
      default: true
    },
    exceptionTabShow: {
      type: Boolean,
      default: true
    },
    operateLogTabShow: {
      type: Boolean,
      default: true
    },
    sortRateTabShow: {
      type: Boolean,
      default: true
    }
  },
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      activeName: 'waybill',
      wsComExceptionBillDTOList: [],
      wsComWaybillOperateLogDTOList: [],
      wsComWaybillSortRateLogDTOList: [],
      wsComWaybillOperateLogTableColumns: [
        { type: '', width: '150', prop: 'waybillId', label: this.$t('wsComWaybillOperateLog.waybillId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '120', prop: 'operateNode', label: this.$t('wsComWaybillOperateLog.operateNode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '450', prop: 'opeareteDescription', label: this.$t('wsComWaybillOperateLog.opeareteDescription'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '120', prop: 'creator', label: this.$t('system.creator'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '130', prop: 'createDate', label: this.$t('system.createDate'), align: 'center', isShow: true, disabled: false }
      ],
      wsComWaybillSortRateLogTableColumns: [
        { type: '', width: '10', prop: 'id', label: this.$t('comInSubWaybill.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '140', prop: 'channelRouterId', label: this.$t('baSortRateLog.channelRouterId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'type', label: this.$t('baSortRateLog.type'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsChannelCode', label: this.$t('baSortRateLog.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'providerChannelCode', label: this.$t('baSortRateLog.providerChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'currency', label: this.$t('baSortRateLog.currency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'zone', label: this.$t('baSortRateLog.zone'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'payableSumD', label: this.$t('baSortRateLog.payableSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'balanceWeightD', label: this.$t('baSortRateLog.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeightUnit', label: this.$t('baSortRateLog.balanceWeightUnit'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'message', label: this.$t('baSortRateLog.message'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('baSortRateLog.createDate'), align: 'center', isShow: true, disabled: false }
      ],
      inboundInfo: '',
      remark: '',
      dataListLoading: false,
      wsComExceptionBillTableColumns: [
        { type: '', width: '150', prop: 'waybillId', label: this.$t('wsComExceptionBill.waybillId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('wsComExceptionBill.waybillNo'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'warehouseId', label: this.$t('wsComExceptionBill.warehouseId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'exceptionDescription', label: this.$t('wsComExceptionBill.exceptionDescription'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'assignedCustomer', label: this.$t('wsComExceptionBill.assignedCustomer'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'status', label: this.$t('wsComExceptionBill.processStatus'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'creator', label: this.$t('wsComExceptionBill.creator'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'processUser', label: this.$t('wsComExceptionBill.processUser'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'processTime', label: this.$t('wsComExceptionBill.processTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'processSchemeCode', label: this.$t('wsComExceptionBill.processSchemeCode'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'processSchemeName', label: this.$t('wsComExceptionBill.processSchemeName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'processRemark', label: this.$t('wsComExceptionBill.processRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'operationStatus', label: this.$t('wsComExceptionBill.operationStatus'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'operator', label: this.$t('wsComExceptionBill.operator'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'operationTime', label: this.$t('wsComExceptionBill.operationTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'delFalg', label: this.$t('wsComExceptionBill.delFalg'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'deleteUser', label: this.$t('wsComExceptionBill.deleteUser'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'deleteTime', label: this.$t('wsComExceptionBill.deleteTime'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('system.createDate'), align: 'center', isShow: true, disabled: false }
      ],
      dataForm: {
        id: '',
        mergeWaybillId: '',
        creator: '',
        createDate: '',
        fbaWarehouseCode: '',
        updater: '',
        updateDate: '',
        status: '',
        mergeType: '',
        version: '',
        orderId: '',
        customerOrderNo: '',
        standardUnit: '',
        declareCurrency: '',
        waybillNo: '',
        deliveryNo: '',
        logisticsProductCode: '',
        logisticsChannelCode: '',
        logisticsType: '',
        providerCode: '',
        forecastWeightD: '',
        weightD: '',
        volumeWeightD: '',
        inOperateWeightD: '',
        lengthD: '',
        widthD: '',
        heightD: '',
        inAdjustWeightD: '',
        outAdjustWeightD: '',
        inPackageQty: 0,
        outPackageQty: 0,
        weightUnit: '',
        storageLocation: '',
        goodsCategory: '',
        parcelType: '',
        electric: '',
        magnetized: '',
        liquid: '',
        powder: '',
        remote: '',
        consigneeCountry: '',
        customerRemark: '',
        serviceId: '',
        salesmanId: '',
        objectId: '',
        objectName: '',
        objectNameDesensitized: '',
        objectCode: '',
        objectType: '',
        warehouseId: '',
        plateformType: '',
        packageQty: '',
        exceptionFlag: '',
        arrearage: '',
        shipper: {
          shipperName: '',
          shipperCompany: '',
          shipperPhone: '',
          shipperEmail: '',
          shipperCountry: 'CN',
          shipperProvince: '',
          shipperCity: '',
          shipperDistrict: '',
          shipperAddress: '',
          shipperPostcode: '',
          shipperDoorplate: '',
          shipperStreet: ''
        },
        consignee: {
          consigneeName: '',
          consigneeCompany: '',
          consigneePhone: '',
          consigneeEmail: '',
          consigneeProvince: '',
          consigneeCity: '',
          consigneeDistrict: '',
          consigneeAddress: '',
          consigneePostcode: '',
          consigneeDoorplate: '',
          consigneeStreet: '',
          consigneeIdcard: ''
        }
      },
      declareDataList: [],
      declareTableColumns: [
        { type: '', width: '80', prop: 'chineseName', label: this.$t('wsComWaybill.declare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'englishName', label: this.$t('wsComWaybill.declare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'quantity', label: this.$t('wsComWaybill.declare.quantity'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '110', prop: 'unitDeclarePriceD', label: this.$t('wsComWaybill.declare.unitDeclarePrice'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '110', prop: 'declareSumD', label: this.$t('wsComWaybill.declare.declareSum'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'hsCode', label: this.$t('wsComWaybill.declare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'unitNetWeightD', label: this.$t('fba.unitNetWeight'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'goodsBarcode', label: this.$t('wsComWaybill.declare.goodsBarcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'brand', label: this.$t('wsComWaybill.declare.brand'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'sku', label: this.$t('wsComWaybill.declare.sku'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'productModel', label: this.$t('wsComWaybill.declare.productModel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'material', label: this.$t('wsComWaybill.declare.material'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'purpose', label: this.$t('wsComWaybill.declare.purpose'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'origin', label: this.$t('wsComWaybill.declare.origin'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'pickingRemark', label: this.$t('wsComWaybill.declare.pickingRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'productUrl', label: this.$t('wsComWaybill.declare.productUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'picUrl', label: this.$t('wsComWaybill.declare.picUrl'), align: 'center', isShow: true, disabled: false }
      ],
      packageTableColumns: [
        { type: '', width: '150', prop: 'packageNo', label: this.$t('coOrderPackage.packageCustomerNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'subCustomerOrderNo', label: this.$t('coOrderPackage.subCustomerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'deliveryNo', label: this.$t('coOrderPackage.packageDeliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'weightD', label: this.$t('baPayableBillFee.weightD'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'volumeWeightD', label: this.$t('baSubBillsReceivable.volumeWeightD'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'volD', label: '方数', align: 'right', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'size', label: this.$t('coOrderPackage.size'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'l2wd', label: this.$t('coOrderPackage.l2wd'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'lwd', label: this.$t('coOrderPackage.lwd'), align: 'right', isShow: true, disabled: false },
        // { type: '', width: '150', prop: 'lengthD', label: this.$t('fba.length'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '150', prop: 'widthD', label: this.$t('fba.width'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '150', prop: 'heightD', label: this.$t('fba.height'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'inTime', label: this.$t('coOrderPackage.inTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packTime', label: this.$t('coOrderPackage.packTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'outTime', label: this.$t('coOrderPackage.outTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'channelLabelUrl', label: this.$t('coOrderPackage.channelLabelUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'picList', label: this.$t('coOrderPackage.picList'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }],
      referenceTableColumns: [
        { type: '', width: '150', prop: 'fbaNo', label: this.$t('coOrderFbaReference.fbaNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'referenceId', label: this.$t('coOrderFbaReference.referenceId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageQty', label: this.$t('coOrderFbaReference.packageQty'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      referenceTableLoading: false,
      referenceDataList: [],
      attachmentsPage: 1,
      attachmentsLimit: 10,
      attachmentsTotal: 0,
      attachmentsTableLoading: false,
      attachmentsList: [],
      attachmentsTableColumns: [
        { type: '', width: '150', prop: 'name', label: this.$t('bdAttachments.file'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'attachmentUrl', label: this.$t('bdAttachments.attachmentUrl'), align: 'center', isShow: false, disabled: false }
      ],
      // 数据字典
      comWaybillLogOperateNodeList: [],
      parcelTypeList: [],
      goodsCategoryList: [],
      yesOrNoList: [],
      iossTypeList: [],
      currencyList: [],
      taxPayModeList: [],
      customsMethodList: [],
      statusList: [],
      sortRateStatusList: [],
      objectTypeList: [],
      exceptionStatusList: [],
      // 基础资料
      logisticsProductByParamsList: [],
      logisticsChannelByParamsList: [],
      channelRouterList: [],
      packageDataList: [],
      warehouseInfoList: [],
      weightUnitList: [],
      quotationSourceList: [],
      userList: [],
      countryList: [],
      providerList: []
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
    // 获取数据字典
    this.getDict()
    // 若路由跳转过来的
    let id = this.$route.query.id
    if (id) {
      this.dataForm.id = id
      this.init()
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getWaybill()
        }
      })
    },
    async getBaseData () {
      this.channelRouterList = await baseData(baseDataApi.logisticschannelrouteList).catch(() => {})
      this.logisticsProductByParamsList = await baseData(baseDataApi.logisticsProductByParamsList).catch(() => {})
      this.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByParamsList).catch(() => {})
      this.warehouseInfoList = await baseData(baseDataApi.warehouseInfoList).catch(() => {})
      this.countryList = await baseData(baseDataApi.countryList)
      this.currencyList = await baseData(baseDataApi.currencyList)
      this.userList = await baseData(baseDataApi.allUserList).catch(() => {})
      this.providerList = await baseData(baseDataApi.providerList).catch(() => {})
    },
    async getDict () {
      this.comWaybillLogOperateNodeList = await this.getDictTypeList('ComWaybillLogOperateNode')
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
      this.iossTypeList = await this.getDictTypeList('IossType')
      this.taxPayModeList = await this.getDictTypeList('OrderTaxPayMode')
      this.customsMethodList = await this.getDictTypeList('OrderCustomsMethod')
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType')
      this.goodsCategoryList = await this.getDictTypeList('OrderGoodsCategory')
      this.statusList = await this.getDictTypeList('WsComWaybillStatus')
      this.sortRateStatusList = await this.getDictTypeList('baSortRateStatus')
      this.objectTypeList = await this.getDictTypeList('wsComWaybillObjectType')
      // 仓库异常状态
      this.exceptionStatusList = await this.getDictTypeList('wsComExceptionBillStatus')
      this.getDictTypeList('weightUnit').then(res => {
        this.weightUnitList = res
      })
      this.quotationSourceList = await this.getDictTypeList('channelQuotationSource') // 渠道报价来源
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        // case 'createDate':
        //   value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD hh:mm:ss')
        //   break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'exceptionStatus':
          value = formatterType(scope.row.status, this.exceptionStatusList)
          break
        case 'creator':
          value = formatterUser(scope.row.creator, this.userList)
          if (value === undefined || value === '') {
            value = '客户'
          }
          break
        case 'operateNode':
          value = formatterType(scope.row.operateNode, this.comWaybillLogOperateNodeList)
          break
        case 'processUser':
          value = formatterUser(scope.row.processUser, this.userList)
          break
        case 'operator':
          value = formatterUser(scope.row.operator, this.userList)
          break
        case 'origin':
          value = formatterCodeName(scope.row.origin, this.countryList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeName(scope.row.logisticsChannelCode, this.logisticsChannelByParamsList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    formatterSortFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsChannelCode':
          value = formatterCodeName(scope.row.logisticsChannelCode, this.logisticsChannelByParamsList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.sortRateStatusList)
          break
        case 'balanceWeightUnit':
          value = formatterType(scope.row.balanceWeightUnit, this.weightUnitList)
          break
        case 'type':
          value = formatterType(scope.row.type, this.quotationSourceList)
          break
        case 'channelRouterId':
          value = formatterName(scope.row.channelRouterId, this.channelRouterList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    formatterValue (value, field) {
      if (value !== undefined && value !== null && value !== '') {
        switch (field) {
          case 'product':
            value = formatterCodeName(value, this.logisticsProductByParamsList)
            break
          case 'channel':
            value = formatterCodeName(value, this.logisticsChannelByParamsList)
            break
          case 'provider':
            value = formatterCode(value, this.providerList)
            break
          case 'creator':
            value = formatterUser(value, this.userList)
            break
          case 'parcelType':
            value = formatterType(value, this.parcelTypeList)
            break
          case 'goodsCategory':
            value = formatterType(value, this.goodsCategoryList)
            break
          case 'yesOrNo':
            value = formatterType(value, this.yesOrNoList)
            break
          case 'warehouse':
            value = formatterShowName(value, this.warehouseInfoList, 'warehouseName')
            break
          case 'finishUser':
            value = formatterUser(value, this.userList)
            break
          case 'country':
            value = formatterCodeName(value, this.countryList)
            break
          case 'objectType':
            value = formatterType(value, this.objectTypeList)
            break
          case 'status':
            value = formatterType(value, this.statusList)
            break
          case 'shipper.iossTaxType':
            value = formatterType(value, this.iossTypeList)
            break
          case 'insuredCurrency':
            value = formatterCodeName(value, this.currencyList)
            break
          case 'declareCurrency':
            value = formatterCodeName(value, this.currencyList)
            break
          case 'codCurrency':
            value = formatterCodeName(value, this.currencyList)
            break
          case 'taxPayMode':
            value = formatterType(value, this.taxPayModeList)
            break
          case 'customsMethod':
            value = formatterType(value, this.customsMethodList)
            break
        }
      }
      return value
    },
    // 申报明细列表合计
    getDeclareTableSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (column.property === 'quantity' || column.property === 'declareSumD' || column.property === 'unitNetWeightD') {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            if (column.property === 'declareSumD') {
              sums[index] = sums[index].toFixed(2)
            } else if (column.property === 'unitNetWeightD') {
              sums[index] = sums[index].toFixed(3)
            }
          } else {
            sums[index] = ''
          }
        } else if (column.property === 'serialNo') {
          sums[index] = '合计'
        } else {
          sums[index] = ''
        }
      })
      return [sums]
    },
    // 包裹列表合计
    getPackageTableSummaries (param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (column.property === 'weightD' ||
            column.property === 'volD' ||
            column.property === 'volumeWeightD' ||
            column.property === 'lwd' ||
            column.property === 'l2wd') {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            if (column.property === 'lwd' || column.property === 'l2wd') {
              sums[index] = sums[index].toFixed(1)
            } else {
              sums[index] = sums[index].toFixed(3)
            }
          } else {
            sums[index] = ''
          }
        } else if (column.property === 'serialNo') {
          sums[index] = '合计'
        } else {
          sums[index] = ''
        }
      })
      return [sums]
    },
    // 获取信息
    getWaybill () {
      this.$http.get(`/ws/comwaybill/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (!res.data.shipper) {
          delete res.data['shipper']
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.getOrder()
        this.getShipper()
        // FBA显示【复制到货信息】
        if (this.dataForm.logisticsType === 12) {
          this.getInboundInfo(this.dataForm.id)
        }
      }).catch(() => {})
    },
    // 获取信息
    getOrder (callback) {
      this.$http.get(`/co/order/${this.dataForm.orderId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (!res.data.shipper) {
          delete res.data['shipper']
        }
        this.$nextTick(() => {
          if (res.data && res.data.id) {
            delete res.data['id']
          }
          // 过滤掉订单的 status，避免覆盖到运单
          const { status, logisticsChannelCode, logisticsProductCode, ...restOrderData } = res.data
          this.dataForm = {
            ...this.dataForm,
            ...restOrderData
          }
        })
        // this.dataForm.subCustomerOrderNo = res.data.packageList[0].subCustomerOrderNo
        callback && callback()
      }).catch(() => {})
    },
    tabsClick (tab, event) {
      // let url
      this.dataList = []
      switch (tab.name) {
        case 'exception':
          this.getException()
          break
        case 'operateLog':
          this.getOperatelog()
          break
        case 'sortRate':
          this.getSortRatelog()
          break
        case 'shipperConsignee':
          this.getShipper()
          this.getConsignee()
          break
        case 'package':
          this.getPackageList()
          break
        case 'declare':
          this.getDeclare()
          break
        case 'reference':
          this.getReference()
          break
        case 'attachments':
          this.getAttachments()
          break
        case 'track':
          break
        default:
          break
      }
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    getException () {
      this.$http.get('/ws/comexceptionbill/listByParams', { params: { waybillId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.wsComExceptionBillDTOList = res.data
      }).catch(() => { })
    },
    getOperatelog () {
      this.$http.get('/ws/comwaybilloperatelog/query', { params: { waybill_id: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.wsComWaybillOperateLogDTOList = res.data
      }).catch(() => { })
    },
    getSortRatelog () {
      this.$http.get('/ba/sortratelog/list/', { params: { orderId: this.dataForm.orderId } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.wsComWaybillSortRateLogDTOList = res.data
      }).catch(() => { })
    },
    getShipper () {
      this.$http.get('/co/ordershipper/' + this.dataForm.orderId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data) {
          this.dataForm.shipper = res.data
        }
      }).catch(() => { })
    },
    getConsignee () {
      this.$http.get('/co/orderconsignee/' + this.dataForm.orderId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.consignee = res.data
      }).catch(() => { })
    },
    getAttachments () {
      this.attachmentsTableLoading = true
      this.$http.get('/bd/attachments/page', { params: { relationId: this.dataForm.orderId, page: this.attachmentsPage, limit: this.attachmentsLimit } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.attachmentsList = res.data.list
        this.attachmentsTotal = res.data.total || 0
      }).catch(() => { }).finally(() => { this.attachmentsTableLoading = false })
    },
    attachmentsPageSizeChangeHandle (val) {
      this.attachmentsPage = 1
      this.attachmentsLimit = val
      this.getAttachments()
    },
    // 分页, 当前页
    attachmentsPageCurrentChangeHandle (val) {
      this.attachmentsPage = val
      this.getAttachments()
    },
    getPackageList () {
      let mergeType = this.dataForm.mergeType === 1
      let paramsObj = mergeType ? { mergeWaybillId: this.dataForm.id } : { waybillId: this.dataForm.id }
      this.$http.get('/ws/cominsubwaybill/list', { params: paramsObj }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.packageDataList = res.data
        this.$refs.packageDataList.reloadData(this.packageDataList)
      }).catch(() => { })
    },
    getDeclare () {
      this.$http.get('/co/orderdeclare/orderid', { params: { orderId: this.dataForm.orderId } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.declareDataList = res.data
        this.$refs.declareDataList.reloadData(this.declareDataList)
      }).catch(() => { })
    },
    getReference () {
      this.referenceTableLoading = true
      this.$http.get('/co/coorderfbareference/page', { params: { orderId: this.dataForm.orderId, page: 1, limit: 100 } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.referenceDataList = res.data.list
      }).catch(() => { }).finally(() => { this.referenceTableLoading = false })
    },
    operateLogSubmitHandler () {
      let data = {
        waybillId: this.dataForm.id,
        operateNode: 11,
        opeareteDescription: this.remark
      }
      this.$http.post('/ws/comwaybilloperatelog', data).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        } else {
          this.remark = ''
          return this.$message.success(res.msg)
        }
      }).catch(() => { })
    },
    async getInboundInfo (id) {
      let info = {}
      // 1
      this.inboundInfo = await this.$http.get(`/ba/inorder/${id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return res.msg
        }
        if (!res.data) {
          return '暂未计费，到货信息复制失败'
        }
        info.customerVoucherNo = res.data.customerVoucherNo
        info.fbaWarehouseCode = res.data.fbaWarehouseCode
        info.consigneePostcode = res.data.consigneePostcode
        info.logisticsProductCode = res.data.logisticsProductCode
        info.logisticsProductName = formatterCodeNativeName(res.data.logisticsProductCode, this.logisticsProductByParamsList)
        info.customsMethod = formatterType(res.data.customsMethod, this.customsMethodList)
        info.totalQty = res.data.totalQty
      }).catch(() => {})
      if (this.inboundInfo) {
        return
      }
      // 2
      this.inboundInfo = await this.$http.get(`/ba/billsreceivable/infoByBusinessId/${id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return res.msg
        }
        if (!res.data) {
          return '暂未计费，到货信息复制失败'
        }
        info.balanceWeightD = res.data.balanceWeightD
        info.weightD = res.data.subBillsReceivableList.reduce((total, item) => total + item.weightD, 0).toFixed(3)
        info.volumeWeightD = res.data.subBillsReceivableList.reduce((total, item) => total + item.volumeWeightD, 0).toFixed(3)
      }).catch(() => {})
      if (this.inboundInfo) {
        return this.$message.error(this.inboundInfo)
      }
      // 3
      this.inboundInfo = await this.$http.get(`/ba/receivablefee/listByParams?groupByFeeType=1&businessId=${id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return res.msg
        }
        if (!res.data || res.data.length === 0) {
          return '暂未计费，到货信息复制失败'
        }
        info.sumD = res.data.reduce((total, item) => total + item.sumD, 0).toFixed(2)
        // 运费ID = 1238746448119152641
        info.priceD = 'N/A'
        res.data.forEach(item => {
          if (item.feeTypeId === '1238746448119152641') {
            info.priceD = item.referencePrice
          }
        })
      }).catch(() => {})
      if (this.inboundInfo) {
        // return this.$message.error(this.inboundInfo)
        return this.inboundInfo
      }
      // 复制
      console.log('info', info)
      let txt = '客户单号：' + info.customerVoucherNo + '\n' +
        '销售产品：' + info.logisticsProductName + '\n' + '报关方式：' + info.customsMethod + '\n' +
        '目的邮编：' + info.consigneePostcode + '，仓库代码：' + info.fbaWarehouseCode + '\n' +
        '收货件数：' + info.totalQty + '，实重：' + info.weightD + '，材积重：' + info.volumeWeightD + '，计费重：' + info.balanceWeightD + '\n' +
        '\n' +
        '表价：' + info.priceD + '/kg\n' +
        '应收费用合计：' + info.sumD + '\n' +
        '————————\n' +
        '以上数据/费用无异议默认确认无需回复，有异议请及时@我司港前客服财务处理更新！'
      // console.log(txt)
      this.inboundInfo = txt
    },
    clip,
    copyInboundInfoHandle (event) {
      console.log(this.inboundInfo)
      if (this.inboundInfo && !this.inboundInfo.includes('————————')) {
        return this.$message.error(this.inboundInfo)
      }
      this.clip(this.inboundInfo, event)
    },
    async downloadFile (downloadUrl, fileName) {
      let url = this.getDownloadUrl(downloadUrl)
      const response = await fetch(url)
      const blob = await response.blob()
      saveAs(blob, fileName)
    },
    getDownloadUrl (downloadUrl) {
      if (downloadUrl.indexOf('http://') === 0 || downloadUrl.indexOf('https://') === 0) {
        return downloadUrl
      }
      return `static/${downloadUrl}`
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterUser,
    numberFormat,
    formatterCode
  },
  computed: {
    packageTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.packageTableColumns).map((key) => this.packageTableColumns[key])
      arr = tableColumns.filter((item) => {
        if (item.prop === 'packageWeightD') {
          item.label = this.$t('fba.weight') + (this.dataForm.standardUnit === 0 ? '(KG)' : '(LB)')
        }
        if (item.prop === 'packageLengthD') {
          item.label = this.$t('fba.length') + (this.dataForm.standardUnit === 0 ? '(CM)' : '(IN)')
        }
        if (item.prop === 'packageWidthD') {
          item.label = this.$t('fba.width') + (this.dataForm.standardUnit === 0 ? '(CM)' : '(IN)')
        }
        if (item.prop === 'packageHeightD') {
          item.label = this.$t('fba.height') + (this.dataForm.standardUnit === 0 ? '(CM)' : '(IN)')
        }
        return item.isShow
      })
      return arr
    },
    attachmentsTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.attachmentsTableColumns).map((key) => this.attachmentsTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    wsComExceptionBillTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.wsComExceptionBillTableColumns).map((key) => this.wsComExceptionBillTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    wsComWaybillOperateLogTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.wsComWaybillOperateLogTableColumns).map((key) => this.wsComWaybillOperateLogTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    wsComWaybillSortRateLogTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.wsComWaybillSortRateLogTableColumns).map((key) => this.wsComWaybillSortRateLogTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    declareTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.declareTableColumns).map((key) => this.declareTableColumns[key])
      arr = tableColumns.filter((item, index) => {
        if (item.prop === 'unitNetWeightD') {
          item.label = this.$t('fba.unitNetWeight') + '(' + (this.dataForm.standardUnit === 1 ? 'LB' : 'KG') + ')'
        } else if (item.prop === 'unitDeclarePriceD' && this.dataForm.declareCurrency) {
          item.label = this.$t('wsComWaybill.declare.unitDeclarePrice') + '(' + this.dataForm.declareCurrency + ')'
        } else if (item.prop === 'declareSumD' && this.dataForm.declareCurrency) {
          item.label = this.$t('wsComWaybill.declare.declareSum') + '(' + this.dataForm.declareCurrency + ')'
        }
        return item.isShow
      })
      return arr
    },
    referenceTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.referenceTableColumns).map((key) => this.referenceTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  }
}
</script>
