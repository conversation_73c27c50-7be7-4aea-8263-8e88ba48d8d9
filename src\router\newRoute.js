const newRoute = [
  {
    path: '/message-notice-receiver',
    component: resolve => (require([`@/views/modules/message/notice-receiver.vue`], resolve)),
    name: 'message-notice-receiver',
    meta: { title: '我收到的消息', isTab: true }
  },
  {
    path: '/saas-company-pay',
    component: resolve => (require([`@/views/modules/saas/company-pay.vue`], resolve)),
    name: 'saas-company-pay',
    meta: { title: '开通服务', isTab: true }
  },
  {
    path: '/saas-company-visit-code',
    component: resolve => (require([`@/views/modules/saas/company-visit-code.vue`], resolve)),
    name: 'saas-company-visit-code',
    meta: { title: '邀请有奖', isTab: true }
  },
  {
    path: 'notice-list',
    component: resolve => (require([`@/views/modules/notice/list.vue`], resolve)),
    name: 'notice-list',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `公告列表`,
      isTab: true
    }
  },
  {
    path: 'notice-detail',
    component: resolve => (require([`@/views/modules/notice/detail.vue`], resolve)),
    name: 'notice-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `公告详情`,
      isTab: true
    }
  },
  {
    path: 'logistics-channel-api-type-detail',
    component: resolve => (require([`@/views/modules/bd/logistics-channel-api-type-detail.vue`], resolve)),
    name: 'logistics-channel-api-type-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `物流渠道API类型明细`
    }
  },
  // 新增路由，分区模板明细维护
  {
    path: 'zone-template-detail',
    component: resolve => (require([`@/views/modules/bd/zone-template-detail.vue`], resolve)),
    name: 'zone-template-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `分区模板明细维护`
    }
  },
  // 新增路由,尾程渠道设置
  {
    path: 'logistics-channel-setting',
    component: resolve => (require([`@/views/modules/bd/logistics-channel-setting.vue`], resolve)),
    name: 'logistics-channel-setting',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `尾程派送渠道设置`
    }
  },
  // 新增路由,尾程渠道邮编映射
  {
    path: 'logistics-channel-postcode',
    component: resolve => (require([`@/views/modules/bd/logistics-channel-postcode.vue`], resolve)),
    name: 'logistics-channel-postcode',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `物流渠道链路邮编映射`
    }
  },
  // 新增路由,物流产品设置
  {
    path: 'logistics-product-setting',
    component: resolve => (require([`@/views/modules/bd/logistics-product-setting.vue`], resolve)),
    name: 'logistics-product-setting',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `物流产品设置`
    }
  },
  // 新增路由,尾程渠道设置
  {
    path: 'express-base-quotation-setting',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-setting.vue`], resolve)),
    name: 'express-base-quotation-setting',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价设置`
    }
  },
  // 新增路由,报价设置(新版)
  {
    path: 'express-base-quotation-new-setting',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-new-setting.vue`], resolve)),
    name: 'express-base-quotation-new-setting',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价设置(新版)`
    }
  },
  // 新增路由,报价超限费分区报价明细
  {
    path: 'express-base-quotation-overstep-item',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-overstep-item.vue`], resolve)),
    name: 'express-base-quotation-overstep-item',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `超限费分区报价`
    }
  },
  // 新增路由,报价附加费分区重量段明细
  {
    path: 'express-base-quotation-addition-item',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-addition-item.vue`], resolve)),
    name: 'express-base-quotation-addition-item',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `分区重量段`
    }
  },
  // 新增路由,渠道链路维护
  {
    path: 'logistics-product-channel-route',
    component: resolve => (require([`@/views/modules/bd/logistics-product-channel-route.vue`], resolve)),
    name: 'logistics-product-channel-route',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `物流渠道链路维护`
    }
  },
  {
    path: 'diylabel',
    name: 'diylabel',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      iframeURL: `${window.location.origin}/static/diylabel/index.html`,
      title: `DIY面单`,
      isTab: true
    }
  },
  // 新增路由,基础资料-客户详情
  {
    path: 'customer-view-detail',
    component: resolve => (require([`@/views/modules/bd/customer-view-detail.vue`], resolve)),
    name: 'customer-view-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `客户详情`
    }
  },
  // 新增路由,应收管理-收款账户余额
  {
    path: 'receivable-account',
    component: resolve => (require([`@/views/modules/ba/receivable-account.vue`], resolve)),
    name: 'receivable-account',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `收款账户余额`
    }
  },
  // 新增路由，diypdf明细维护
  {
    path: 'diypdf-detail',
    component: resolve => (require([`@/views/modules/bd/diypdf-detail.vue`], resolve)),
    name: 'diypdf-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `diypdf明细维护`
    }
  },
  // 报价预览
  {
    path: 'express-base-quotation-price-view-detail',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-price-view-detail.vue`], resolve)),
    name: 'express-base-quotation-price-view-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价预览`
    }
  },
  // 报价批量编辑
  {
    path: 'express-base-quotation-price-add-or-update',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-price-add-or-update.vue`], resolve)),
    name: 'express-base-quotation-price-add-or-update',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价编辑`
    }
  },
  // 报价折扣预览
  {
    path: 'express-base-quotation-discount-view-detail',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-discount-view-detail.vue`], resolve)),
    name: 'express-base-quotation-discount-view-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价折扣预览`
    }
  },
  // 报价折扣批量编辑
  {
    path: 'express-base-quotation-discount-add-or-update',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-discount-add-or-update.vue`], resolve)),
    name: 'express-base-quotation-discount-add-or-update',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价折扣编辑`
    }
  },
  // 附加费明细预览
  {
    path: 'express-base-quotation-addition-item-view-detail',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-addition-item-view-detail.vue`], resolve)),
    name: 'express-base-quotation-addition-item-view-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `附加费明细预览`
    }
  },
  // 单条报价新增
  {
    path: 'express-base-quotation-price-add',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-price-add.vue`], resolve)),
    name: 'express-base-quotation-price-add',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `新增报价`
    }
  },
  // 单条报价新增
  {
    path: 'express-base-quotation-tax-price-add',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-tax-price-add.vue`], resolve)),
    name: 'express-base-quotation-tax-price-add',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `新增税金报价`
    }
  },
  // 报价快录添加列表
  {
    path: 'express-base-quotation-price-smart',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-price-smart.vue`], resolve)),
    name: 'express-base-quotation-price-smart',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `报价快录`
    }
  },
  // 智能添加报价
  {
    path: 'express-base-quotation-price-smart-add',
    component: resolve => (require([`@/views/modules/ps/express-base-quotation-price-smart-add.vue`], resolve)),
    name: 'express-base-quotation-price-smart-add',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `智能添加报价`
    }
  },
  // 客户报价试算
  {
    path: 'express-customer-calculate',
    component: resolve => (require([`@/views/modules/ps/express-customer-calculate.vue`], resolve)),
    name: 'express-customer-calculate',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `客户报价试算`
    }
  },
  // 供应商报价试算
  {
    path: 'express-provide-calculate',
    component: resolve => (require([`@/views/modules/ps/express-provide-calculate.vue`], resolve)),
    name: 'express-provide-calculate',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `供应商报价试算`
    }
  },
  // 异步任务
  {
    path: 'sys-sys-async-task',
    component: resolve => (require([`@/views/modules/sys/sys-async-task.vue`], resolve)),
    name: 'sys-sys-async-task',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `异步任务表`
    }
  },
  // 新增路由,结算基础信息-供应商费用项对应
  {
    path: 'fee-type-provider-matching-item',
    component: resolve => (require([`@/views/modules/bd/fee-type-provider-matching-item.vue`], resolve)),
    name: 'fee-type-provider-matching-item',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `费用项对应明细`
    }
  },
  // 新增路由,结算基础信息-供应商费用项对应
  {
    path: 'commission-plan-setting',
    component: resolve => (require([`@/views/modules/ps/commission-plan-setting.vue`], resolve)),
    name: 'commission-plan-setting',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `提成方案明细`
    }
  },
  {
    path: 'com-parcel-bagging',
    component: resolve => (require([`@/views/modules/ws/com-parcel-bagging.vue`], resolve)),
    name: 'com-parcel-bagging',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `装袋/箱`
    }
  },
  {
    path: 'tks-tracking-import',
    component: resolve => (require([`@/views/modules/csm/tks-tracking-import.vue`], resolve)),
    name: 'tks-tracking-import',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `导入轨迹`
    }
  },
  {
    path: 'tks-tracking-import-by-mawb',
    component: resolve => (require([`@/views/modules/csm/tks-tracking-import-by-mawb.vue`], resolve)),
    name: 'tks-tracking-import-by-mawb',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `按干线主单导入轨迹`
    }
  },
  {
    path: 'track-autonomous-push-detail',
    component: resolve => (require([`@/views/modules/csm/track-autonomous-push-detail.vue`], resolve)),
    name: 'track-autonomous-push-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `轨迹自主明细维护`
    }
  },
  {
    path: 'push-customer-bills-detail',
    component: resolve => (require([`@/views/modules/csm/push-customer-bills-detail.vue`], resolve)),
    name: 'push-customer-bills-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `应收账单推送明细`
    }
  },
  {
    path: 'ws-com-bag-list',
    component: resolve => (require([`@/views/modules/ws/com-bag-list.vue`], resolve)),
    name: 'ws-com-bag-list',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `容器列表`
    }
  },
  {
    path: 'ws-com-out-warehouse-batch',
    component: resolve => (require([`@/views/modules/ws/com-out-warehouse-batch.vue`], resolve)),
    name: 'ws-com-out-warehouse-batch',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `出库发运批次`
    }
  },
  {
    path: 'ws-com-return-in-batch',
    component: resolve => (require([`@/views/modules/ws/com-return-in-batch.vue`], resolve)),
    name: 'ws-com-return-in-batch',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `退回入库批次`
    }
  },
  {
    path: 'ws-com-return-out-batch',
    component: resolve => (require([`@/views/modules/ws/com-return-out-batch.vue`], resolve)),
    name: 'ws-com-return-out-batch',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `退货出库批次`
    }
  },
  {
    path: 'ws-com-waybill-view-detail',
    component: resolve => (require([`@/components/ws/com-waybill-view-detail.vue`], resolve)),
    name: 'ws-com-waybill-view-detail',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `查看运单`
    }
  },
  {
    path: 'change-label-scan-print',
    component: resolve => (require([`@/views/modules/tls/change-label-scan-print.vue`], resolve)),
    name: 'change-label-scan-print',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `换标扫描打印`
    }
  },
  {
    path: 'batch-change-label-scan-print',
    component: resolve => (require([`@/views/modules/tls/batch-change-label-scan-print.vue`], resolve)),
    name: 'batch-change-label-scan-print',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `批量换标任务打印 - 批量`
    }
  },
  {
    path: 'label-change-consignor-scan-print',
    component: resolve => (require([`@/views/modules/tls/label-change-consignor-scan-print.vue`], resolve)),
    name: 'label-change-consignor-scan-print',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `Label更换发件人 - 扫描打印`
    }
  },
  {
    path: 'delivery-no-sort-order-scan-sort',
    component: resolve => (require([`@/views/modules/tls/delivery-no-sort-order-scan-sort.vue`], resolve)),
    name: 'delivery-no-sort-order-scan-sort',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `派送单号分拣 - 扫描分拣`
    }
  },
  {
    path: 'batch-change-label-single-scan-print',
    component: resolve => (require([`@/views/modules/tls/batch-change-label-single-scan-print.vue`], resolve)),
    name: 'batch-change-label-single-scan-print',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `批量换标任务打印 - 单个`
    }
  },
  {
    path: 'fedex-upload',
    component: resolve => (require([`@/views/modules/other/fedex-upload.vue`], resolve)),
    name: 'fedex-upload',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `Fedex无纸化`
    }
  },
  {
    path: 'ups-upload',
    component: resolve => (require([`@/views/modules/other/ups-upload.vue`], resolve)),
    name: 'ups-upload',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `UPS无纸化`
    }
  },
  {
    path: 'csm-customer-weight-update',
    component: resolve => (require([`@/views/modules/csm/customer-weight-update.vue`], resolve)),
    name: 'csm-customer-weight-update',
    meta: {
      ...window.SITE_CONFIG['contentTabDefault'],
      menuId: '',
      title: `重量核对导入`
    }
  },
]

export default newRoute
