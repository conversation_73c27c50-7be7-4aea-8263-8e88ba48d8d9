import { Browser } from '@/utils/tools'
import printJS from 'print-js'
import http from '@/utils/request'
import { Loading, Message } from 'element-ui'

// 打印公共方法
export function printFn (url, failBack, successBack) {
  let isAjax = false
  if (Browser.isChrome) {
    if (isAjax) {
      return false
    }
    isAjax = true
    const loadStatus = Loading.service({
      lock: true,
      text: 'Loading',
      fullscreen: true,
      // spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.6)'
    })
    http({
      method: 'get',
      url: url,
      data: '',
      responseType: 'arraybuffer'
    }).then(({ data: res }) => {
      isAjax = false
      loadStatus.close()
      ab2str(res)
      function ab2str (buf) {
        try {
          let resJson
          let enc = new TextDecoder('utf-8')
          resJson = JSON.parse(enc.decode(new Uint8Array(buf))) // 转化成json对象
          if (resJson.code !== 0) {
            failBack && failBack()
            return Message({ message: resJson.msg, type: 'error', duration: 5000 })
          } else {
            return false
          }
        } catch (e) {
          let localPdf = new window.Blob([res], { type: 'application/pdf' })
          localPdf = window.URL.createObjectURL(localPdf)
          successBack && successBack()
          printJS(localPdf)
        }
      }
    }).catch(error => {
      console.error('An error occurred:', error)
      isAjax = false
      loadStatus.close()
      if (failBack) {
        // eslint-disable-next-line standard/no-callback-literal
        failBack && failBack({ code: 500, msg: '发生错误' + error.toString(), data: null })
      } else {
        if (error.response) {
          console.log('error.response', error.response)
          let errorMsg = error.response.data ? error.response.data.message : error.response.statusText
          return Message({ message: errorMsg, type: 'error', duration: 5000 })
        } else {
          return Message({ message: error.toString(), type: 'error', duration: 5000 })
        }
      }
    })
  } else {
    window.open('/static/pdf/web/viewer.html?file=' + encodeURIComponent(url))
  }
}

export function printFn4Post (url, param, failBack, successBack) {
  let isAjax = false
  if (Browser.isChrome) {
    if (isAjax) {
      return false
    }
    isAjax = true
    const loadStatus = Loading.service({
      lock: true,
      text: 'Loading',
      fullscreen: true,
      // spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.6)'
    })
    http({
      method: 'post',
      url: url,
      data: param,
      responseType: 'arraybuffer'
    }).then(({ data: res }) => {
      isAjax = false
      loadStatus.close()
      ab2str(res)
      function ab2str (buf) {
        try {
          let resJson
          let enc = new TextDecoder('utf-8')
          resJson = JSON.parse(enc.decode(new Uint8Array(buf))) // 转化成json对象
          if (resJson.code !== 0) {
            failBack && failBack()
            return Message({ message: resJson.msg, type: 'error', duration: 5000 })
          } else {
            return false
          }
        } catch (e) {
          let localPdf = new window.Blob([res], { type: 'application/pdf' })
          localPdf = window.URL.createObjectURL(localPdf)
          successBack && successBack()
          printJS(localPdf)
        }
      }
    }).catch(error => {
      console.error('An error occurred:', error)
      isAjax = false
      loadStatus.close()
      // eslint-disable-next-line standard/no-callback-literal
      failBack && failBack({ code: 500, msg: '发生错误', data: null })
    })
  } else {
    window.open('/static/pdf/web/viewer.html?file=' + encodeURIComponent(url))
  }
}
