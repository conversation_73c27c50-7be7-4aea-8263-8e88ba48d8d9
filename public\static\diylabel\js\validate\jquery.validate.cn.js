/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: CN
 */
jQuery.extend(jQuery.validator.messages, {
        required: "此内容为必填项,请输入!",
		remote: "内容输入错误!",
		email: "E-mail格式错误,请重新输入!",
		url: "网址格式错误,请重新输入!",
		date: "日期格式错误,请重新输入!",
		dateISO: "日期格式错误,请重新输入!",
		number: "请输入合法的数字!",
		digits: "请输入零或正整数!",
		creditcard: "信用卡号格式错误,请重新输入!",
		equalTo: "两次输入不一致,请重新输入!",
		accept: "请输入拥有合法后缀名的字符串!",
		maxlength: jQuery.validator.format("字符串长度不能大于{0}!"),
		minlength: jQuery.validator.format("字符串长度不能小于{0}!"),
		rangelength: jQuery.validator.format("字符串长度只允许在{0}-{1}之间!"),
		range: jQuery.validator.format("输入的数值只允许在{0}-{1}之间!"),
		max: jQuery.validator.format("输入的数值不允许大于{0}!"),
		min: jQuery.validator.format("输入的数值不允许小于{0}!"),
		integer: "请输入合法的整数!",
		positive: "请输入合法的正数!",
		positiveInteger: "请输入合法的正整数!",
		mobile: "手机号码格式错误,请重新输入!",
		phone: "电话号码格式错误,请重新输入!",
		zipCode: "邮政编码格式错误,请重新输入!",
		requiredTo: "此内容为必填项,请输入!",
		username: "只允许包含中文、英文、数字和下划线!",
		prefix: "请输入以 {0} 开头的字符串!",
		lettersonly: "只允许包含字母!",
		alphanumeric: "只允许包含英文、数字和下划线!"
});