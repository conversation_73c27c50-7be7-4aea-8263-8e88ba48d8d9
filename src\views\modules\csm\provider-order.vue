<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="140px">
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <three-no-input ref="threeNoInput"
                              :customerOrderNo.sync="dataForm.customerOrderNos"
                              :waybillNo.sync="dataForm.waybillNos"
                              :deliveryNo.sync="dataForm.deliveryNos"
                              :postalTrackingNo.sync="dataForm.postalTrackingNos"
                              :autosize="threeNoInputAutoSize" :noSize="200" />
            </el-col>
            <el-col :span="16">
              <el-row :gutter="10">
                <div class="fl width100">
                  <el-col :span="10">
                    <el-form-item :label="$t('wsComWaybill.logisticsProductCode')" prop="logisticsProductCode">
                      <el-select v-model="dataForm.logisticsProductCode" filterable clearable>
                        <el-option v-for="item in baseData.logisticsProductByParamsList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item :label="$t('wsComWaybill.logisticsChannelCode')" prop="logisticsChannelCode">
                      <el-select v-model="dataForm.logisticsChannelCode" filterable clearable>
                        <el-option v-for="item in baseData.logisticsChannelByParamsList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="fl width100">
                  <el-col :span="10">
                    <el-form-item :label="$t('wsComWaybill.objectName')" prop="objectName">
                      <el-select v-model="dataForm.objectId" clearable :placeholder="$t('twoCharToSelectForCustomer')"
                                 :loading="loading"  filterable  remote reserve-keyword :remote-method="getCustomerByCodeOrName">
                        <el-option v-for="item in customerList" :key="item.code" :label="item.name" :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item :label="$t('wsComWaybill.warehouseId')" prop="warehouseId">
                      <el-select v-model="dataForm.warehouseId" filterable clearable>
                        <el-option v-for="item in baseData.warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="fl width100">
                  <el-col :span="10">
                    <el-form-item :label="$t('wsComWaybill.status')" prop="status">
                      <el-select v-model="dataForm.status" clearable filterable :placeholder="$t('wsComWaybill.status')" >
                        <el-option v-for="item in dict.statusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item :label="$t('wsComWaybill.pushOrderToProviderStatus')" prop="pushOrderToProviderStatus">
                      <el-select v-model="dataForm.pushOrderToProviderStatus" clearable filterable :placeholder="$t('wsComWaybill.pushOrderToProviderStatus')" >
                        <el-option v-for="item in dict.pushOrderToProviderStatusTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="fl width100">
                  <el-col :span="10">
                    <el-form-item :label="$t('wsComWaybill.consigneeCountry')" prop="consigneeCountry">
                      <el-select v-model="dataForm.consigneeCountry" filterable placeholder="" clearable>
                        <el-option v-for="item in baseData.countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item :label="$t('wsComWaybill.pushManifestToProviderStatus')" prop="pushManifestToProviderStatus">
                      <el-select v-model="dataForm.pushManifestToProviderStatus" clearable filterable :placeholder="$t('wsComWaybill.pushManifestToProviderStatus')" >
                        <el-option v-for="item in dict.pushManifestToProviderStatusTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="fl width100">
                  <el-col :span="14">
                    <el-form-item :label="$t('wsComWaybill.createDate')" prop="createDateArray">
                      <el-date-picker v-model="createDateArray" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" :start-placeholder="$t('startTime')" :end-placeholder="$t('endTime')" style="width: 100%"></el-date-picker>
                    </el-form-item>
                  </el-col>
                </div>
              </el-row>
            </el-col>
            <div class="search_box_btn">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetHandle()" icon="el-icon-refresh-right">重置</el-button>
<!--              <el-button type="text" @click="searchBoxShowFn">{{searchBoxShow? '展开':'收起'}}<i :class="searchBoxShow ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></el-button>-->
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12">
            <el-button size="mini" type="primary" plain v-if="$hasPermission(`csm:providerOrder:pushOrder`)" @click="pushOrderToProviderHandle()">{{ $t('wsComWaybill.pushOrderToProvider') }}</el-button>
            <el-button size="mini" type="primary" plain v-if="$hasPermission(`csm:providerOrder:checkStatus`)" @click="queryProviderOrderStatusHandle()">{{ $t('wsComWaybill.queryProviderOrderStatus') }}</el-button>
            <el-button size="mini" type="primary" plain v-if="$hasPermission(`csm:providerOrder:sendShipmentManifests`)" @click="sendShipmentManifestsHandle()">{{ $t('wsComWaybill.sendShipmentManifests') }}</el-button>
            <!--保留空格符-->
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <ux-grid ref="tableData" v-loading="dataListLoading" :data="dataList"   @selection-change="dataListSelectionChangeHandle"
                   :show-overflow="true" :max-height="tableHeight" size="mini" :widthResize="true" :border="false">
            <!-- 动态显示表格 -->
            <ux-table-column type="checkbox" width='50' fixed="left" ></ux-table-column>
            <ux-table-column v-for="(item) in tableColumnsArr" :key="item.id"
                             :field="item.prop"  :title="item.label" :resizable="true" :border="false"
                             header-align="center" :align="item.align" :min-width="item.width"
                             :sortable="item.prop === 'createDate' || item.prop === 'pushOrderToProviderStatus'">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <span>{{formatterFn(scope,item.prop)}}</span>
                  </div>
                  <div v-else-if="item.prop === 'pushOrderToProviderStatus'">
                    <el-badge is-dot class="badge_status" v-if="scope.row.pushOrderToProviderStatus === 10" type="info"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.pushOrderToProviderStatus === 20" type="primary"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.pushOrderToProviderStatus === 25" type="danger"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.pushOrderToProviderStatus === 30" type="warning"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.pushOrderToProviderStatus === 40" type="danger"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.pushOrderToProviderStatus === 50" type="success"></el-badge>
                    <span>{{ formatterFn(scope,item.prop) }}</span>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </ux-table-column>
            <ux-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false" v-if="$hasPermission(`csm:comWaybill:view`)"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </ux-table-column>
          </ux-grid>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 100, 200, 500, 1000]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <!--共用拦截组件-->

  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeNativeName, formatterShowName, formatterUser } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import comMixins from '@/mixins/comMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import ViewDetail from '@/components/ws/com-waybill-view-detail'
import threeNoInput from '@/components/three-no-input'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { getBeforeDay, getNowDate } from '@/utils/tools'

export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '90', prop: 'pushOrderToProviderStatus', label: this.$t('wsComWaybill.pushOrderToProviderStatus'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '130', prop: 'pushOrderToProviderErrorMessage', label: this.$t('wsComWaybill.pushOrderToProviderErrorMessage'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'pushManifestToProviderStatus', label: this.$t('wsComWaybill.pushManifestToProviderStatus'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '130', prop: 'pushManifestToProviderErrorMessage', label: this.$t('wsComWaybill.pushManifestToProviderErrorMessage'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '0', prop: 'orderId', label: this.$t('wsComWaybill.orderId'), align: 'center', isShow: false, disabled: true },
        { type: '', width: '150', prop: 'customerOrderNo', label: this.$t('wsComWaybill.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('wsComWaybill.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'deliveryNo', label: this.$t('wsComWaybill.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'postalTrackingNo', label: this.$t('wsComWaybill.postalTrackingNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'logisticsType', label: this.$t('wsComWaybill.orderLogisticsType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsProductCode', label: this.$t('wsComWaybill.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsChannelCode', label: this.$t('wsComWaybill.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneeCountry', label: this.$t('wsComWaybill.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'objectNameDesensitized', label: this.$t('wsComWaybill.objectName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'status', label: this.$t('wsComWaybill.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'serviceId', label: this.$t('wsComWaybill.serviceId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'warehouseId', label: this.$t('wsComWaybill.warehouseId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('wsComWaybill.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '0', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ws/pushorderaftergettrackingno/page',
        getDataListIsPage: true
      },
      dataForm: {
        customerOrderNos: '',
        waybillNos: '',
        deliveryNos: '',
        postalTrackingNos: '',
        logisticsProductCode: '',
        logisticsChannelCode: '',
        objectName: '',
        warehouseId: '',
        status: '',
        pushOrderToProviderStatus: '',
        pushManifestToProviderStatus: '',
        createDateFrom: '',
        createDateTo: '',
        consigneeCountry: ''
      },
      loading: false,
      loadingFlag: false,
      customerList: [],
      // 数据字典
      dict: {
        statusList: [],
        objectTypeList: [],
        pushOrderToProviderStatusTypeList: [],
        pushManifestToProviderStatusTypeList: [],
        orderLogisticsTypeList: []
      },
      createDateArray: [getBeforeDay(7), getNowDate()],
      activeName: 'all',
      tableName: 'ws-wscomwaybill',
      baseData: {
        logisticsProductByParamsList: [],
        logisticsChannelByParamsList: [],
        warehouseList: [],
        countryList: [],
        userList: []
      },
      batchInterceptDialogVisible: false,
      addMemoVisible: false,
      batchInterceptDataForm: {
        opeareteDescription: '',
        wsComWaybillDTOList: null
      },
      changeChannelDialog: {
        visible: false,
        dataForm: {
          waybillNos: '',
          logisticsChannelCode: '',
          changeReason: ''
        }
      }
    }
  },
  watch: {
    createDateArray: {
      handler (value, oldName) {
        if (value !== undefined && value !== '' && value !== null) {
          this.dataForm.createDateFrom = timestampFormat(value[0])
          this.dataForm.createDateTo = timestampFormat(value[1])
        } else {
          this.createDateArray = [getBeforeDay(7), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
    this.getDict()
  },
  methods: {
    async getDict () {
      this.dict.statusList = await this.getDictTypeList('WsComWaybillStatus')
      // 仓库运单对象类型
      this.dict.objectTypeList = await this.getDictTypeList('wsComWaybillObjectType')
      this.dict.orderLogisticsTypeList = await this.getDictTypeList('orderLogisticsType')
      this.dict.pushOrderToProviderStatusTypeList = await this.getDictTypeList('pushOrderToProviderStatus')
      this.dict.pushManifestToProviderStatusTypeList = await this.getDictTypeList('pushManifestToProviderStatus')
    },
    async getBaseData () {
      this.baseData.logisticsProductByParamsList = await baseData(baseDataApi.listAllByCurrent).catch(() => {})
      // 128 为同步订单到供应商的API节点
      this.baseData.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByApiPushNodeList, { apiPushNode: 128 }).catch(() => {})
      this.baseData.warehouseList = await baseData(baseDataApi.warehouseInfoList).catch(() => {})
      this.baseData.countryList = await baseData(baseDataApi.countryList).catch(() => {})
      // 用户信息
      this.baseData.userList = await baseData(baseDataApi.allUserList).catch(() => {})
    },
    searchHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        this.queryPageByParam()
      }
    },
    resetHandle () {
      this.createDateArray = [getBeforeDay(7), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        // case 'createDate':
        //   value = timestampFormat(scope.row.createDate, 'YYYY-MM-DD hh:mm:ss')
        //   break
        case 'pushOrderToProviderStatus':
          value = formatterType(scope.row.pushOrderToProviderStatus, this.dict.pushOrderToProviderStatusTypeList)
          break
        case 'pushManifestToProviderStatus':
          value = formatterType(scope.row.pushManifestToProviderStatus, this.dict.pushManifestToProviderStatusTypeList)
          break
        case 'logisticsProductCode':
          value = formatterCodeNativeName(scope.row.logisticsProductCode, this.baseData.logisticsProductByParamsList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeNativeName(scope.row.logisticsChannelCode, this.baseData.logisticsChannelByParamsList)
          break
        case 'consigneeCountry':
          value = formatterCodeNativeName(scope.row.consigneeCountry, this.baseData.countryList)
          break
        case 'serviceId':
          value = formatterUser(scope.row.serviceId, this.baseData.userList)
          break
        case 'warehouseId':
          value = formatterShowName(scope.row.warehouseId, this.baseData.warehouseList, 'warehouseName')
          break
        case 'objectType':
          value = formatterType(scope.row.objectType, this.dict.objectTypeList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.dict.statusList)
          break
        case 'logisticsType':
          value = formatterType(scope.row.logisticsType, this.dict.orderLogisticsTypeList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    pushOrderToProviderHandle () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.warning(this.$t('select'))
      }
      let ids = this.dataListSelections.map(item => item.id)
      this.$http.post('/ws/pushorderaftergettrackingno/pushOrderToProvider', ids).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.searchHandle()
          }
        })
      }).catch(() => {})
    },
    sendShipmentManifestsHandle () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.warning(this.$t('select'))
      }
      let ids = this.dataListSelections.map(item => item.id)
      this.$http.post('/ws/pushorderaftergettrackingno/sendShipmentManifests', ids).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.searchHandle()
          }
        })
      }).catch(() => {})
    },
    queryProviderOrderStatusHandle () {
      if (this.dataListSelections.length <= 0) {
        return this.$message.warning(this.$t('select'))
      }
      let ids = this.dataListSelections.map(item => item.id)
      this.$http.post('/ws/pushorderaftergettrackingno/queryProviderOrderStatus', ids).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.searchHandle()
          }
        })
      }).catch(() => {})
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeNativeName,
    formatterShowName,
    formatterUser
  },
  computed: {
    batchInterceptDataRule () {
      return {
        opeareteDescription: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    changeChannnelDialogDataRule () {
      return {
        changeReason: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        logisticsChannelCode: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 5, maxRows: 7 }
      } else {
        return { minRows: 7, maxRows: 7 }
      }
    },
    // 过滤启用的物流渠道--用于强制更换渠道
    logisticsChannelEnableList () {
      return this.baseData.logisticsChannelByParamsList.filter(d => d.status === 1)
    }
  },
  components: {
    tableSet,
    ViewDetail,
    threeNoInput
  }
}
</script>
