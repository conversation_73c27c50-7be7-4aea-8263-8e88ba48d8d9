

/*主页面样式
-------------------------------  */
.index-table-expand {
  font-size: 0;
  label {
    width: 120px;
    color: #99a9bf;
  }
  .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 25%;
    float: left;
    display: flex;
  }
  .el-form-item__label, .el-form-item__content {
    font-size: 12px;
  }
  .el-form-item__content {
    flex: 1;
    word-break: break-all;
  }
}
.inner-table {
  margin-top: -5px;
}
// 滚动条
// ::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);box-shadow:inset 0 0 6px rgba(0,0,0,0.3);background-color:#F5F5F5;}
// ::-webkit-scrollbar{width:6px;height:6px; background-color:#F5F5F5;}
// ::-webkit-scrollbar-thumb{background-color:rgba(95, 95, 95, 0.6);}
// 按钮
.optBtnSet{
  padding:10px 5px;
  border:1px solid #EBEEF5;
  border-bottom: 0;
}
.optBtnSet.top1{
  margin-top: -1px;
}
.optBtn_panel{
  margin-top: -5px;
  margin-bottom: 10px
}
.optBtn_leftFixed{
  border: 1px solid transparent;
}
.searchTool {
  position: absolute;
  top: 2px;
  left: 50%;
  width: 120px;
  height: 14px;
  margin-left: -60px;
  text-align: center;
  line-height: 14px;
  font-size: 12px;
  color: #8e9192;
  background: #f1f4f5;
  cursor: pointer;
  z-index: 9999;
  &:hover{
    color: $--color-primary;
  }
}
// 按钮
.optBtn{
  margin-bottom: 10px
}
.text{
  position: relative;
  display: inline-block;
  padding: 0  10px;
  background: #fff;
  z-index: 3;
}
// 高级查询
.cs_more{
  position: relative;
  margin: 0 auto 10px;
  text-align: center;
  color:#666;
  cursor: pointer
}
.cs_more::before{
  content: "";
  position: absolute;
  top: 8px;
  left: 0;
  width: 100%;
  height:1px;
  background: #eee;
}
// 新增
.add-body {
  position: relative;
  padding-bottom: 82px;
}
.add-bottom {
  position: fixed;
  width: 100%;
  height: 80px;
  text-align: center;
  text-indent: 180px;
  left: 0;
  bottom: 0;
}
.el-form3 {
  padding-bottom: 20px;
  .el-form-item{
    float: left;
    width: 100%;
    min-width: 300px;
    margin-bottom: 20px;

  }
  &.label_page{
    .el-form-item{
      margin-bottom: 0;
    }
  }
}
.no_shadow{
  box-shadow: unset !important;
}
.el-menu-item.is-active{
  background-color: $--color-primary;
}
.no-padding {
  padding: 0!important;
}
// 小屏
@media only screen and (min-width: 860px){
  .el-form3 .el-form-item{
    width: 50%;
  }
}
// 中屏
@media only screen and (min-width: 992px){
  .el-form3 .el-form-item{
    width: 33.3%;
  }
}
// 中屏
@media only screen and (min-width: 1024px){
  .el-form3 .el-form-item{
    width: 25%;
  }
}

.flex {
  display: flex;
  align-items: center;
}
.flex.column {
  flex-direction:column;
}
.flex_1 {
  flex: 1;
}

// 强制换行
.word_break{
  word-break: break-all;
  & > div{
    padding-top: 5px;
    line-height: 1.5
  }
}

// 底部按钮
.el-form-footer{
  position:absolute;
  bottom: 0px !important;
  height: 56px;
  width: 99%;
  line-height: 56px;
  text-align: center;
  background: #fff;
  z-index: 99;
  //box-shadow: 1px -1px 6px rgba(0, 0, 0, 0.2);
}

// 新增底部按钮
.addOrUpdate_foot{
  margin-top: 20px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.aui-wrapper.aui-sidebar--fold{
  .el-form-footer{
    left: 79px;
  }
}
.panel-hd{
  margin:-20px -20px 20px;
  border-bottom:1px solid #F2F6FC;
  padding:20px;
  font-size: 18px;
  color: #333;
  line-height: 1.2
}
.optBtn{
  .el-button{
    margin:10px 10px 0 0;
  }
  .el-button + .el-button{
    margin:10px 10px 0 0;
  }
}
.el-table {
  .el-link{
    padding:0 4px;
  }
}
.el-link.el-link--default {
  color: $--color-primary !important;
}
.el-link.el-link--warning {
  color: $--color-warning !important;
}
.el-link.el-link--default.is-disabled {
  color: #C0C4CC !important;
}

// 列表中更多菜单
.cs_moreBtn{
  margin-left: 8px;
  border:1px solid #F2F6FC;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  .icon_down{
    display: inline-block;
    padding:4px 6px;
  }
}
.cs_poper_class{
  min-width:unset;
  ul{
    margin: 0;
    padding: 0;
    list-style: none;
  }
  li{
    cursor: pointer;
  }
}

// 表格嵌套表格的样式重置
.table_in_table .el-table__expanded-cell[class*=cell] {
  padding: 20px;
}

// tabs
.custom-tab.el-tabs--border-card{
  border: unset !important;
  box-shadow: unset !important;
  .el-tabs__header{
    border: 1px solid #dcdfe6;
  }
  .el-tabs__content{
    padding: 0 !important;
  }
}

// 自定义tab
.tab_box{
  position: relative;
  border-bottom: 1px solid #E4E7ED;
  margin-bottom: 20px;
  &:after{
    content: '';
    display: table;
    clear: both;
  }
  .tab_panel{
    margin: 0;
    padding: 0;
    margin-bottom: -1px;
    position: relative;
    border: 1px solid #E4E7ED;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
    position: relative;
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s;
    float: left;
    z-index: 2;
    .tab_item{
      padding: 0 20px;
      min-width: 4em;
      height: 40px;
      position: relative;
      line-height: 40px;
      display: inline-block;
      list-style: none;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      border-bottom: 1px solid transparent;
      border-left: 1px solid #E4E7ED;
      cursor: pointer;
      -webkit-transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      &:first-child {
        border-left: none;
      }
      &.active {
        color: $--color-primary;
        border-bottom-color: #fff;
      }
    }
  }
}
.tab_content{
  position: relative;
}


// 排版
.content_tip{
  .tip{
    padding: 8px 16px;
    background-color: #ecf8ff;
    border-radius: 4px;
    border-left: 5px solid $--color-primary;
    margin: 20px 0;
  }
  p {
    margin: 0;
    padding: 0;
    font-size: 14px;
    color: #5e6d82;
    line-height: 1.5em;
  }
  code {
    background-color: #f9fafc;
    padding: 0 4px;
    border: 1px solid #eaeefb;
    border-radius: 4px;
  }
}

// flex_tab
.search_box{
  position: relative;
  .search_box_btn{
    position: relative;
    padding-left: 15px;
    margin-left: 10px;
    min-width: 228px;
    border-left: 1px solid #eee;
    &.no_more{
      min-width: 190px;
    }
  }
  .sub_search_box_btn{
    position: relative;
    padding-left: 15px;
    margin-left: 60px;
    //margin-left: 210px;
    min-width: 128px;
    border-left: 1px solid #eee;
    &.no_more{
      min-width: 90px;
    }
  }

  .search_close_btn{
    position: absolute;
    bottom: 0;
    left: 50%;
    margin-left: -30px;
    width: 60px;
    height: 18px;
    background: #f1f4f5;
    border-top-left-radius:3px;
    border-top-right-radius: 3px;
    text-align: center;
    font-size: 18px;
    line-height: 18px;
    cursor: pointer;
    i{
      color: #C0C4CC;
    }
  }
}
.flex_wrap{
  $small-screen-height: calc(100vh - 15.5vh);
  $large-screen-height: calc(100vh - 12.6vh);

  // 当屏幕高度小于等于 800像素时，应用其中的样式
  @media only screen and (max-height: 800px) {
    min-height: $small-screen-height;
  }
  // 当屏幕高度大于等于 800像素时，应用其中的样式。
  @media only screen and (min-height: 800px) {
    min-height: $large-screen-height;
  }
  display:flex;
  flex-direction:column;
}
.flex_tab{
  position:relative;
  flex:1;
  min-height: 250px;
  .el-tabs__content, & > .el-card__body{
    position: absolute;
    top: 39px;
    bottom: 0;
    left: 0;
    right: 0;
    display:flex;
    flex-direction:column;
  }
  & > .el-card__body{
    top: 0;
  }
  &.flex_top > .el-card__body{
    top: 60px;
  }
  .flex_table{
    flex:1;
    max-height: 100%;
  }
}
.box-card{
  &.sub_flex_top > .el-card__body{
    top: 164px;
  }
  .el-table{
    font-size: 12px;
  }
}
//.pane_wrap{
//  min-height: calc(calc(100vh - 5.9vh));
//}

.panel_dark_bg {
  background: $dark_bg;
  min-height: calc(calc(100vh - 38px - 30px - 48px) - 2px);
}
.white{
  color: #fff;
}
.data-body{
  background-color: #fff;
}
.date-result{
  padding: 15px 15px 0;
}
.list-title{
  padding: 0 15px;
  margin-bottom: 5px;
}

// diy面单样式
.diylabelFrame {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
}

@media only screen and (min-width: 768px) {
  .el-col-sm-4-8 {
    width: 20%
  }
  .el-col-sm-offset-4-8 {
    margin-left: 20%
  }

  .el-col-sm-pull-4-8 {
    position: relative;
    right: 20%
  }

  .el-col-sm-push-4-8 {
    position: relative;
    left: 20%
  }
}

@media only screen and (min-width: 992px) {
  .el-col-md-4-8 {
    width: 20%
  }

  .el-col-md-offset-4-8 {
    margin-left: 20%
  }

  .el-col-md-pull-4-8 {
    position: relative;
    right: 20%
  }

  .el-col-md-push-4-8 {
    position: relative;
    left: 20%
  }
}

@media only screen and (min-width: 1200px) {
  .el-col-lg-4-8 {
    width: 20%
  }

  .el-col-lg-offset-4-8 {
    margin-left: 20%
  }
  .el-col-lg-pull-4-8 {
    position: relative;
    right: 20%
  }

  .el-col-lg-push-4-8 {
    position: relative;
    left: 20%
  }
}

@media only screen and (min-width: 1920px) {
  .el-col-xl-4-8 {
    width: 20%
  }
  .el-col-xl-offset-4-8 {
    margin-left: 20%
  }
  .el-col-xl-pull-4-8 {
    position: relative;
    right: 20%
  }
  .el-col-xl-push-4-8 {
    position: relative;
    left: 20%
  }
}

.badge_status{
  .el-badge__content{
    vertical-align: top;
    margin: 10px 6px 0;
  }
}

.detail-title {
  padding: 10px;
  position: relative;
}
.el-drawer__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  margin: 0;
}
.el-table .el-table-grey-row{
  background-color: #f5f7fa;
}
.main-color {
  color: $--color-primary;
}
.grey-color {
  color: #DCDFE6;
}
.table-light-blue-row{
  background-color: #ecf5ff;
}
.elx-table--tooltip-wrapper.theme--dark {
  background: #FFF;
  color: black;
  border: 1px solid black;
}
.elx-table--tooltip-wrapper.theme--dark.placement--top .elx-table--tooltip-arrow:before{border-top-color:white !important;transform: translateY(0.5px)}
.elx-table--tooltip-wrapper.theme--dark.placement--bottom .elx-table--tooltip-arrow:before{border-bottom-color:white !important;transform: translateY(-0.5px)}
.elx-grid .el-link {
  padding:0 4px !important;
}
.messageView {
  min-width: 58vw;
  .el-message-box__content {
    max-height: 400px  !important;
    overflow-y: scroll  !important;
  }
}
#paginationTotal .el-loading-mask{
  top: 2px !important;
  .el-loading-spinner {
    margin-top: -15px !important;
  }
}
