/*
* @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
* @Date:   2016-06-30 15:07:00
* @Last Modified by:   Administrator
* @Last Modified time: 2016-11-03 18:19:10
*/

.form-control {
	height:40px;
	border-radius:2px;
}
textarea {
 	border-color:#ccc;
	resize:none;
	padding:8px!important;
}
.form-control:focus, textarea:focus {
	border-color: #2D9FFF;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.065), 0 0 8px rgba(102, 175, 233, 0.5);
	outline:none;
}
.form-horizontal .control-label {
	padding-top:10px;
}
.form .control-label-sm {
	padding-top:6px;
}
.form .control-label-m {
	padding-top:8px;
}
.input-xs {
	height:24px!important;
	font-size:12px!important;
}
.input-sm {
	height:30px!important;
	font-size:12px!important;
}
.input-m {
	height:35px!important;
}
.input-lg {
	height:46px!important;
}
.col-md-1-i {
	width:8.33333333%!important;
}
.col-md-2-i {
	width:16.66666667%!important;
}
.col-md-3-i {
	width:25%!important;
}
.col-md-4-i {
	width:33.33333333%!important;
}
.col-md-5-i {
	width:41.66666667%!important;
}
.col-md-6-i {
	width:50%!important;
}
.btn-group {
	font-size:0;
}
.btn-group .dropdown-toggle {
	margin-left:-1px;
}
.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
	word-wrap:break-word;
	word-break:break-all;
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
	background-color: #fff;
	cursor:pointer;
}
.panel-default > .panel-heading {
	padding:0;
}
.panel-title > a {
	display:inline-block;
	width:100%;
	padding:10px 15px;
}
.popover {
	font-family: 'Microsoft YaHei';
	z-index:10002;
	max-width:400px;
}
.popover-title {
	font-size:13px;
}
.popover-content {
	font-size:12px;
}
