<template>
  <div class="add-body panel_body">
    <div class="addOrUpdatePanel orderAdd" >
      <el-form :model='dataForm' ref='dataForm'  key='6' :inline-message='true' label-width="100px" @submit.native.prevent>
        <el-row :gutter="22" >
          <el-col :span="12" >
            <el-form-item :label="$t('label.no')" prop="no" :rules='dataRule.no'>
                <el-input ref="dataFormNo"  class="input-with-select fontSize20"  v-model="dataForm.no"  :placeholder="$t('comAllInWarehouse.noPlaceholder')"
                        @keydown.enter.native="changeNo" >
                  <!--单号类型-->
                  <el-select v-model="dataForm.scanNoType" slot="prepend"  style="width: 150px;font-size: 13px;color: dodgerblue;" @change="changeNoType">
                    <el-option v-for="item in scanNoTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
                  </el-select>
                </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
              <el-checkbox v-model="togetherWeight" :label="$t('comAllInWarehouse.togetherWeight')" @change="togetherWeightChange()" class="el-form-item__label"></el-checkbox>
              <el-checkbox v-model="oneByOneForLabel" :label="$t('comAllInWarehouse.oneByOneForLabel')" class="el-form-item__label"></el-checkbox>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('comAllInWarehouse.inTime')"  prop="inTime">
              <el-date-picker
                v-model="dataForm.inTime"
                type="datetime"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期" style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="22" v-if="togetherWeight" >
          <el-col :span="14">
            <el-form-item :label="$t('label.totalWeight')"  prop="totalWeight" :rules='dataRule.totalWeight'>
              <el-input  class="fontSize20" ref="dataFormTotalWeight" :placeholder="$t('comAllInWarehouse.totalWeightPlaceholder')"  v-model="dataForm.totalWeight" @change="setTotalWeight()" @keydown.enter.native="handleTogetherWeightEnterKey"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        </el-form>
        <el-form :model='boxDataForm' ref='boxDataForm'  key='7' :inline-message='true'>
            <el-row :gutter="22" v-if="packageInfoShow">
              <div style="float: left;padding-top: 10px;;padding-bottom: 2px;font-size:13px">
                <label>{{$t('label.forecastInfo')}} → </label>
                <label>{{$t('label.customer')}}: {{ dataForm.customerName}}</label>
                <label> / {{$t('label.cno')}}: {{ dataForm.cno}}</label>
                <label> / {{$t('label.forecastBoxQty')}}: {{ dataForm.forecastBoxQty}}{{ $t('label.box') }} </label>
                <label> / {{$t('label.forecastWeight')}}: {{ dataForm.forecastWeight}}{{ dataForm.standardUnit}}</label>
              </div>
              <div style="float: right;padding-top: 10px;padding-bottom: 2px;font-size:13px">
                <label>{{$t('label.inWarehouseStatistics')}} → </label>
                {{$t('fba.totalBox')}}: <label class="primary" style="font-size: 20px">{{sumQty}}</label>
                / {{$t('wsComOperationCost.unitWeight')}}: <label class="primary" style="font-size: 20px">{{unitWeight}}</label>
                / {{$t('wsComOperationCost.totalVolume')}}: <label class="primary" style="font-size: 20px">{{sumVolume}}</label>
                / {{$t('wsComOperationCost.totalWeight')}}: <label class="primary" style="font-size: 20px">{{sumWeight}}</label>
                <el-link style="margin-left: 30px;" @click="openBatchBoxHandle">批量导入箱明细</el-link>
              </div>
                <div class="flex_table" ref="tableElm" v-domResize="redraw" id='boxDataFormId'>
                    <el-table  :data='boxDataForm.subWaybillList' border :max-height="tableHeight"  class='width100 margin_top10' >
                      <el-table-column :label="$t('label.serialNo')" prop='boxType' type='index' min-width='50'>
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.packageSerialNo'" >
                            <span v-text="scope.row.packageSerialNo = scope.$index + 1"></span>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop='lengthD' :label="$t('label.unitLength')" min-width='70' header-align='center' align='center' :render-header="addRedStar">
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.lengthD'" :rules='boxRule.lengthD'>
                            <el-input  v-model="scope.row.lengthD" :ref="'subWaybillList.' + scope.$index + '.lengthD'" maxlength="6" @keydown.enter.native="handleBoxDataEnterKey(scope.$index, 'lengthD')" @change="sumRowVol(scope.row)"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop='widthD' :label="$t('label.unitWidth')" min-width='70' header-align='center' align='center'  :render-header="addRedStar">
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.widthD'" :rules='boxRule.widthD'>
                            <el-input  v-model="scope.row.widthD" :ref="'subWaybillList.' + scope.$index + '.widthD'" maxlength="6" @keydown.enter.native="handleBoxDataEnterKey(scope.$index, 'widthD')" @change="sumRowVol(scope.row)"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop='heightD' :label="$t('label.unitHeight')" min-width='70' header-align='center' align='center'  :render-header="addRedStar">
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.heightD'" :rules='boxRule.heightD'>
                            <el-input  v-model="scope.row.heightD" :ref="'subWaybillList.' + scope.$index + '.heightD'" maxlength="6" @keydown.enter.native="handleBoxDataEnterKey(scope.$index, 'heightD')" @change="sumRowVol(scope.row)"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop='weightD' v-if="!togetherWeight" :label="$t('label.unitWeight')" min-width='80' header-align='center' align='center' :render-header="addRedStar">
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.weightD'" :rules='boxRule.weightD'>
                            <el-input  v-model="scope.row.weightD" :ref="'subWaybillList.' + scope.$index + '.weightD'" maxlength="7" @keydown.enter.native="handleBoxDataEnterKey(scope.$index, 'weightD')" @change="sumRowWeight(scope.row)"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop='packageQty' :label="$t('fba.boxCount')" min-width='60' header-align='center' align='center' :render-header="addRedStar">
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.packageQty'" :rules='boxRule.packageQty'>
                            <el-input  v-model="scope.row.packageQty" :ref="'subWaybillList.' + scope.$index + '.packageQty'" maxlength="4" @keydown.enter.native="handleBoxDataEnterKey(scope.$index, 'packageQty')" @blur="setTotalQty(scope.row)"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column prop='multiplePackageNos' v-if="oneByOneForLabel"
                                       :label="$t('fba.boxNoOrMarkNo')" min-width='390' header-align='center' align='center'>
                        <template slot-scope="scope" slot="header">
                          <el-popover
                            placement="top-start"
                            trigger="hover">
                            <div class="popover-content" v-html="$t('fba.inputPackageNoForWholePackageInPlaceholder')"></div>
                            <el-button type="text"  slot="reference">
                              <label style="color: red"> * </label>{{$t('fba.boxNoOrMarkNo')}}
                              <i class="el-icon-info el-icon--right primary"></i>
                            </el-button>
                          </el-popover>
                        </template>
                        <template slot-scope="scope">
                          <el-form-item :prop="'subWaybillList.' + scope.$index + '.multiplePackageNos'" :rules='boxRule.packageNo'>
                            <el-input v-model="scope.row.multiplePackageNos" :ref="'subWaybillList.' + scope.$index + '.multiplePackageNos'"
                                      @keydown.enter.native="handleBoxDataEnterKey(scope.$index, 'multiplePackageNos')" @change="packageNoChange($event, scope.$index)"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column :label="$t('handle')"  min-width='80' header-align='center' align='center' >
                        <template slot-scope='scope'>
                          <el-link :underline='false' @click='copyRow(scope.row)'>{{ $t('copy') }}</el-link>
                          <popconfirm i18nOperateValue="delete" @clickHandle='deleteRow(scope)'>{{ $t('delete') }}</popconfirm>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button class='el-icon-plus width100 margin_top10' size='mini' type='primary' plain @click='addRow'>{{ $t('add') }}</el-button>
                </div>
            </el-row>
        </el-form>
      <div id="cs_FormFooter" class="el-form-footer" slot="footer">
        <el-input style="width:400px; margin-right:30px;" v-model="dataForm.inRemark" :placeholder="$t('comAllInWarehouse.inRemark')" ></el-input>
        <el-button type='primary' :loading='submitLoading' @click='handleSubmit()'>{{ $t('confirmInWarehouse') }}</el-button>
      </div>
  </div>
    <batchBoxDialog ref="batchBoxDialog" @callback="batchBoxConfirmHandle"></batchBoxDialog>
  </div>
</template>

<script>
import batchBoxDialog from './com-all-in-warehouse-batch-box.vue'
import areaBox from '@/components/areaBox'
import listPage from '@/mixins/listPage'
import { mul, add, div } from '@/utils/tools'
import { numberFormat, cutOutNum } from '@/filters/filters'
import { isDecimal3, isDecimal1, isOverLength, isPlusFloat, isPlusInteger2 } from '@/utils/validate'
import debounce from 'lodash/debounce'
export default {
  mixins: [listPage],
  data () {
    return {
      batchBoxVisible: false,
      visible: false,
      autoGenerateBox: false,
      submitLoading: false,
      customerOrderNo: null,
      packageInfoShow: false,
      togetherWeight: false,
      oneByOneForLabel: false,
      rowElementNameArray: [],
      sumWeight: 0.0,
      unitWeight: 0.0,
      sumVolume: 0.0,
      sumQty: 0,
      dataForm: {
        inTime: '',
        no: '',
        scanNoType: 10,
        totalWeight: '',
        boxNoPrefix: '',
        packageDeliveryNo: '',
        packageLength: '',
        packageHeight: '',
        packageWeight: '',
        packageWidth: '',
        cno: '',
        forecastBoxQty: 0,
        forecastWeight: '',
        customerName: '',
        standardUnit: '',
        customerId: ''
      },
      boxDataForm: {
        inTime: '',
        no: '',
        waybillId: '',
        orderId: '',
        totalWeight: '',
        wholePackageIn: '',
        subWaybillList: []
      },
      scanNoTypeList: [ { 'value': 10, 'name': '客户单号' }, { 'value': 20, 'name': '运单号' }, { 'value': 30, 'name': '派送单号' }, { 'value': 40, 'name': '运单编号(ID)' }, { 'value': 50, 'name': '子派送单号' }, { 'value': 60, 'name': 'FBA唛头号/箱号' } ],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 7 * 24 * 60 * 60 * 1000
        }
      },
      customerList: []
    }
  },
  watch: {
    sumQty: {
      handler (value, oldName) {
        if (value !== undefined && value !== null && value !== '') {
          if (this.dataForm.forecastBoxQty - this.sumQty < 0) {
            this.$message.error('实际入库箱数 > 预报箱数')
          }
        } else {
          this.sumQty = 0
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: false
    },
    togetherWeight: {
      handler (value, oldName) {
        if (this.togetherWeight && this.dataForm.no !== '') {
          this.$nextTick(() => {
            this.$refs.dataFormTotalWeight.focus()
          })
        } else if (this.dataForm.no === '') {
          this.$refs.dataFormNo.focus()
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: false
    }
  },
  computed: {
    boxRule () {
      const isPositiveInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的整数'))
        }
        callback()
      }
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isFloat1 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal1(value)) {
          return callback(new Error('仅保留1位小数'))
        }
        callback()
      }
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('仅保留3位小数'))
        }
        callback()
      }
      const repeatBoxNo = (rule, value, callback) => {
        if (value && this.boxDataForm.subWaybillList && this.boxDataForm.subWaybillList.length > 0) {
          let packageNoArr = this.boxDataForm.subWaybillList.map(item => {
            if (item.packageNo === value) {
              return item.packageNo
            }
          }).filter(item => typeof item !== 'undefined')
          let packageNoSet = new Set(this.boxDataForm.subWaybillList.map(item => {
            if (item.multiplePackageNos === value) {
              return item.multiplePackageNos
            }
          }).filter(item => typeof item !== 'undefined')
          )
          if (packageNoSet.size !== packageNoArr.length) {
            return callback(new Error('箱号/FBA唛头不能重复'))
          }
        }
        callback()
      }
      return {
        packageQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isPositiveInteger, trigger: 'blur' }
        ],
        multiplePackageNos: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' },
          { validator: repeatBoxNo, trigger: 'blur' }
        ],
        lengthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        widthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        heightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        weightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat3, trigger: 'blur' }
        ]
      }
    },
    dataRule () {
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('仅保留3位小数'))
        }
        callback()
      }
      return {
        no: [
          { required: true, message: this.$t('validate.required'), trigger: 'input' }
        ],
        totalWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat3, trigger: 'blur' }
        ]
      }
    }
  },
  activated () {
    this.$nextTick(() => {
      if (this.dataForm.no === '') {
        this.$refs.dataFormNo.focus()
      }
    })
  },
  methods: {
    // FBA唛头号/箱号将回车、空格、中文逗号替换为英文逗号
    packageNoChange (event, rowIndex) {
      let packageNos = this.boxDataForm.subWaybillList[rowIndex].multiplePackageNos
      packageNos = packageNos.replace(/[\r\n|\t|\s]/g, ',')
        .replace(/(，)+/g, ',')
        .replace(/(,)+/g, ',')
        .replace(/( )+/g, ',')
      packageNos = packageNos.trim()
      if (packageNos.substr(0, 1) === ',') {
        packageNos = packageNos.substr(1, packageNos.length)
      }
      this.boxDataForm.subWaybillList[rowIndex].multiplePackageNos = packageNos
    },
    // 单号类型改变
    changeNoType () {
      this.$refs.dataFormNo.select()
      this.$refs.dataFormNo.focus()
    },
    // 单号改变
    changeNo () {
      if (!this.dataForm.no) {
        // 单号为空，重置所有的数据
        this.resetAllData()
        return false
      }
      this.$http.post('ws/cominwarehousejob/checkWaybillForWholePackage', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          this.resetAllData(false)
          this.packageInfoShow = false
          this.$message.error(res.msg)
          this.$refs.dataFormNo.select()
        }
        this.boxDataForm.waybillId = res.data.waybillId
        this.boxDataForm.orderId = res.data.orderId
        this.dataForm.cno = res.data.masterCustomerOrderNo
        this.dataForm.forecastBoxQty = res.data.packageQty
        this.dataForm.forecastWeight = res.data.masterForecastWeight
        this.dataForm.customerName = res.data.customerName
        console.log('res.data.standardUnit', res.data.standardUnit)
        console.log('res.data', res.data)
        this.dataForm.standardUnit = res.data.standardUnit === 0 ? 'KG' : 'LB'
        // 展示录入箱信息
        this.packageInfoShow = true
        // 如果是整票入库,则焦点移到 整票重量
        if (this.togetherWeight) {
          this.$refs.dataFormTotalWeight.focus()
          this.$refs.dataFormTotalWeight.select()
        } else {
          this.newFirstRowAndFocus()
        }
      }).catch(() => {
      })
    },
    /**
     * 处理 整票重量回车事件
     */
    handleTogetherWeightEnterKey () {
      if (this.boxDataForm.waybillId === null || this.boxDataForm.waybillId === '' || this.boxDataForm.waybillId === 'undefined' || this.boxDataForm.waybillId === undefined) {
        // 如果单号为空，则焦点移到单号的输入框
        console.log('this.dataForm.no', this.dataForm.no)
        if (this.dataForm.no === '') {
          this.$refs.dataFormNo.focus()
        } else {
          // 如果输入了单号，但还未请求后台数据
          this.changeNo()
        }
      } else {
        this.newFirstRowAndFocus()
      }
    },
    /**
     * 新增第一行，并使第一行的第一个元素获得焦点
     */
    newFirstRowAndFocus () {
      if (this.boxDataForm.subWaybillList == null || this.boxDataForm.subWaybillList.length === 0) {
        this.addRow()
      }
      // 箱信息的第一行的 lengthD元素获得焦点
      this.boxDataFocus(0, 'lengthD')
    },
    /**
     *箱信息中的元素回车键处理
     * @param rowIndex 行号
     * @param  focusElementName 当前元素的名称
     */
    handleBoxDataEnterKey (rowIndex, focusElementName) {
      // 获取表格的元素集合
      let nextFocusElementName = this.getNextRowElementName(focusElementName)
      // 如果当前获得焦点的元素是最后一个元素，则新增一行,并使下一个行的第一个元素lengthD获得焦点
      if (!nextFocusElementName) {
        // 如果实际入库箱数 < 预报箱数并且当前焦点在最后一行，则新增一行
        let subInSubWaybillLength = this.boxDataForm.subWaybillList.length
        let surplusBoxQty = this.dataForm.forecastBoxQty - this.sumQty
        if (surplusBoxQty > 0 && rowIndex === (subInSubWaybillLength - 1)) {
          this.addRow()
          // 下一个行的第一个元素lengthD获得焦点
          this.boxDataFocus(rowIndex + 1, 'lengthD')
        } else if (rowIndex < subInSubWaybillLength - 1) {
          this.boxDataFocus(rowIndex + 1, 'lengthD')
        }
      } else {
        // 使当前行的下一个元素获得焦点
        this.boxDataFocus(rowIndex, nextFocusElementName)
      }
    },
    /**
     * 箱信息得到焦点
     * param rowIndex 行号
     * inputElementName 行中对应input的ref名称
     */
    boxDataFocus (rowIndex, inputElementName) {
      let elementRefName = 'subWaybillList.' + rowIndex + '.' + inputElementName
      this.$nextTick(() => {
        this.$refs[elementRefName].focus()
        this.$refs[elementRefName].select()
      })
    },
    /**
     * 获取表格元素中当前获得焦点元素的下一个元素的名称
     * @param focusElementName 当前获得焦点的表格某一行的元素
     * @return 如果当前元素是最后一个元素，返回 null，否则返回下一个元素名
     */
    getNextRowElementName (focusElementName) {
      // 获取表格的元素集合
      this.rowElementNameArray = this.setRowElementNameArray()
      let length = this.rowElementNameArray.length
      // 当前获得焦点的元素在表格元素集合中的位置
      let focusElementIndex = this.rowElementNameArray.indexOf(focusElementName)
      // 如果是最后最后一个元素, 返回 null
      if (focusElementIndex === length - 1) {
        return null
      } else {
        // 返回下一个元素
        return this.rowElementNameArray[focusElementIndex + 1]
      }
    },
    /**
     * 设置箱信息的元素集合
     * @returns {string[]}
     */
    setRowElementNameArray () {
      let elementNameArr = ['lengthD', 'widthD', 'heightD', 'weightD', 'packageQty', 'multiplePackageNos']
      if (this.togetherWeight) {
        if (this.oneByOneForLabel) {
          // 删除weightD
          elementNameArr.splice(3, 1)
          return elementNameArr
        } else {
          // 删除weightD
          elementNameArr.splice(3, 1)
          // 删除multiplePackageNos
          elementNameArr.splice(4, 1)
          return elementNameArr
        }
      } else {
        if (this.oneByOneForLabel) {
          return elementNameArr
        } else {
          // 删除multiplePackageNos
          elementNameArr.splice(5, 1)
          return elementNameArr
        }
      }
    },
    addRow () {
      let obj = {
        multiplePackageNos: '',
        lengthD: '',
        widthD: '',
        heightD: '',
        packageQty: 1,
        weightD: '',
        lineVolume: 0.0,
        lineWeight: 0.0
      }
      this.boxDataForm.subWaybillList.push(obj)
      this.setTotalQty()
    },
    copyRow (row) {
      let obj = {
        ...row
      }
      // 不复制箱号
      obj.multiplePackageNos = ''
      this.boxDataForm.subWaybillList.push(obj)
      this.setTotalQty()
    },
    deleteRow (scope) {
      this.boxDataForm.subWaybillList.splice(scope.$index, 1)
      this.setTotalQty()
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    // 整票称重复选框改变
    togetherWeightChange () {
      this.dataForm.totalWeight = ''
      this.sumWeight = 0.0
    },
    // 根据数量计算体积和重量
    setTotalQty () {
      let totalQty = 0
      this.boxDataForm.subWaybillList.forEach(item => {
        if (item.packageQty !== null && item.packageQty !== undefined) {
          totalQty = add(totalQty, item.packageQty)
        }
      })
      this.sumQty = totalQty
      this.sumRowVol()
      this.sumRowWeight()
      this.setUnitWeight()
    },
    // 汇总体积
    sumRowVol () {
      this.boxDataForm.subWaybillList.forEach(row => {
        if (row.lengthD && row.widthD && row.heightD && row.packageQty) {
          // 转成立方米显示
          let lineVolume = div(mul(mul(mul(row.lengthD, row.widthD), row.heightD), row.packageQty), 1000000)
          row.lineVolume = Math.round(lineVolume * 10000) / 10000
        } else {
          return false
        }
      })
      this.setTotalVol()
    },
    // 设置单箱平均重量
    setUnitWeight () {
      if (this.sumQty > 0) {
        let unitWeight = div(this.sumWeight, this.sumQty)
        this.unitWeight = cutOutNum(unitWeight, 3)
      } else {
        this.unitWeight = 0.0
      }
    },
    // 汇总重量
    sumRowWeight () {
      this.boxDataForm.subWaybillList.forEach(row => {
        if (row.weightD && row.packageQty) {
          let lineWeight = mul(row.weightD, row.packageQty)
          row.lineWeight = Math.round(lineWeight * 10000) / 10000
        } else {
          return false
        }
      })
      this.setTotalWeight()
    },
    setTotalVol () {
      let totalVolume = 0.0
      this.boxDataForm.subWaybillList.forEach(item => {
        if (item.lineVolume !== null && item.lineVolume !== undefined) {
          totalVolume = add(totalVolume, item.lineVolume)
        }
      })
      this.sumVolume = cutOutNum(totalVolume, 3)
    },
    setTotalWeight () {
      // 如果是整票称重
      if (this.togetherWeight) {
        this.sumWeight = this.dataForm.totalWeight
        this.setUnitWeight()
      } else {
        let totalWeight = 0.0
        this.boxDataForm.subWaybillList.forEach(item => {
          if (item.lineWeight !== null && item.lineWeight !== undefined) {
            totalWeight = add(totalWeight, item.lineWeight)
          }
        })
        this.sumWeight = cutOutNum(totalWeight, 3)
        this.setUnitWeight()
      }
    },
    /**
     * 重置所有的数据
     * @param resetTotalWeight 重置整票重量。false：不重置/ true: 重置
     *
     */
    resetAllData (resetTotalWeight) {
      let totalWeight = this.dataForm.totalWeight
      let formNo = this.dataForm.no
      this.$refs['boxDataForm'].resetFields()
      this.$refs['dataForm'].resetFields()
      this.boxDataForm.waybillId = ''
      if (!resetTotalWeight) {
        this.dataForm.totalWeight = totalWeight
      }
      this.dataForm.no = formNo
      this.boxDataForm.subWaybillList = []
      this.packageInfoShow = false
      this.sumQty = 0
      this.sumVolume = 0
      this.sumWeight = 0
      this.unitWeight = 0
      this.$refs.dataFormNo.select()
      this.$refs.dataFormNo.focus()
    },
    handleSubmit: debounce(function () {
      this.boxDataForm.inTime = this.dataForm.inTime
      this.boxDataForm.inRemark = this.dataForm.inRemark
      this.boxDataForm.totalWeight = this.dataForm.totalWeight
      this.boxDataForm.no = this.dataForm.no
      this.boxDataForm.togetherWeight = this.togetherWeight
      this.boxDataForm.oneByOneForLabel = this.oneByOneForLabel
      // 整票入库
      this.boxDataForm.wholePackageIn = true
      let canSubmit = true

      if (this.boxDataForm.subWaybillList === null || this.boxDataForm.subWaybillList.length === 0) {
        canSubmit = false
        return this.$message.error(this.$t('fba.boxIsEmpty'))
      }

      this.$refs['boxDataForm'].validate((valid) => {
        if (!valid) {
          canSubmit = false
          return false
        }
      })
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          canSubmit = false
          return false
        }
      })
      if (canSubmit) {
        this.submitLoading = true
        this.$http.post('/ws/cominwarehousejob/inWarehouseForWholePackage', this.boxDataForm).then(({ data: res }) => {
          this.submitLoading = false
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          } else {
            this.resetAllData(true)
            this.$nextTick(() => {
              this.$refs.dataFormNo.select()
            })
            return this.$message.success(this.$t('prompt.success'))
          }
        }).catch(() => {
          this.submitLoading = false
          this.dataForm.no = ''
          this.$refs.dataFormNo.select()
        })
      }
    }),
    closeHandle () {
      this.visible = false
      this.$emit('closeInputBoxInfo')
    },
    openBatchBoxHandle () {
      // this.batchBoxVisible = true
      this.$nextTick(() => {
        this.$refs.batchBoxDialog.init(this.togetherWeight, this.oneByOneForLabel)
      })
    },
    batchBoxConfirmHandle (boxList) {
      this.boxDataForm.subWaybillList = boxList
      this.setTotalQty()
      this.batchBoxVisible = false
    }
  },
  filters: {
    numberFormat
  },
  components: {
    areaBox,
    batchBoxDialog
  }
}
</script>
<style lang='scss' scoped>
#boxDataFormId{
  ::v-deep .el-form-item {
    margin-bottom: 0!important;
  }
}
</style>
