<template>
  <el-dialog :title="!dataForm.id ? $t('add') : $t('update')" :visible.sync="dialogVisible"
             :close-on-click-modal="false" :close-on-press-escape="false"  :lock-scroll="true" width="600px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="108px">
      <el-row :gutter="20" type="flex">
        <el-col :span="24">
          <el-form-item  :label="$t('wsComBatchOrder.batchNo')" prop="batchNo">
            <el-input v-model="dataForm.batchNo" clearable ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" type="flex">
        <el-col :span="24">
          <el-form-item  :label="$t('wsComBatchOrder.customerName')" prop="customerId">
            <el-select v-model="dataForm.customerId" clearable :placeholder="$t('twoCharToSelectForCustomer')"
                       :loading="loading"  filterable  remote reserve-keyword :remote-method="getCustomerByCodeOrName" :disabled="canEditCustomerOrPostcode">
              <el-option v-for="item in customerList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" type="flex">
          <el-col :span="24">
            <el-form-item  :label="$t('wsComBatchOrder.logisticsChannelCode')" prop="logisticsChannelCode">
              <el-select filterable clearable v-model="dataForm.logisticsChannelCode" :disabled="canEditCustomerOrPostcode">
                <el-option v-for="(item, index) in logisticsChannelByParamsList" :key="index" :label="item.name" :value="item.code"/>
              </el-select>
            </el-form-item>
          </el-col>
      </el-row>
      <el-row :gutter="20" type="flex">
        <el-col :span="24">
          <el-form-item  :label="$t('wsComBatchOrder.originPostcode')" prop="originPostcode">
            <el-input v-model="dataForm.originPostcode" clearable  :disabled="canEditCustomerOrPostcode"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancelFn">{{this.$t('cancel')}}</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveOrder">{{ $t('confirm') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import {
  isOverLength,
  letterAndNumberLine
} from '@/utils/validate'
import comMixins from '@/mixins/comMixins'

export default {
  mixins: [ comMixins ],
  data () {
    return {
      dialogVisible: false,
      dataForm: {
        id: '',
        batchNo: '',
        parcelQty: 0,
        providerId: '',
        customerId: '',
        customerName: '',
        originPostcode: '',
        logisticsChannelCode: ''
      },
      canEditCustomerOrPostcode: false,
      saveLoading: false,
      loading: false,
      customerList: [],
      logisticsChannelByParamsList: []
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
  },
  methods: {
    init () {
      this.dialogVisible = true
      this.customerList = [{ id: this.dataForm.customerId, name: this.dataForm.customerName }]
      this.$nextTick(() => {
      })
    },
    async getBaseData () {
      this.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByParamsList, { autoFilter: true, isEnable: true }).catch(() => {})
    },
    cancelFn () {
      this.$refs.dataForm.resetFields()
      this.dialogVisible = false
    },
    saveOrder () {
      this.saveLoading = true
      console.log('id', this.dataForm.id)
      this.$http[!this.dataForm.id ? 'post' : 'put']('/ws/combatchorder', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.$emit('addDialogAfterHandle')
        this.cancelFn()
      }).catch(() => {
      }).finally(() => { this.saveLoading = false })
    }
  },
  computed: {
    dataRule () {
      const isLength32 = (rule, value, callback) => {
        if (value && !isOverLength(value, 32)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 32 })))
        }
        callback()
      }
      const onlyLetterAndNumberLineValidator = (rule, value, callback) => {
        if (value && !letterAndNumberLine(value)) {
          return callback(new Error('只能用字母，数字，中划线和下划线组合'))
        }
        callback()
      }
      return {
        logisticsChannelCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        originPostcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        batchNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' },
          { validator: onlyLetterAndNumberLineValidator, trigger: 'blur' }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.selectChannelClass{
  .form-card{
    padding: 15px;
  }
  .el-checkbox__label{
    font-size: 12px;
  }
  .el-form-item__label,
  .el-checkbox,
  .el-radio{
    color: #000!important;
  }
  .el-table .success-row {
    background: #09ff00;
  }
}
</style>
