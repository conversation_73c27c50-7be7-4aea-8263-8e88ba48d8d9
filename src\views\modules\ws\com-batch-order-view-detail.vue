<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('wsComBatchOrder.providerId')">
              <span v-text="formatterValue(batchDataForm.providerId,'provider')"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComBatchOrder.customerName')">
              <span v-text="batchDataForm.customerName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComBatchOrder.logisticsChannelCode')">
              <span v-text="formatterValue(batchDataForm.logisticsChannelCode,'channel')"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComBatchOrder.batchNo')">
              <span v-text="batchDataForm.batchNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComBatchOrder.parcelQty')">
              <span v-text="batchDataForm.parcelQty"></span>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item :label="$t('wsComBatchOrder.batchFileUrl')">
              <span v-text="batchDataForm.batchFileUrl"></span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('wsComBatchOrder.errorMessage')">
              <span v-text="batchDataForm.errorMessage"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="mod-co__inorder flex_wrap" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchList()" label-width="120px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('wsComBatchItem.deliveryNo')" prop="deliveryNo">
                    <el-input v-model="dataForm.deliveryNo"  clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="searchList()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import comMixins from '@/mixins/comMixins'
import baseData from '@/api/baseData'
import baseDataApi from '@/api'
// table 自定义显示
import tableSet from '@/components/tableSet'
import {
  formatterCodeName,
  formatterCodeNativeName, formatterName,
  formatterType,
  gtmToLtm,
  timestampFormat
} from '@/filters/filters'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      batchDataForm: {
        id: '',
        providerId: '',
        batchNo: '',
        parcelQty: '',
        batchFileUrl: '',
        errorMessage: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        companyId: ''
      },
      dataForm: {
        batchId: null,
        batchNo: null
      },
      loading: false,
      logisticsChannelByParamsList: [],
      logisticsProductByParamsList: [],
      providerList: [],
      tableColumns: [
        { type: '', width: '150', prop: 'customerId', label: this.$t('wsComBatchItem.customerId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'customerName', label: this.$t('wsComBatchItem.customerName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'batchId', label: this.$t('wsComBatchItem.batchId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'batchNo', label: this.$t('wsComBatchItem.batchNo'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '200', prop: 'deliveryNo', label: this.$t('wsComBatchItem.deliveryNo'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerOrderNo', label: this.$t('wsComBatchItem.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('wsComBatchItem.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsProductCode', label: this.$t('wsComBatchItem.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsChannelCode', label: this.$t('wsComBatchItem.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ws/combatchitem/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    async getBaseData () {
      this.providerList = await baseData(baseDataApi.providerList, { status: 11 }).catch(() => {})
      this.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByParamsList, { autoFilter: true }).catch(() => {})
      // 所有物流产品 包含未启用
      this.logisticsProductByParamsList = await baseData(baseDataApi.listAllByCurrent).catch(() => {})
    },
    formatterValue (value, field) {
      if (value !== undefined && value !== null && value !== '') {
        switch (field) {
          case 'channel':
            value = formatterCodeName(value, this.logisticsChannelByParamsList)
            break
          case 'provider':
            value = formatterName(value, this.providerList)
            break
        }
      }
      return value
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'logisticsProductCode':
          value = formatterCodeNativeName(scope.row.logisticsProductCode, this.logisticsProductByParamsList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeNativeName(scope.row.logisticsChannelCode, this.logisticsChannelByParamsList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    searchList () {
      this.getDataList()
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.batchDataForm.id) {
          this.getInfo()
          this.getDataList()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/ws/combatchorder/${this.batchDataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.batchDataForm = {
          ...this.batchDataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  components: {
    tableSet
  }
}
</script>
