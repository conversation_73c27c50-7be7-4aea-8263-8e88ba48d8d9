<template>
  <div>
    <el-dialog :title="$t('title.changeWarehouse')" :visible.sync="visible" width="40%" style="min-width: 300px;" top="10px" :append-to-body="false"
        :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true" :before-close="backFunc" class="location_model">
      <el-form ref="dataForm" :model="dataForm" :rules="dataRule" @submit.native.prevent label-width="100px">
        <el-row :gutter="20" type="flex" justify="center" style="padding-top: 10px">
          <el-col :span="20">
            <el-form-item :label="$t('wsComWaybill.warehouseId')" prop="warehouseId">
                <el-select v-model="dataForm.warehouseId" filterable clearable>
                  <el-option v-for="item in baseData.warehouseIdList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('label.processRemark')" prop="processRemark">
                <el-input type="textarea" v-model="dataForm.processRemark" :placeholder="$t('label.processRemark')"></el-input>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" :loading="loading" @click="changewarehouseHandle">{{$t('btn.submit')}}</el-button>
                <el-button @click="backFunc">{{ $t('cancel') }}</el-button>
              </el-form-item>
            </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <progress-dialog ref="progressDialog" :title="$t('wsComWaybill.changeWarehouse')" no="waybillNo" noLabel="运单号" @callback="backFunc"></progress-dialog>
  </div>
</template>

<script>
import ProgressDialog from '@/components/progress-dialog'
import comMixins from '@/mixins/comMixins'
import dictTypeMixins from '@/mixins/dictTypeMixins'

export default {
  mixins: [comMixins, dictTypeMixins],
  data () {
    return {
      dataForm: {
        reforecast: 0,
        waybillNos: '',
        warehouseId: '',
        processRemark: ''
      },
      isSuccess: true,
      dataList: [],
      loading: false,
      visible: false,
      baseData: {
        warehouseIdList: []
      }
    }
  },
  created () {
    this.getUserInfo()
  },
  methods: {
    init (dataList) {
      this.loading = false
      this.$nextTick(() => {
        this.dataList = dataList
        this.resetFormValues()
        console.log('this.dataList', this.dataList)
      })
    },
    resetFormValues () {
      this.dataForm = {
        reforecast: 0,
        waybillNos: [],
        warehouseId: '',
        processRemark: ''
      }
    },
    // 获取当前管理员信息
    async getUserInfo () {
      await this.$http.get('/sys/user/info').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.baseData.warehouseIdList = res.data.operateWarehouseList
      })
    },
    // 变更仓库
    changewarehouseHandle () {
      if (!this.dataForm.warehouseId) {
        return this.$message.error('请选择要变更的仓库')
      }
      this.$refs.progressDialog.init(this.dataList.length)
      this.$nextTick(() => {
        // 10个并发请求
        this.$refs.progressDialog.concurrencyRequest(this.dataList, 10, this.changeWarehouseRequest)
      })
    },
    // 单笔请求
    changeWarehouseRequest (row) {
      this.loading = true
      let reqData = {
        warehouseId: this.dataForm.warehouseId,
        processRemark: this.dataForm.processRemark,
        waybillNos: [row.waybillNo]
      }
      this.$http.post('/ws/comexceptionbill/changeWarehouse', reqData).then(({ data: res }) => {
        if (res.code !== 0) {
          // 请求失败通知
          this.$refs.progressDialog.fail(row.waybillNo, res.msg)
        } else {
          // 请求成功通知
          this.$refs.progressDialog.success()
        }
      }).catch(() => {
        // 请求失败通知
        this.$refs.progressDialog.fail(row.waybillNo, '请求超时')
      })
    },
    backFunc () {
      this.visible = false
      this.$emit('callback')
    }
  },
  computed: {
    dataRule () {
      return {
        warehouseId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    ProgressDialog
  }
}
</script>
<style lang="scss" scoped>
</style>
