$().ready( function() {
    //国家select2下拉列
    $(".select2-country").each(function(){
        selectCountry($(this));
    });
});

/**
 * 客户下拉框
 * @param $idInput 客户ID的输入框
 */
function selectAllConsignor($idInput){
    if( !$idInput ) return;
    var callbackMain = arguments[1];
    $idInput.select2({
        placeholder: "请选择客户",//占位符
        minimumInputLength: 2,//最小输入字符的个数
        allowClear:true,//允许清除数据
        //远程请求
        ajax: {
            url: "sys_consignor!findAllConsignor.action",
            dataType: 'json',
            type:"post", //可避免中文乱码
            data: function (term, page) {//请求参数
                return {
                    "sysConsignor.code": term,
                    "sysConsignor.name": term
                };
            },
            results: function (data, page) {//响应返回的数据
                return { results: data};
            }
        },
        //初始化时设置选择的项
        initSelection: function(element, callback) {
            var id=$(element).val();
            if (id!=="") {
                $.ajax("sys_consignor!findConsignor.action", {
                    data: {
                        "sysConsignor.id": id
                    },
                    dataType: "json"
                }).done(function(data) {
                    callback(data[0]);
                    if(callbackMain) callbackMain(data[0]);
                });
            }
        },
        //已选择项的显示格式
        formatSelection: function(obj){
            if(callbackMain) callbackMain(obj);
            return obj.name;
        },
        //结果列表的显示格式
        formatResult: function(obj){
            if(obj.status == 0){
                return "<font color='red'>"+obj.name+"("+obj.code+" 未启用)</font>";
            }
            return obj.name+"("+obj.code+")";
        }
    });
}

/**
 * 客户下拉框
 * @param $idInput 客户ID的输入框
 */
function selectConsignor($idInput){
    if( !$idInput ) return;
    var callbackMain = arguments[1];
    $idInput.select2({
        placeholder: "请选择客户",//占位符
        minimumInputLength: 2,//最小输入字符的个数
        allowClear:true,//允许清除数据
        //远程请求
        ajax: {
            url: "sys_consignor!findConsignor.action",
            dataType: 'json',
            type:"post", //可避免中文乱码
            data: function (term, page) {//请求参数
                return {
                    "sysConsignor.code": term,
                    "sysConsignor.name": term
                };
            },
            results: function (data, page) {//响应返回的数据
                return { results: data};
            }
        },
        //初始化时设置选择的项
        initSelection: function(element, callback) {
            var id=$(element).val();
            if (id!=="") {
                $.ajax("sys_consignor!findConsignor.action", {
                    data: {
                        "sysConsignor.id": id
                    },
                    dataType: "json"
                }).done(function(data) {
                    callback(data[0]);
                    if(callbackMain) callbackMain(data[0]);
                });
            }
        },
        //已选择项的显示格式
        formatSelection: function(obj){
            if(callbackMain) callbackMain(obj);
            return obj.name;
        },
        //结果列表的显示格式
        formatResult: function(obj){return obj.name+"("+obj.code+")";}
    });
}


/**
 * 客户多选框
 * @param $idInput 客户ID的输入框
 */
function selectConsignors($idInput){
    if( !$idInput ) return;
    var callbackMain = arguments[1];
    $idInput.select2({
        placeholder: "请选择客户",//占位符
        multiple:true,
        minimumInputLength: 2,//最小输入字符的个数
        allowClear:true,//允许清除数据
        //远程请求
        ajax: {
            url: "sys_consignor!findConsignor.action",
            dataType: 'json',
            type:"post", //可避免中文乱码
            data: function (term, page) {//请求参数
                return {
                    "sysConsignor.code": term,
                    "sysConsignor.name": term
                };
            },
            results: function (data, page) {//响应返回的数据
                return { results: data};
            }
        },
        //初始化时设置选择的项
        initSelection: function(element, callback) {
            var id=$(element).val();
            if (id!=="") {
                $.ajax("sys_consignor!findConsignor.action", {
                    data: {
                        "sysConsignor.id": id
                    },
                    dataType: "json"
                }).done(function(data) {
                    callback(data[0]);
                    if(callbackMain) callbackMain(obj);
                });
            }
        },
        //已选择项的显示格式
        formatSelection: function(obj){
            if(callbackMain) callbackMain(obj);
            return obj.name;
        },
        //结果列表的显示格式
        formatResult: function(obj){return obj.name;}
    });
}

/**
 * 代理下拉框
 * @param $idInput 代理ID的输入框
 */
function selectSp($idInput){
    if( !$idInput ) return;
    var callbackMain = arguments[1];
    $idInput.select2({
        placeholder: "请选择代理",//占位符
        minimumInputLength: 2,//最小输入字符的个数
        allowClear:true,//允许清除数据
        //远程请求
        ajax: {
            url: "sys_sp!findSp.action",
            dataType: 'json',
            type:"post", //可避免中文乱码
            data: function (term, page) {//请求参数
                return {
                    "sysSp.code": term,
                    "sysSp.name": term
                };
            },
            results: function (data, page) {//响应返回的数据
                return { results: data};
            }
        },
        //初始化时设置选择的项
        initSelection: function(element, callback) {
            var id=$(element).val();
            if (id!=="") {
                $.ajax("sys_sp!findSp.action", {
                    data: {
                        "sysSp.id": id
                    },
                    dataType: "json"
                }).done(function(data) {
                    callback(data[0]);
                    if(callbackMain) callbackMain(obj);
                });
            }
        },
        //已选择项的显示格式
        formatSelection: function(obj){
            if(callbackMain) callbackMain(obj);
            return obj.name;
        },
        //结果列表的显示格式
        formatResult: function(obj){return obj.name+"("+obj.code+")";}
    });
}


/**
 * 代理多选框
 * @param $idInput 代理ID的输入框
 */
function selectSps($idInput){
    if( !$idInput ) return;
    var callbackMain = arguments[1];
    $idInput.select2({
        placeholder: "请选择代理",//占位符
        multiple:true,
        minimumInputLength: 2,//最小输入字符的个数
        allowClear:true,//允许清除数据
        //远程请求
        ajax: {
            url: "sys_sp!findSp.action",
            dataType: 'json',
            type:"post", //可避免中文乱码
            data: function (term, page) {//请求参数
                return {
                    "sysSp.code": term,
                    "sysSp.name": term
                };
            },
            results: function (data, page) {//响应返回的数据
                return { results: data};
            }
        },
        //初始化时设置选择的项
        initSelection: function(element, callback) {
            var id=$(element).val();
            if (id!=="") {
                $.ajax("sys_sp!findSp.action", {
                    data: {
                        "sysSp.id": id
                    },
                    dataType: "json"
                }).done(function(data) {
                    callback(data[0]);
                    if(callbackMain) callbackMain(obj);
                });
            }
        },
        //已选择项的显示格式
        formatSelection: function(obj){
            if(callbackMain) callbackMain(obj);
            return obj.name;
        },
        //结果列表的显示格式
        formatResult: function(obj){return obj.name;}
    });
}

/**
 * 国家下拉框
 * @param $idInput 国家的输入框
 */
function selectCountry($idInput){
    if( !$idInput ) return;
    var callbackMain = arguments[1];
    $idInput.select2({
        placeholder: "请选择国家",//占位符
        minimumInputLength: 2,//最小输入字符的个数
        allowClear:true,//允许清除数据
        //远程请求
        ajax: {
            url: "sys_region!findCountry.action",
            dataType: 'json',
            type:"post", //可避免中文乱码
            data: function (term, page) {//请求参数
                return {
                    "sysRegion.path": term,
                    "sysRegion.name": term,
                    "sysRegion.ename": term
                };
            },
            results: function (data, page) {//响应返回的数据
                var arr = [];
                if( data != null && data.length > 0 ){
                    for(var i = 0; i < data.length; i++){
                        arr[i] = new Object();
                        arr[i].id = data[i].regionId;
                        arr[i].name = data[i].name;
                        arr[i].ename = data[i].ename;
                    }
                }
                return { results: arr};
            }
        },
        //初始化时设置选择的项
        initSelection: function(element, callback) {
            var regionId=$(element).val();
            if (regionId!=="") {
                $.ajax("sys_region!findCountry.action", {
                    data: {
                        "sysRegion.regionId": regionId
                    },
                    dataType: "json"
                }).done(function(data) {
                    var res = {};
                    if( data != null && data.length > 0 ){
                        res = {id:data[0].regionId,name:data[0].name,ename:data[0].ename};
                    }
                    callback(res);
                    if(callbackMain) callbackMain(res);
                });
            }
        },
        //已选择项的显示格式
        formatSelection: function(obj){
            if(callbackMain) callbackMain(obj);
            return obj.id + " " + obj.name;
        },
        //结果列表的显示格式
        formatResult: function(obj){return obj.id + " " + obj.name + " " + obj.ename;}
    });
}

/*获取location*/
function getLocation(){
    var strUrl=window.location.href;
    var arrUrl=strUrl.split("/");
    var strPage=arrUrl[arrUrl.length-1];
    var arrPage=strPage.split(".");
    var strLoc=arrPage[0];
    return strLoc;
}
/*获取要访问的action*/
function getAction() {
    var strLoc = getLocation();
    var arrClass=strLoc.split("!");
    var strClass=arrClass[0];
    return strClass;
}
